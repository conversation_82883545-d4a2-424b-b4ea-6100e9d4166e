{"ast": null, "code": "import { onMounted } from 'vue';\nvar __default__ = {\n  name: 'ArchiveVisitBooking'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    onMounted(function () {});\n    var __returned__ = {\n      onMounted\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["onMounted", "__default__", "name"], "sources": ["D:/zy/xm/h5/i西安/appperformduties/src/views/ArchiveVisitBooking/ArchiveVisitBooking.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ArchiveVisitBooking\">\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ArchiveVisitBooking' }\r\n</script>\r\n<script setup>\r\nimport { onMounted } from 'vue'\r\nonMounted(() => {\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.ArchiveVisitBooking {}\r\n</style>\r\n"], "mappings": "AAQA,SAASA,SAAS,QAAQ,KAAK;AAH/B,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAsB,CAAC;;;;;IAI9CF,SAAS,CAAC,YAAM,CAChB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}