{"ast": null, "code": "import { ref, onMounted } from 'vue';\n\n// 响应式数据\n\nvar __default__ = {\n  name: 'ArchiveVisitBooking'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var visitorCount = ref(1);\n    var contactName = ref('');\n    var contactPhone = ref('');\n    var groupName = ref('');\n    var groupAddress = ref('');\n\n    // 日期数据\n    var dateList = ref([{\n      day: '二',\n      date: '9-2',\n      status: '可预约',\n      active: false,\n      disabled: false\n    }, {\n      day: '三',\n      date: '9-3',\n      status: '可预约',\n      active: false,\n      disabled: false\n    }, {\n      day: '四',\n      date: '9-4',\n      status: '可预约',\n      active: false,\n      disabled: false\n    }, {\n      day: '五',\n      date: '9-5',\n      status: '可预约',\n      active: true,\n      disabled: false\n    }, {\n      day: '六',\n      date: '9-6',\n      status: '可预约',\n      active: false,\n      disabled: false\n    }, {\n      day: '日',\n      date: '9-7',\n      status: '可预约',\n      active: false,\n      disabled: false\n    }, {\n      day: '一',\n      date: '9-8',\n      status: '可预约',\n      active: false,\n      disabled: true\n    }]);\n\n    // 时段数据\n    var timeSlots = ref([{\n      id: 1,\n      time: '09:00-11:00',\n      status: '可预约',\n      count: 48,\n      active: false\n    }, {\n      id: 2,\n      time: '13:30-16:30',\n      status: '可预约',\n      count: 160,\n      active: true\n    }]);\n\n    // 方法\n    var selectDate = function selectDate(date) {\n      if (date.disabled) return;\n      dateList.value.forEach(function (item) {\n        return item.active = false;\n      });\n      date.active = true;\n    };\n    var selectTimeSlot = function selectTimeSlot(slot) {\n      timeSlots.value.forEach(function (item) {\n        return item.active = false;\n      });\n      slot.active = true;\n    };\n    var increaseCount = function increaseCount() {\n      visitorCount.value++;\n    };\n    var decreaseCount = function decreaseCount() {\n      if (visitorCount.value > 1) {\n        visitorCount.value--;\n      }\n    };\n    onMounted(function () {\n      // 初始化逻辑\n    });\n    var __returned__ = {\n      visitorCount,\n      contactName,\n      contactPhone,\n      groupName,\n      groupAddress,\n      dateList,\n      timeSlots,\n      selectDate,\n      selectTimeSlot,\n      increaseCount,\n      decreaseCount,\n      ref,\n      onMounted\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "onMounted", "__default__", "name", "visitorCount", "contactName", "contactPhone", "groupName", "groupAddress", "dateList", "day", "date", "status", "active", "disabled", "timeSlots", "id", "time", "count", "selectDate", "value", "for<PERSON>ach", "item", "selectTimeSlot", "slot", "increaseCount", "decreaseCount"], "sources": ["D:/zy/xm/h5/i西安/appperformduties/src/views/ArchiveVisitBooking/ArchiveVisitBooking.vue"], "sourcesContent": ["<template>\r\n  <div class=\"archive-visit-booking\">\r\n    <!-- 选择入馆日期 -->\r\n    <div class=\"section\">\r\n      <div class=\"section-title\">\r\n        <img src=\"../../assets/img/icon_museum.png \" alt=\"\" class=\"icon_museum\">\r\n        选择入馆日期\r\n        <span class=\"year\">2025年</span>\r\n      </div>\r\n      <div class=\"date-grid\">\r\n        <div class=\"date-item\" v-for=\"date in dateList\" :key=\"date.day\"\r\n          :class=\"{ active: date.active, disabled: date.disabled }\" @click=\"selectDate(date)\">\r\n          <div class=\"day\">{{ date.day }}</div>\r\n          <div class=\"date\">{{ date.date }}</div>\r\n          <div class=\"status\">{{ date.status }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 选择入馆时段 -->\r\n    <div class=\"section\">\r\n      <div class=\"section-title\">\r\n        <span class=\"star\">★</span>\r\n        选择入馆时段\r\n      </div>\r\n      <div class=\"time-slots\">\r\n        <div class=\"time-slot\" v-for=\"slot in timeSlots\" :key=\"slot.id\" :class=\"{ active: slot.active }\"\r\n          @click=\"selectTimeSlot(slot)\">\r\n          <div class=\"time\">{{ slot.time }}</div>\r\n          <div class=\"capacity\">{{ slot.status }} ({{ slot.count }}人)</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 添加参观人员 -->\r\n    <div class=\"section\">\r\n      <div class=\"section-title\">\r\n        <span class=\"icon\">👥</span>\r\n        添加参观人员\r\n      </div>\r\n\r\n      <!-- 参观人数 -->\r\n      <div class=\"form-item\">\r\n        <label class=\"label\">*参观人数</label>\r\n        <div class=\"counter\">\r\n          <button class=\"counter-btn\" @click=\"decreaseCount\">-</button>\r\n          <span class=\"count\">{{ visitorCount }}</span>\r\n          <button class=\"counter-btn\" @click=\"increaseCount\">+</button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 联系人 -->\r\n      <div class=\"form-item\">\r\n        <label class=\"label\">*联系人</label>\r\n        <input type=\"text\" class=\"input\" placeholder=\"请输入姓名\" v-model=\"contactName\">\r\n      </div>\r\n\r\n      <!-- 联系方式 -->\r\n      <div class=\"form-item\">\r\n        <label class=\"label\">*联系方式</label>\r\n        <input type=\"text\" class=\"input\" placeholder=\"请输入手机号\" v-model=\"contactPhone\">\r\n      </div>\r\n\r\n      <!-- 团体名称 -->\r\n      <div class=\"form-item\">\r\n        <label class=\"label\">*团体名称</label>\r\n        <input type=\"text\" class=\"input\" placeholder=\"请输入单位名称\" v-model=\"groupName\">\r\n      </div>\r\n\r\n      <!-- 单位地址 -->\r\n      <div class=\"form-item\">\r\n        <label class=\"label\">*单位地址</label>\r\n        <input type=\"text\" class=\"input\" placeholder=\"请选择单位地址\" v-model=\"groupAddress\">\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 底部按钮 -->\r\n    <div class=\"bottom-actions\">\r\n      <div class=\"action-icons\">\r\n        <div class=\"action-icon\">\r\n          <span class=\"icon\">👤</span>\r\n          <span class=\"text\">人员名单</span>\r\n        </div>\r\n        <div class=\"action-icon\">\r\n          <span class=\"icon\">📋</span>\r\n          <span class=\"text\">填写说明</span>\r\n        </div>\r\n        <div class=\"action-icon\">\r\n          <span class=\"icon\">📎</span>\r\n          <span class=\"text\">附件</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"submit-buttons\">\r\n        <button class=\"btn btn-secondary\">提交预约</button>\r\n        <button class=\"btn btn-primary\">我的预约</button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default { name: 'ArchiveVisitBooking' }\r\n</script>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue'\r\n\r\n// 响应式数据\r\nconst visitorCount = ref(1)\r\nconst contactName = ref('')\r\nconst contactPhone = ref('')\r\nconst groupName = ref('')\r\nconst groupAddress = ref('')\r\n\r\n// 日期数据\r\nconst dateList = ref([\r\n  { day: '二', date: '9-2', status: '可预约', active: false, disabled: false },\r\n  { day: '三', date: '9-3', status: '可预约', active: false, disabled: false },\r\n  { day: '四', date: '9-4', status: '可预约', active: false, disabled: false },\r\n  { day: '五', date: '9-5', status: '可预约', active: true, disabled: false },\r\n  { day: '六', date: '9-6', status: '可预约', active: false, disabled: false },\r\n  { day: '日', date: '9-7', status: '可预约', active: false, disabled: false },\r\n  { day: '一', date: '9-8', status: '可预约', active: false, disabled: true }\r\n])\r\n\r\n// 时段数据\r\nconst timeSlots = ref([\r\n  { id: 1, time: '09:00-11:00', status: '可预约', count: 48, active: false },\r\n  { id: 2, time: '13:30-16:30', status: '可预约', count: 160, active: true }\r\n])\r\n\r\n// 方法\r\nconst selectDate = (date) => {\r\n  if (date.disabled) return\r\n  dateList.value.forEach(item => item.active = false)\r\n  date.active = true\r\n}\r\n\r\nconst selectTimeSlot = (slot) => {\r\n  timeSlots.value.forEach(item => item.active = false)\r\n  slot.active = true\r\n}\r\n\r\nconst increaseCount = () => {\r\n  visitorCount.value++\r\n}\r\n\r\nconst decreaseCount = () => {\r\n  if (visitorCount.value > 1) {\r\n    visitorCount.value--\r\n  }\r\n}\r\n\r\nonMounted(() => {\r\n  // 初始化逻辑\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.archive-visit-booking {\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow: auto;\r\n  background: #f9f9f9;\r\n  padding: 12px;\r\n\r\n  .section {\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    margin-bottom: 12px;\r\n    padding: 10px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n    .section-title {\r\n      display: flex;\r\n      align-items: center;\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n      color: #333;\r\n      margin-bottom: 16px;\r\n      position: relative;\r\n\r\n      .star {\r\n        color: #ff6b35;\r\n        margin-right: 8px;\r\n        font-size: 14px;\r\n      }\r\n\r\n      .icon_museum {\r\n        width: 15px;\r\n        height: 15px;\r\n        margin-right: 8px;\r\n      }\r\n\r\n      .icon {\r\n        margin-right: 8px;\r\n        font-size: 16px;\r\n      }\r\n\r\n      .year {\r\n        position: absolute;\r\n        right: 0;\r\n        font-size: 14px;\r\n        color: #666;\r\n        font-weight: normal;\r\n      }\r\n    }\r\n\r\n    // 日期选择样式\r\n    .date-grid {\r\n      display: flex;\r\n      gap: 6px;\r\n      overflow-x: auto;\r\n      padding: 4px 0;\r\n      justify-content: space-between;\r\n\r\n      .date-item {\r\n        flex: 1;\r\n        min-width: 50px;\r\n        max-width: 70px;\r\n        background: #f8f8f8;\r\n        border-radius: 6px;\r\n        padding: 10px 4px;\r\n        text-align: center;\r\n        cursor: pointer;\r\n        transition: all 0.3s ease;\r\n        border: 1px solid transparent;\r\n        position: relative;\r\n\r\n        .day {\r\n          font-size: 12px;\r\n          color: #666;\r\n          margin-bottom: 3px;\r\n          line-height: 1;\r\n          font-weight: normal;\r\n        }\r\n\r\n        .date {\r\n          font-size: 13px;\r\n          font-weight: 500;\r\n          color: #333;\r\n          margin-bottom: 3px;\r\n          line-height: 1;\r\n        }\r\n\r\n        .status {\r\n          font-size: 10px;\r\n          color: #999;\r\n          line-height: 1;\r\n          font-weight: normal;\r\n        }\r\n\r\n        &.active {\r\n          background: #fff3e0;\r\n          border-color: #ff6b35;\r\n\r\n          .day,\r\n          .date {\r\n            color: #ff6b35;\r\n          }\r\n\r\n          .status {\r\n            color: #ff6b35;\r\n          }\r\n        }\r\n\r\n        &.disabled {\r\n          opacity: 0.4;\r\n          cursor: not-allowed;\r\n          background: #f5f5f5;\r\n\r\n          .day,\r\n          .date,\r\n          .status {\r\n            color: #ccc;\r\n          }\r\n        }\r\n\r\n        &:hover:not(.disabled):not(.active) {\r\n          background: #f0f0f0;\r\n          border-color: #e0e0e0;\r\n        }\r\n      }\r\n    }\r\n\r\n    // 时段选择样式\r\n    .time-slots {\r\n      display: grid;\r\n      grid-template-columns: repeat(2, 1fr);\r\n      gap: 12px;\r\n\r\n      .time-slot {\r\n        background: #f8f8f8;\r\n        border-radius: 8px;\r\n        padding: 16px;\r\n        cursor: pointer;\r\n        transition: all 0.3s ease;\r\n        border: 2px solid transparent;\r\n\r\n        .time {\r\n          font-size: 16px;\r\n          font-weight: 600;\r\n          color: #333;\r\n          margin-bottom: 4px;\r\n        }\r\n\r\n        .capacity {\r\n          font-size: 14px;\r\n          color: #666;\r\n        }\r\n\r\n        &.active {\r\n          background: #fff3e0;\r\n          border-color: #ff6b35;\r\n\r\n          .time {\r\n            color: #ff6b35;\r\n          }\r\n\r\n          .capacity {\r\n            color: #ff6b35;\r\n          }\r\n        }\r\n\r\n        &:hover {\r\n          background: #f0f0f0;\r\n        }\r\n      }\r\n    }\r\n\r\n    // 表单样式\r\n    .form-item {\r\n      margin-bottom: 16px;\r\n\r\n      .label {\r\n        display: block;\r\n        font-size: 14px;\r\n        color: #333;\r\n        margin-bottom: 8px;\r\n        font-weight: 500;\r\n\r\n        &::before {\r\n          content: '*';\r\n          color: #ff6b35;\r\n          margin-right: 4px;\r\n        }\r\n      }\r\n\r\n      .input {\r\n        width: 100%;\r\n        padding: 12px 16px;\r\n        border: 1px solid #ddd;\r\n        border-radius: 8px;\r\n        font-size: 16px;\r\n        background: white;\r\n        transition: border-color 0.3s ease;\r\n\r\n        &:focus {\r\n          outline: none;\r\n          border-color: #ff6b35;\r\n        }\r\n\r\n        &::placeholder {\r\n          color: #999;\r\n        }\r\n      }\r\n\r\n      .counter {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 16px;\r\n\r\n        .counter-btn {\r\n          width: 32px;\r\n          height: 32px;\r\n          border: 1px solid #ddd;\r\n          border-radius: 6px;\r\n          background: white;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          cursor: pointer;\r\n          font-size: 18px;\r\n          color: #666;\r\n          transition: all 0.3s ease;\r\n\r\n          &:hover {\r\n            border-color: #ff6b35;\r\n            color: #ff6b35;\r\n          }\r\n\r\n          &:active {\r\n            background: #f0f0f0;\r\n          }\r\n        }\r\n\r\n        .count {\r\n          font-size: 18px;\r\n          font-weight: 600;\r\n          color: #333;\r\n          min-width: 24px;\r\n          text-align: center;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 底部操作区域\r\n  .bottom-actions {\r\n    background: white;\r\n    border-radius: 8px;\r\n    padding: 16px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n    .action-icons {\r\n      display: flex;\r\n      justify-content: space-around;\r\n      margin-bottom: 20px;\r\n      padding-bottom: 16px;\r\n      border-bottom: 1px solid #f0f0f0;\r\n\r\n      .action-icon {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        cursor: pointer;\r\n        transition: all 0.3s ease;\r\n\r\n        .icon {\r\n          font-size: 24px;\r\n          margin-bottom: 4px;\r\n        }\r\n\r\n        .text {\r\n          font-size: 12px;\r\n          color: #666;\r\n        }\r\n\r\n        &:hover {\r\n          .text {\r\n            color: #ff6b35;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .submit-buttons {\r\n      display: grid;\r\n      grid-template-columns: 1fr 1fr;\r\n      gap: 12px;\r\n\r\n      .btn {\r\n        padding: 14px 24px;\r\n        border-radius: 8px;\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        border: none;\r\n        cursor: pointer;\r\n        transition: all 0.3s ease;\r\n\r\n        &.btn-secondary {\r\n          background: #f8f8f8;\r\n          color: #666;\r\n\r\n          &:hover {\r\n            background: #e8e8e8;\r\n          }\r\n        }\r\n\r\n        &.btn-primary {\r\n          background: #ff6b35;\r\n          color: white;\r\n\r\n          &:hover {\r\n            background: #e55a2b;\r\n          }\r\n        }\r\n\r\n        &:active {\r\n          transform: translateY(1px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 响应式设计\r\n@media (max-width: 768px) {\r\n  .archive-visit-booking {\r\n    padding: 12px;\r\n\r\n    .section {\r\n      padding: 12px;\r\n\r\n      .date-grid {\r\n        grid-template-columns: repeat(3, 1fr);\r\n      }\r\n\r\n      .time-slots {\r\n        grid-template-columns: 1fr;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>"], "mappings": "AAyGA,SAASA,GAAG,EAAEC,SAAS,QAAQ,KAAK;;AAEpC;;AANA,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAsB,CAAC;;;;;IAO9C,IAAMC,YAAY,GAAGJ,GAAG,CAAC,CAAC,CAAC;IAC3B,IAAMK,WAAW,GAAGL,GAAG,CAAC,EAAE,CAAC;IAC3B,IAAMM,YAAY,GAAGN,GAAG,CAAC,EAAE,CAAC;IAC5B,IAAMO,SAAS,GAAGP,GAAG,CAAC,EAAE,CAAC;IACzB,IAAMQ,YAAY,GAAGR,GAAG,CAAC,EAAE,CAAC;;IAE5B;IACA,IAAMS,QAAQ,GAAGT,GAAG,CAAC,CACnB;MAAEU,GAAG,EAAE,GAAG;MAAEC,IAAI,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,QAAQ,EAAE;IAAM,CAAC,EACxE;MAAEJ,GAAG,EAAE,GAAG;MAAEC,IAAI,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,QAAQ,EAAE;IAAM,CAAC,EACxE;MAAEJ,GAAG,EAAE,GAAG;MAAEC,IAAI,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,QAAQ,EAAE;IAAM,CAAC,EACxE;MAAEJ,GAAG,EAAE,GAAG;MAAEC,IAAI,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,MAAM,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAM,CAAC,EACvE;MAAEJ,GAAG,EAAE,GAAG;MAAEC,IAAI,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,QAAQ,EAAE;IAAM,CAAC,EACxE;MAAEJ,GAAG,EAAE,GAAG;MAAEC,IAAI,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,QAAQ,EAAE;IAAM,CAAC,EACxE;MAAEJ,GAAG,EAAE,GAAG;MAAEC,IAAI,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,QAAQ,EAAE;IAAK,CAAC,CACxE,CAAC;;IAEF;IACA,IAAMC,SAAS,GAAGf,GAAG,CAAC,CACpB;MAAEgB,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,aAAa;MAAEL,MAAM,EAAE,KAAK;MAAEM,KAAK,EAAE,EAAE;MAAEL,MAAM,EAAE;IAAM,CAAC,EACvE;MAAEG,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,aAAa;MAAEL,MAAM,EAAE,KAAK;MAAEM,KAAK,EAAE,GAAG;MAAEL,MAAM,EAAE;IAAK,CAAC,CACxE,CAAC;;IAEF;IACA,IAAMM,UAAU,GAAG,SAAbA,UAAUA,CAAIR,IAAI,EAAK;MAC3B,IAAIA,IAAI,CAACG,QAAQ,EAAE;MACnBL,QAAQ,CAACW,KAAK,CAACC,OAAO,CAAC,UAAAC,IAAI;QAAA,OAAIA,IAAI,CAACT,MAAM,GAAG,KAAK;MAAA,EAAC;MACnDF,IAAI,CAACE,MAAM,GAAG,IAAI;IACpB,CAAC;IAED,IAAMU,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,IAAI,EAAK;MAC/BT,SAAS,CAACK,KAAK,CAACC,OAAO,CAAC,UAAAC,IAAI;QAAA,OAAIA,IAAI,CAACT,MAAM,GAAG,KAAK;MAAA,EAAC;MACpDW,IAAI,CAACX,MAAM,GAAG,IAAI;IACpB,CAAC;IAED,IAAMY,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;MAC1BrB,YAAY,CAACgB,KAAK,EAAE;IACtB,CAAC;IAED,IAAMM,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;MAC1B,IAAItB,YAAY,CAACgB,KAAK,GAAG,CAAC,EAAE;QAC1BhB,YAAY,CAACgB,KAAK,EAAE;MACtB;IACF,CAAC;IAEDnB,SAAS,CAAC,YAAM;MACd;IAAA,CACD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}