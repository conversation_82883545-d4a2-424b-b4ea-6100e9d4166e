{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, resolveComponent as _resolveComponent, createVNode as _createVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, createBlock as _createBlock, createStaticVNode as _createStaticVNode } from \"vue\";\nimport _imports_0 from '../../assets/img/icon_museum.png';\nvar _hoisted_1 = {\n  class: \"archive-visit-booking\"\n};\nvar _hoisted_2 = {\n  class: \"section\"\n};\nvar _hoisted_3 = {\n  class: \"date-grid flex_box\"\n};\nvar _hoisted_4 = [\"onClick\"];\nvar _hoisted_5 = {\n  class: \"day\"\n};\nvar _hoisted_6 = {\n  class: \"date\"\n};\nvar _hoisted_7 = {\n  class: \"status\"\n};\nvar _hoisted_8 = {\n  class: \"section\"\n};\nvar _hoisted_9 = {\n  class: \"time-slots\"\n};\nvar _hoisted_10 = [\"onClick\"];\nvar _hoisted_11 = {\n  class: \"time\"\n};\nvar _hoisted_12 = {\n  class: \"capacity\"\n};\nvar _hoisted_13 = {\n  key: 0,\n  class: \"active-indicator\"\n};\nvar _hoisted_14 = {\n  class: \"section\"\n};\nvar _hoisted_15 = {\n  class: \"upload-container\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_van_field = _resolveComponent(\"van-field\");\n  var _component_van_icon = _resolveComponent(\"van-icon\");\n  var _component_van_button = _resolveComponent(\"van-button\");\n  var _component_van_uploader = _resolveComponent(\"van-uploader\");\n  var _component_van_cell_group = _resolveComponent(\"van-cell-group\");\n  var _component_van_picker = _resolveComponent(\"van-picker\");\n  var _component_van_popup = _resolveComponent(\"van-popup\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 选择入馆日期 \"), _createElementVNode(\"div\", _hoisted_2, [_cache[9] || (_cache[9] = _createStaticVNode(\"<div class=\\\"section-header flex_box flex_justify_between\\\" data-v-b40b4332><div class=\\\"flex_box flex_align_center\\\" data-v-b40b4332><img src=\\\"\" + _imports_0 + \"\\\" alt=\\\"\\\" class=\\\"icon_museum\\\" data-v-b40b4332><div class=\\\"section-title\\\" data-v-b40b4332>选择入馆日期</div></div><div class=\\\"year\\\" data-v-b40b4332>2025年</div></div>\", 1)), _createElementVNode(\"div\", _hoisted_3, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.dateList, function (date) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: _normalizeClass([\"date-item\", {\n        active: date.active,\n        disabled: date.disabled\n      }]),\n      key: date.day,\n      onClick: function onClick($event) {\n        return $setup.selectDate(date);\n      }\n    }, [_createElementVNode(\"div\", _hoisted_5, _toDisplayString(date.day), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_6, _toDisplayString(date.date), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_7, _toDisplayString(date.status), 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_4);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 选择入馆时段 \"), _createElementVNode(\"div\", _hoisted_8, [_cache[10] || (_cache[10] = _createElementVNode(\"div\", {\n    class: \"section-header flex_box flex_justify_between\"\n  }, [_createElementVNode(\"div\", {\n    class: \"flex_box flex_align_center\"\n  }, [_createElementVNode(\"img\", {\n    src: _imports_0,\n    alt: \"\",\n    class: \"icon_museum\"\n  }), _createElementVNode(\"div\", {\n    class: \"section-title\"\n  }, \"选择入馆时段\")])], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_9, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.timeSlots, function (slot) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: _normalizeClass([\"time-slot\", {\n        active: slot.active\n      }]),\n      key: slot.id,\n      onClick: function onClick($event) {\n        return $setup.selectTimeSlot(slot);\n      }\n    }, [_createElementVNode(\"div\", _hoisted_11, _toDisplayString(slot.time), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_12, _toDisplayString(slot.status) + \" (\" + _toDisplayString(slot.count) + \"人)\", 1 /* TEXT */), slot.active ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13)) : _createCommentVNode(\"v-if\", true)], 10 /* CLASS, PROPS */, _hoisted_10);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 预约人员信息 \"), _createElementVNode(\"div\", _hoisted_14, [_cache[14] || (_cache[14] = _createElementVNode(\"div\", {\n    class: \"section-header flex_box flex_justify_between\"\n  }, [_createElementVNode(\"div\", {\n    class: \"flex_box flex_align_center\"\n  }, [_createElementVNode(\"span\", {\n    class: \"section-line\"\n  }), _createElementVNode(\"div\", {\n    class: \"section-title\"\n  }, \"预约人员信息\")])], -1 /* CACHED */)), _createVNode(_component_van_cell_group, {\n    inset: \"\",\n    class: \"form-group\"\n  }, {\n    default: _withCtx(function () {\n      return [_createCommentVNode(\" 联系人 \"), _createVNode(_component_van_field, {\n        modelValue: $setup.contactName,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n          return $setup.contactName = $event;\n        }),\n        label: \"联系人\",\n        placeholder: \"请输入姓名\",\n        required: \"\",\n        \"label-width\": \"80px\",\n        class: \"form-field\"\n      }, null, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 联系方式 \"), _createVNode(_component_van_field, {\n        modelValue: $setup.contactPhone,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n          return $setup.contactPhone = $event;\n        }),\n        label: \"联系方式\",\n        placeholder: \"请输入手机号\",\n        required: \"\",\n        \"label-width\": \"80px\",\n        class: \"form-field\"\n      }, null, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 团体名称 \"), _createVNode(_component_van_field, {\n        modelValue: $setup.groupName,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n          return $setup.groupName = $event;\n        }),\n        label: \"团体名称\",\n        placeholder: \"请输入单位名称\",\n        required: \"\",\n        \"label-width\": \"80px\",\n        class: \"form-field\"\n      }, null, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 团体性质 \"), _createVNode(_component_van_field, {\n        modelValue: $setup.groupTypeText,\n        \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n          return $setup.groupTypeText = $event;\n        }),\n        label: \"团体性质\",\n        placeholder: \"请选择团体性质\",\n        readonly: \"\",\n        clickable: \"\",\n        required: \"\",\n        \"label-width\": \"80px\",\n        class: \"form-field\",\n        onClick: _cache[4] || (_cache[4] = function ($event) {\n          return $setup.showGroupTypePicker = true;\n        })\n      }, null, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 人员名单 \"), _createVNode(_component_van_field, {\n        label: \"人员名单\",\n        required: \"\",\n        \"label-width\": \"80px\",\n        class: \"form-field upload-field\"\n      }, {\n        input: _withCtx(function () {\n          return [_createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_van_uploader, {\n            modelValue: $setup.personnelList,\n            \"onUpdate:modelValue\": _cache[5] || (_cache[5] = function ($event) {\n              return $setup.personnelList = $event;\n            }),\n            \"max-count\": 1,\n            \"after-read\": $setup.onPersonnelListRead,\n            accept: \".xlsx,.xls,.doc,.docx,.pdf\",\n            class: \"custom-uploader\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_van_button, {\n                size: \"small\",\n                type: \"primary\",\n                plain: \"\"\n              }, {\n                default: _withCtx(function () {\n                  return [_createVNode(_component_van_icon, {\n                    name: \"plus\"\n                  }), _cache[11] || (_cache[11] = _createTextVNode(\" 上传附件 \"))];\n                }),\n                _: 1 /* STABLE */,\n                __: [11]\n              })];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"]), $setup.personnelListTemplate ? (_openBlock(), _createBlock(_component_van_button, {\n            key: 0,\n            size: \"small\",\n            type: \"default\",\n            plain: \"\",\n            class: \"download-btn\",\n            onClick: $setup.downloadPersonnelTemplate\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_van_icon, {\n                name: \"down\"\n              }), _cache[12] || (_cache[12] = _createTextVNode(\" 下载模板 \"))];\n            }),\n            _: 1 /* STABLE */,\n            __: [12]\n          })) : _createCommentVNode(\"v-if\", true)])];\n        }),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 单位证明材料 \"), _createVNode(_component_van_field, {\n        label: \"单位证明材料\",\n        required: \"\",\n        \"label-width\": \"80px\",\n        class: \"form-field upload-field\"\n      }, {\n        input: _withCtx(function () {\n          return [_createVNode(_component_van_uploader, {\n            modelValue: $setup.unitProofFiles,\n            \"onUpdate:modelValue\": _cache[6] || (_cache[6] = function ($event) {\n              return $setup.unitProofFiles = $event;\n            }),\n            \"max-count\": 3,\n            \"after-read\": $setup.onUnitProofRead,\n            accept: \".jpg,.jpeg,.png,.pdf,.doc,.docx\",\n            class: \"custom-uploader\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_van_button, {\n                size: \"small\",\n                type: \"primary\",\n                plain: \"\"\n              }, {\n                default: _withCtx(function () {\n                  return [_createVNode(_component_van_icon, {\n                    name: \"plus\"\n                  }), _cache[13] || (_cache[13] = _createTextVNode(\" 上传附件 \"))];\n                }),\n                _: 1 /* STABLE */,\n                __: [13]\n              })];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      })];\n    }),\n    _: 1 /* STABLE */\n  })]), _createCommentVNode(\" 底部按钮 \"), _cache[15] || (_cache[15] = _createStaticVNode(\"<div class=\\\"bottom-actions\\\" data-v-b40b4332><div class=\\\"action-sections\\\" data-v-b40b4332><!-- 人员名单行 --><div class=\\\"action-row\\\" data-v-b40b4332><div class=\\\"action-item\\\" data-v-b40b4332><span class=\\\"icon\\\" data-v-b40b4332>�</span><span class=\\\"text\\\" data-v-b40b4332>人员名单</span></div><div class=\\\"action-item\\\" data-v-b40b4332><span class=\\\"icon\\\" data-v-b40b4332>📋</span><span class=\\\"text\\\" data-v-b40b4332>*单位说明</span></div></div><!-- 单位证明行 --><div class=\\\"action-row\\\" data-v-b40b4332><div class=\\\"action-item full-width\\\" data-v-b40b4332><span class=\\\"icon\\\" data-v-b40b4332>📎</span><span class=\\\"text\\\" data-v-b40b4332>*单位证明</span></div></div></div><div class=\\\"submit-buttons\\\" data-v-b40b4332><button class=\\\"btn btn-secondary\\\" data-v-b40b4332>提交预约</button><button class=\\\"btn btn-primary\\\" data-v-b40b4332>我的预约</button></div></div>\", 1)), _createCommentVNode(\" 团体性质选择弹窗 \"), _createVNode(_component_van_popup, {\n    show: $setup.showGroupTypePicker,\n    \"onUpdate:show\": _cache[8] || (_cache[8] = function ($event) {\n      return $setup.showGroupTypePicker = $event;\n    }),\n    position: \"bottom\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_van_picker, {\n        columns: $setup.groupTypeOptions,\n        onConfirm: $setup.onGroupTypeConfirm,\n        onCancel: _cache[7] || (_cache[7] = function ($event) {\n          return $setup.showGroupTypePicker = false;\n        })\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"show\"])]);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_Fragment", "_renderList", "$setup", "dateList", "date", "_normalizeClass", "active", "disabled", "key", "day", "onClick", "$event", "selectDate", "_hoisted_5", "_toDisplayString", "_hoisted_6", "_hoisted_7", "status", "_hoisted_8", "src", "alt", "_hoisted_9", "timeSlots", "slot", "id", "selectTimeSlot", "_hoisted_11", "time", "_hoisted_12", "count", "_hoisted_13", "_hoisted_14", "_createVNode", "_component_van_cell_group", "inset", "_component_van_field", "contactName", "label", "placeholder", "required", "contactPhone", "groupName", "groupTypeText", "readonly", "clickable", "_cache", "showGroupTypePicker", "input", "_withCtx", "_hoisted_15", "_component_van_uploader", "personnelList", "onPersonnelListRead", "accept", "_component_van_button", "size", "type", "plain", "_component_van_icon", "name", "personnelListTemplate", "_createBlock", "downloadPersonnelTemplate", "unitProofFiles", "onUnitProofRead", "_component_van_popup", "show", "position", "_component_van_picker", "columns", "groupTypeOptions", "onConfirm", "onGroupTypeConfirm", "onCancel"], "sources": ["D:\\zy\\xm\\h5\\i西安\\appperformduties\\src\\views\\ArchiveVisitBooking\\ArchiveVisitBooking.vue"], "sourcesContent": ["<template>\r\n  <div class=\"archive-visit-booking\">\r\n    <!-- 选择入馆日期 -->\r\n    <div class=\"section\">\r\n      <div class=\"section-header flex_box flex_justify_between\">\r\n        <div class=\"flex_box flex_align_center\">\r\n          <img src=\"../../assets/img/icon_museum.png \" alt=\"\" class=\"icon_museum\">\r\n          <div class=\"section-title\">选择入馆日期</div>\r\n        </div>\r\n        <div class=\"year\">2025年</div>\r\n      </div>\r\n      <div class=\"date-grid flex_box\">\r\n        <div class=\"date-item\" v-for=\"date in dateList\" :key=\"date.day\"\r\n          :class=\"{ active: date.active, disabled: date.disabled }\" @click=\"selectDate(date)\">\r\n          <div class=\"day\">{{ date.day }}</div>\r\n          <div class=\"date\">{{ date.date }}</div>\r\n          <div class=\"status\">{{ date.status }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 选择入馆时段 -->\r\n    <div class=\"section\">\r\n      <div class=\"section-header flex_box flex_justify_between\">\r\n        <div class=\"flex_box flex_align_center\">\r\n          <img src=\"../../assets/img/icon_museum.png \" alt=\"\" class=\"icon_museum\">\r\n          <div class=\"section-title\">选择入馆时段</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"time-slots\">\r\n        <div class=\"time-slot\" v-for=\"slot in timeSlots\" :key=\"slot.id\" :class=\"{ active: slot.active }\"\r\n          @click=\"selectTimeSlot(slot)\">\r\n          <div class=\"time\">{{ slot.time }}</div>\r\n          <div class=\"capacity\">{{ slot.status }} ({{ slot.count }}人)</div>\r\n          <div v-if=\"slot.active\" class=\"active-indicator\"></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 预约人员信息 -->\r\n    <div class=\"section\">\r\n      <div class=\"section-header flex_box flex_justify_between\">\r\n        <div class=\"flex_box flex_align_center\">\r\n          <span class=\"section-line\"></span>\r\n          <div class=\"section-title\">预约人员信息</div>\r\n        </div>\r\n      </div>\r\n      <van-cell-group inset class=\"form-group\">\r\n        <!-- 联系人 -->\r\n        <van-field v-model=\"contactName\" label=\"联系人\" placeholder=\"请输入姓名\" required label-width=\"80px\"\r\n          class=\"form-field\" />\r\n\r\n        <!-- 联系方式 -->\r\n        <van-field v-model=\"contactPhone\" label=\"联系方式\" placeholder=\"请输入手机号\" required label-width=\"80px\"\r\n          class=\"form-field\" />\r\n\r\n        <!-- 团体名称 -->\r\n        <van-field v-model=\"groupName\" label=\"团体名称\" placeholder=\"请输入单位名称\" required label-width=\"80px\"\r\n          class=\"form-field\" />\r\n\r\n        <!-- 团体性质 -->\r\n        <van-field v-model=\"groupTypeText\" label=\"团体性质\" placeholder=\"请选择团体性质\" readonly clickable required\r\n          label-width=\"80px\" class=\"form-field\" @click=\"showGroupTypePicker = true\" />\r\n\r\n        <!-- 人员名单 -->\r\n        <van-field label=\"人员名单\" required label-width=\"80px\" class=\"form-field upload-field\">\r\n          <template #input>\r\n            <div class=\"upload-container\">\r\n              <van-uploader v-model=\"personnelList\" :max-count=\"1\" :after-read=\"onPersonnelListRead\"\r\n                accept=\".xlsx,.xls,.doc,.docx,.pdf\" class=\"custom-uploader\">\r\n                <van-button size=\"small\" type=\"primary\" plain>\r\n                  <van-icon name=\"plus\" />\r\n                  上传附件\r\n                </van-button>\r\n              </van-uploader>\r\n              <van-button v-if=\"personnelListTemplate\" size=\"small\" type=\"default\" plain class=\"download-btn\"\r\n                @click=\"downloadPersonnelTemplate\">\r\n                <van-icon name=\"down\" />\r\n                下载模板\r\n              </van-button>\r\n            </div>\r\n          </template>\r\n        </van-field>\r\n\r\n        <!-- 单位证明材料 -->\r\n        <van-field label=\"单位证明材料\" required label-width=\"80px\" class=\"form-field upload-field\">\r\n          <template #input>\r\n            <van-uploader v-model=\"unitProofFiles\" :max-count=\"3\" :after-read=\"onUnitProofRead\"\r\n              accept=\".jpg,.jpeg,.png,.pdf,.doc,.docx\" class=\"custom-uploader\">\r\n              <van-button size=\"small\" type=\"primary\" plain>\r\n                <van-icon name=\"plus\" />\r\n                上传附件\r\n              </van-button>\r\n            </van-uploader>\r\n          </template>\r\n        </van-field>\r\n      </van-cell-group>\r\n    </div>\r\n\r\n    <!-- 底部按钮 -->\r\n    <div class=\"bottom-actions\">\r\n      <div class=\"action-sections\">\r\n        <!-- 人员名单行 -->\r\n        <div class=\"action-row\">\r\n          <div class=\"action-item\">\r\n            <span class=\"icon\">�</span>\r\n            <span class=\"text\">人员名单</span>\r\n          </div>\r\n          <div class=\"action-item\">\r\n            <span class=\"icon\">📋</span>\r\n            <span class=\"text\">*单位说明</span>\r\n          </div>\r\n        </div>\r\n        <!-- 单位证明行 -->\r\n        <div class=\"action-row\">\r\n          <div class=\"action-item full-width\">\r\n            <span class=\"icon\">📎</span>\r\n            <span class=\"text\">*单位证明</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"submit-buttons\">\r\n        <button class=\"btn btn-secondary\">提交预约</button>\r\n        <button class=\"btn btn-primary\">我的预约</button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 团体性质选择弹窗 -->\r\n    <van-popup v-model:show=\"showGroupTypePicker\" position=\"bottom\">\r\n      <van-picker :columns=\"groupTypeOptions\" @confirm=\"onGroupTypeConfirm\" @cancel=\"showGroupTypePicker = false\" />\r\n    </van-popup>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default { name: 'ArchiveVisitBooking' }\r\n</script>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue'\r\n\r\n// 响应式数据\r\nconst contactName = ref('')\r\nconst contactPhone = ref('')\r\nconst groupName = ref('')\r\nconst groupType = ref('')\r\nconst groupTypeText = ref('')\r\nconst showGroupTypePicker = ref(false)\r\n\r\n// 文件上传相关\r\nconst personnelList = ref([])\r\nconst unitProofFiles = ref([])\r\nconst personnelListTemplate = ref(true) // 是否显示下载模板按钮\r\n\r\n// 团体性质选项\r\nconst groupTypeOptions = [\r\n  { text: '机关单位', value: '机关单位' },\r\n  { text: '企事业单位', value: '企事业单位' },\r\n  { text: '学校', value: '学校' },\r\n  { text: '社会团体', value: '社会团体' },\r\n  { text: '其他', value: '其他' }\r\n]\r\n\r\n// 日期数据\r\nconst dateList = ref([\r\n  { day: '二', date: '9-2', status: '可预约', active: false, disabled: false },\r\n  { day: '三', date: '9-3', status: '可预约', active: false, disabled: false },\r\n  { day: '四', date: '9-4', status: '可预约', active: false, disabled: false },\r\n  { day: '五', date: '9-5', status: '可预约', active: true, disabled: false },\r\n  { day: '六', date: '9-6', status: '可预约', active: false, disabled: false },\r\n  { day: '日', date: '9-7', status: '可预约', active: false, disabled: false },\r\n  { day: '一', date: '9-8', status: '可预约', active: false, disabled: true }\r\n])\r\n\r\n// 时段数据\r\nconst timeSlots = ref([\r\n  { id: 1, time: '09:00-11:00', status: '可预约', count: 49, active: false },\r\n  { id: 2, time: '13:30-16:30', status: '可预约', count: 160, active: true },\r\n  { id: 3, time: '13:30-16:30', status: '可预约', count: 160, active: true },\r\n  { id: 4, time: '13:30-16:30', status: '可预约', count: 160, active: true }\r\n])\r\n\r\n// 方法\r\nconst selectDate = (date) => {\r\n  if (date.disabled) return\r\n  dateList.value.forEach(item => item.active = false)\r\n  date.active = true\r\n}\r\n\r\nconst selectTimeSlot = (slot) => {\r\n  timeSlots.value.forEach(item => item.active = false)\r\n  slot.active = true\r\n}\r\n\r\n// 团体性质选择确认\r\nconst onGroupTypeConfirm = ({ selectedOptions }) => {\r\n  groupType.value = selectedOptions[0].value\r\n  groupTypeText.value = selectedOptions[0].text\r\n  showGroupTypePicker.value = false\r\n}\r\n\r\n// 人员名单文件上传\r\nconst onPersonnelListRead = (file) => {\r\n  console.log('人员名单文件上传:', file)\r\n  // 这里可以添加文件上传到服务器的逻辑\r\n  // 例如：uploadFile(file.file)\r\n}\r\n\r\n// 单位证明材料文件上传\r\nconst onUnitProofRead = (file) => {\r\n  console.log('单位证明材料上传:', file)\r\n  // 这里可以添加文件上传到服务器的逻辑\r\n  // 例如：uploadFile(file.file)\r\n}\r\n\r\n// 下载人员名单模板\r\nconst downloadPersonnelTemplate = () => {\r\n  // 创建一个虚拟的下载链接\r\n  const link = document.createElement('a')\r\n  link.href = '/templates/personnel-list-template.xlsx' // 模板文件路径\r\n  link.download = '人员名单模板.xlsx'\r\n  document.body.appendChild(link)\r\n  link.click()\r\n  document.body.removeChild(link)\r\n\r\n  // 或者可以使用实际的API下载\r\n  // window.open('/api/download/personnel-template', '_blank')\r\n}\r\n\r\nonMounted(() => {\r\n  // 初始化逻辑\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.archive-visit-booking {\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow: auto;\r\n  background: #f9f9f9;\r\n  padding: 10px 10px;\r\n\r\n  .section {\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    margin-bottom: 12px;\r\n    padding: 10px 6px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n    .section-header {\r\n      margin-bottom: 12px;\r\n\r\n      .icon_museum {\r\n        width: 16px;\r\n        height: 15px;\r\n        margin-right: 8px;\r\n      }\r\n\r\n      .section-line {\r\n        width: 3px;\r\n        height: 16px;\r\n        background: #A54E3B;\r\n        margin-right: 8px;\r\n      }\r\n\r\n      .section-title {\r\n        font-size: 15px;\r\n        font-weight: bold;\r\n        color: #333;\r\n      }\r\n\r\n      .year {\r\n        font-size: 14px;\r\n        color: #666;\r\n        font-weight: normal;\r\n      }\r\n    }\r\n\r\n    // 日期选择样式\r\n    .date-grid {\r\n      gap: 6px;\r\n      overflow-x: auto;\r\n\r\n      .date-item {\r\n        flex: 1;\r\n        max-width: 70px;\r\n        height: 70px;\r\n        background: #f8f8f8;\r\n        border-radius: 4px;\r\n        position: relative;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: space-evenly;\r\n\r\n        .day {\r\n          font-size: 12px;\r\n          color: #666;\r\n          margin-bottom: 3px;\r\n          line-height: 1;\r\n          font-weight: normal;\r\n        }\r\n\r\n        .date {\r\n          font-size: 13px;\r\n          font-weight: 500;\r\n          color: #333;\r\n          margin-bottom: 3px;\r\n          line-height: 1;\r\n        }\r\n\r\n        .status {\r\n          font-size: 10px;\r\n          color: #999;\r\n          line-height: 1;\r\n          font-weight: normal;\r\n        }\r\n\r\n        &.active {\r\n          background: #fff3e0;\r\n          border-color: #ff6b35;\r\n\r\n          .day,\r\n          .date {\r\n            color: #ff6b35;\r\n          }\r\n\r\n          .status {\r\n            color: #ff6b35;\r\n          }\r\n        }\r\n\r\n        &.disabled {\r\n          opacity: 0.4;\r\n          background: #f5f5f5;\r\n\r\n          .day,\r\n          .date,\r\n          .status {\r\n            color: #ccc;\r\n          }\r\n        }\r\n\r\n        &:hover:not(.disabled):not(.active) {\r\n          background: #f0f0f0;\r\n          border-color: #e0e0e0;\r\n        }\r\n      }\r\n    }\r\n\r\n    // 时段选择样式\r\n    .time-slots {\r\n      display: flex;\r\n      gap: 8px;\r\n\r\n      .time-slot {\r\n        flex: 1;\r\n        background: #f5f5f5;\r\n        border-radius: 6px;\r\n        padding: 12px 8px;\r\n        cursor: pointer;\r\n        transition: all 0.3s ease;\r\n        border: 1px solid transparent;\r\n        position: relative;\r\n        text-align: center;\r\n\r\n        .time {\r\n          font-size: 14px;\r\n          font-weight: 500;\r\n          color: #333;\r\n          margin-bottom: 2px;\r\n          line-height: 1.2;\r\n        }\r\n\r\n        .capacity {\r\n          font-size: 12px;\r\n          color: #666;\r\n          line-height: 1.2;\r\n        }\r\n\r\n        .active-indicator {\r\n          position: absolute;\r\n          top: 4px;\r\n          right: 4px;\r\n          width: 6px;\r\n          height: 6px;\r\n          background: #ff4444;\r\n          border-radius: 50%;\r\n        }\r\n\r\n        &.active {\r\n          background: #fff3e0;\r\n          border-color: #ff9800;\r\n\r\n          .time {\r\n            color: #ff6b35;\r\n          }\r\n\r\n          .capacity {\r\n            color: #ff6b35;\r\n          }\r\n        }\r\n\r\n        &:hover:not(.active) {\r\n          background: #eeeeee;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Vant表单组件样式\r\n    .form-group {\r\n      margin-top: 12px;\r\n      border-radius: 8px;\r\n      overflow: hidden;\r\n\r\n      .van-cell {\r\n        padding: 12px 0;\r\n        background: white;\r\n\r\n        &::after {\r\n          border-bottom: 1px solid #f0f0f0;\r\n        }\r\n\r\n        &:last-child::after {\r\n          border-bottom: none;\r\n        }\r\n      }\r\n\r\n      :deep(.van-field__label) {\r\n        color: #333;\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n        width: 80px;\r\n        flex-shrink: 0;\r\n\r\n        &::before {\r\n          content: '*';\r\n          color: #ff4444;\r\n          margin-right: 4px;\r\n        }\r\n      }\r\n\r\n      :deep(.van-field__control) {\r\n        font-size: 14px;\r\n        color: #333;\r\n\r\n        &::placeholder {\r\n          color: #999;\r\n        }\r\n      }\r\n\r\n      // 上传字段样式\r\n      .upload-field {\r\n        :deep(.van-field__control) {\r\n          padding: 0;\r\n        }\r\n\r\n        .upload-container {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 8px;\r\n          width: 100%;\r\n\r\n          .custom-uploader {\r\n            flex: 1;\r\n\r\n            :deep(.van-uploader__wrapper) {\r\n              display: flex;\r\n              align-items: center;\r\n              gap: 8px;\r\n            }\r\n\r\n            :deep(.van-uploader__preview) {\r\n              margin: 0 8px 0 0;\r\n            }\r\n\r\n            :deep(.van-uploader__upload) {\r\n              margin: 0;\r\n            }\r\n          }\r\n\r\n          .download-btn {\r\n            flex-shrink: 0;\r\n            margin-left: 8px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .counter {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 16px;\r\n\r\n      .counter-btn {\r\n        width: 32px;\r\n        height: 32px;\r\n        border: 1px solid #ddd;\r\n        border-radius: 6px;\r\n        background: white;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        cursor: pointer;\r\n        font-size: 18px;\r\n        color: #666;\r\n        transition: all 0.3s ease;\r\n\r\n        &:hover {\r\n          border-color: #ff6b35;\r\n          color: #ff6b35;\r\n        }\r\n\r\n        &:active {\r\n          background: #f0f0f0;\r\n        }\r\n      }\r\n\r\n      .count {\r\n        font-size: 18px;\r\n        font-weight: 600;\r\n        color: #333;\r\n        min-width: 24px;\r\n        text-align: center;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 底部操作区域\r\n.bottom-actions {\r\n  background: white;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n  .action-sections {\r\n    margin-bottom: 20px;\r\n    padding-bottom: 16px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n\r\n    .action-row {\r\n      display: flex;\r\n      gap: 12px;\r\n      margin-bottom: 12px;\r\n\r\n      &:last-child {\r\n        margin-bottom: 0;\r\n      }\r\n\r\n      .action-item {\r\n        flex: 1;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        gap: 8px;\r\n        padding: 12px 16px;\r\n        background: #f8f8f8;\r\n        border-radius: 6px;\r\n        cursor: pointer;\r\n        transition: all 0.3s ease;\r\n        border: 1px solid #e0e0e0;\r\n\r\n        &.full-width {\r\n          flex: 1;\r\n        }\r\n\r\n        .icon {\r\n          font-size: 16px;\r\n          color: #ff6b35;\r\n        }\r\n\r\n        .text {\r\n          font-size: 14px;\r\n          color: #333;\r\n          font-weight: 500;\r\n        }\r\n\r\n        &:hover {\r\n          background: #f0f0f0;\r\n          border-color: #ff6b35;\r\n\r\n          .text {\r\n            color: #ff6b35;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .submit-buttons {\r\n    display: grid;\r\n    grid-template-columns: 1fr 1fr;\r\n    gap: 12px;\r\n\r\n    .btn {\r\n      padding: 14px 24px;\r\n      border-radius: 8px;\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n      border: none;\r\n      cursor: pointer;\r\n      transition: all 0.3s ease;\r\n\r\n      &.btn-secondary {\r\n        background: #f8f8f8;\r\n        color: #666;\r\n\r\n        &:hover {\r\n          background: #e8e8e8;\r\n        }\r\n      }\r\n\r\n      &.btn-primary {\r\n        background: #ff6b35;\r\n        color: white;\r\n\r\n        &:hover {\r\n          background: #e55a2b;\r\n        }\r\n      }\r\n\r\n      &:active {\r\n        transform: translateY(1px);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 响应式设计\r\n// @media (max-width: 768px) {\r\n//   .archive-visit-booking {\r\n//     padding: 12px;\r\n\r\n//     .section {\r\n//       padding: 12px;\r\n\r\n//       .date-grid {\r\n//         grid-template-columns: repeat(3, 1fr);\r\n//       }\r\n\r\n//       .time-slots {\r\n//         grid-template-columns: 1fr;\r\n//       }\r\n//     }\r\n//   }\r\n// }</style>"], "mappings": ";OAMeA,UAAuC;;EAL/CC,KAAK,EAAC;AAAuB;;EAE3BA,KAAK,EAAC;AAAS;;EAQbA,KAAK,EAAC;AAAoB;;;EAGtBA,KAAK,EAAC;AAAK;;EACXA,KAAK,EAAC;AAAM;;EACZA,KAAK,EAAC;AAAQ;;EAMpBA,KAAK,EAAC;AAAS;;EAObA,KAAK,EAAC;AAAY;;;EAGdA,KAAK,EAAC;AAAM;;EACZA,KAAK,EAAC;AAAU;;;EACGA,KAAK,EAAC;;;EAM/BA,KAAK,EAAC;AAAS;;EA2BPA,KAAK,EAAC;AAAkB;;;;;;;;;uBAlEvCC,mBAAA,CAkIM,OAlINC,UAkIM,GAjIJC,mBAAA,YAAe,EACfC,mBAAA,CAgBM,OAhBNC,UAgBM,G,+XARJD,mBAAA,CAOM,OAPNE,UAOM,I,kBANJL,mBAAA,CAKMM,SAAA,QAAAC,WAAA,CALgCC,MAAA,CAAAC,QAAQ,YAAhBC,IAAI;yBAAlCV,mBAAA,CAKM;MALDD,KAAK,EAAAY,eAAA,EAAC,WAAW;QAAAC,MAAA,EACFF,IAAI,CAACE,MAAM;QAAAC,QAAA,EAAYH,IAAI,CAACG;MAAQ;MADPC,GAAG,EAAEJ,IAAI,CAACK,GAAG;MACDC,OAAK,WAALA,OAAKA,CAAAC,MAAA;QAAA,OAAET,MAAA,CAAAU,UAAU,CAACR,IAAI;MAAA;QACjFP,mBAAA,CAAqC,OAArCgB,UAAqC,EAAAC,gBAAA,CAAjBV,IAAI,CAACK,GAAG,kBAC5BZ,mBAAA,CAAuC,OAAvCkB,UAAuC,EAAAD,gBAAA,CAAlBV,IAAI,CAACA,IAAI,kBAC9BP,mBAAA,CAA2C,OAA3CmB,UAA2C,EAAAF,gBAAA,CAApBV,IAAI,CAACa,MAAM,iB;sCAKxCrB,mBAAA,YAAe,EACfC,mBAAA,CAeM,OAfNqB,UAeM,G,4BAdJrB,mBAAA,CAKM;IALDJ,KAAK,EAAC;EAA8C,IACvDI,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAA4B,IACrCI,mBAAA,CAAwE;IAAnEsB,GAAuC,EAnBvC3B,UAAuC;IAmBC4B,GAAG,EAAC,EAAE;IAAC3B,KAAK,EAAC;MAC1DI,mBAAA,CAAuC;IAAlCJ,KAAK,EAAC;EAAe,GAAC,QAAM,E,uBAGrCI,mBAAA,CAOM,OAPNwB,UAOM,I,kBANJ3B,mBAAA,CAKMM,SAAA,QAAAC,WAAA,CALgCC,MAAA,CAAAoB,SAAS,YAAjBC,IAAI;yBAAlC7B,mBAAA,CAKM;MALDD,KAAK,EAAAY,eAAA,EAAC,WAAW;QAAAC,MAAA,EAA4DiB,IAAI,CAACjB;MAAM;MAA3CE,GAAG,EAAEe,IAAI,CAACC,EAAE;MAC3Dd,OAAK,WAALA,OAAKA,CAAAC,MAAA;QAAA,OAAET,MAAA,CAAAuB,cAAc,CAACF,IAAI;MAAA;QAC3B1B,mBAAA,CAAuC,OAAvC6B,WAAuC,EAAAZ,gBAAA,CAAlBS,IAAI,CAACI,IAAI,kBAC9B9B,mBAAA,CAAiE,OAAjE+B,WAAiE,EAAAd,gBAAA,CAAxCS,IAAI,CAACN,MAAM,IAAG,IAAE,GAAAH,gBAAA,CAAGS,IAAI,CAACM,KAAK,IAAG,IAAE,iBAChDN,IAAI,CAACjB,MAAM,I,cAAtBZ,mBAAA,CAAuD,OAAvDoC,WAAuD,K;sCAK7DlC,mBAAA,YAAe,EACfC,mBAAA,CAyDM,OAzDNkC,WAyDM,G,4BAxDJlC,mBAAA,CAKM;IALDJ,KAAK,EAAC;EAA8C,IACvDI,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAA4B,IACrCI,mBAAA,CAAkC;IAA5BJ,KAAK,EAAC;EAAc,IAC1BI,mBAAA,CAAuC;IAAlCJ,KAAK,EAAC;EAAe,GAAC,QAAM,E,uBAGrCuC,YAAA,CAiDiBC,yBAAA;IAjDDC,KAAK,EAAL,EAAK;IAACzC,KAAK,EAAC;;sBAC1B;MAAA,OAAY,CAAZG,mBAAA,SAAY,EACZoC,YAAA,CACuBG,oBAAA;oBADHjC,MAAA,CAAAkC,WAAW;;iBAAXlC,MAAA,CAAAkC,WAAW,GAAAzB,MAAA;QAAA;QAAE0B,KAAK,EAAC,KAAK;QAACC,WAAW,EAAC,OAAO;QAACC,QAAQ,EAAR,EAAQ;QAAC,aAAW,EAAC,MAAM;QAC1F9C,KAAK,EAAC;+CAERG,mBAAA,UAAa,EACboC,YAAA,CACuBG,oBAAA;oBADHjC,MAAA,CAAAsC,YAAY;;iBAAZtC,MAAA,CAAAsC,YAAY,GAAA7B,MAAA;QAAA;QAAE0B,KAAK,EAAC,MAAM;QAACC,WAAW,EAAC,QAAQ;QAACC,QAAQ,EAAR,EAAQ;QAAC,aAAW,EAAC,MAAM;QAC7F9C,KAAK,EAAC;+CAERG,mBAAA,UAAa,EACboC,YAAA,CACuBG,oBAAA;oBADHjC,MAAA,CAAAuC,SAAS;;iBAATvC,MAAA,CAAAuC,SAAS,GAAA9B,MAAA;QAAA;QAAE0B,KAAK,EAAC,MAAM;QAACC,WAAW,EAAC,SAAS;QAACC,QAAQ,EAAR,EAAQ;QAAC,aAAW,EAAC,MAAM;QAC3F9C,KAAK,EAAC;+CAERG,mBAAA,UAAa,EACboC,YAAA,CAC8EG,oBAAA;oBAD1DjC,MAAA,CAAAwC,aAAa;;iBAAbxC,MAAA,CAAAwC,aAAa,GAAA/B,MAAA;QAAA;QAAE0B,KAAK,EAAC,MAAM;QAACC,WAAW,EAAC,SAAS;QAACK,QAAQ,EAAR,EAAQ;QAACC,SAAS,EAAT,EAAS;QAACL,QAAQ,EAAR,EAAQ;QAC/F,aAAW,EAAC,MAAM;QAAC9C,KAAK,EAAC,YAAY;QAAEiB,OAAK,EAAAmC,MAAA,QAAAA,MAAA,gBAAAlC,MAAA;UAAA,OAAET,MAAA,CAAA4C,mBAAmB;QAAA;+CAEnElD,mBAAA,UAAa,EACboC,YAAA,CAiBYG,oBAAA;QAjBDE,KAAK,EAAC,MAAM;QAACE,QAAQ,EAAR,EAAQ;QAAC,aAAW,EAAC,MAAM;QAAC9C,KAAK,EAAC;;QAC7CsD,KAAK,EAAAC,QAAA,CACd;UAAA,OAaM,CAbNnD,mBAAA,CAaM,OAbNoD,WAaM,GAZJjB,YAAA,CAMekB,uBAAA;wBANQhD,MAAA,CAAAiD,aAAa;;qBAAbjD,MAAA,CAAAiD,aAAa,GAAAxC,MAAA;YAAA;YAAG,WAAS,EAAE,CAAC;YAAG,YAAU,EAAET,MAAA,CAAAkD,mBAAmB;YACnFC,MAAM,EAAC,4BAA4B;YAAC5D,KAAK,EAAC;;8BAC1C;cAAA,OAGa,CAHbuC,YAAA,CAGasB,qBAAA;gBAHDC,IAAI,EAAC,OAAO;gBAACC,IAAI,EAAC,SAAS;gBAACC,KAAK,EAAL;;kCACtC;kBAAA,OAAwB,CAAxBzB,YAAA,CAAwB0B,mBAAA;oBAAdC,IAAI,EAAC;kBAAM,I,6CAAG,QAE1B,G;;;;;;;6CAEgBzD,MAAA,CAAA0D,qBAAqB,I,cAAvCC,YAAA,CAIaP,qBAAA;;YAJ4BC,IAAI,EAAC,OAAO;YAACC,IAAI,EAAC,SAAS;YAACC,KAAK,EAAL,EAAK;YAAChE,KAAK,EAAC,cAAc;YAC5FiB,OAAK,EAAER,MAAA,CAAA4D;;8BACR;cAAA,OAAwB,CAAxB9B,YAAA,CAAwB0B,mBAAA;gBAAdC,IAAI,EAAC;cAAM,I,6CAAG,QAE1B,G;;;;;;;UAKN/D,mBAAA,YAAe,EACfoC,YAAA,CAUYG,oBAAA;QAVDE,KAAK,EAAC,QAAQ;QAACE,QAAQ,EAAR,EAAQ;QAAC,aAAW,EAAC,MAAM;QAAC9C,KAAK,EAAC;;QAC/CsD,KAAK,EAAAC,QAAA,CACd;UAAA,OAMe,CANfhB,YAAA,CAMekB,uBAAA;wBANQhD,MAAA,CAAA6D,cAAc;;qBAAd7D,MAAA,CAAA6D,cAAc,GAAApD,MAAA;YAAA;YAAG,WAAS,EAAE,CAAC;YAAG,YAAU,EAAET,MAAA,CAAA8D,eAAe;YAChFX,MAAM,EAAC,iCAAiC;YAAC5D,KAAK,EAAC;;8BAC/C;cAAA,OAGa,CAHbuC,YAAA,CAGasB,qBAAA;gBAHDC,IAAI,EAAC,OAAO;gBAACC,IAAI,EAAC,SAAS;gBAACC,KAAK,EAAL;;kCACtC;kBAAA,OAAwB,CAAxBzB,YAAA,CAAwB0B,mBAAA;oBAAdC,IAAI,EAAC;kBAAM,I,6CAAG,QAE1B,G;;;;;;;;;;;;;QAOV/D,mBAAA,UAAa,E,04BA4BbA,mBAAA,cAAiB,EACjBoC,YAAA,CAEYiC,oBAAA;IAFOC,IAAI,EAAEhE,MAAA,CAAA4C,mBAAmB;;aAAnB5C,MAAA,CAAA4C,mBAAmB,GAAAnC,MAAA;IAAA;IAAEwD,QAAQ,EAAC;;sBACrD;MAAA,OAA8G,CAA9GnC,YAAA,CAA8GoC,qBAAA;QAAjGC,OAAO,EAAEnE,MAAA,CAAAoE,gBAAgB;QAAGC,SAAO,EAAErE,MAAA,CAAAsE,kBAAkB;QAAGC,QAAM,EAAA5B,MAAA,QAAAA,MAAA,gBAAAlC,MAAA;UAAA,OAAET,MAAA,CAAA4C,mBAAmB;QAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}