<template>
  <div class="CultureHistoryAdd">
    <van-form ref="formRef" @submit="onSubmit">
      <van-field v-model="form.title" name="title" placeholder="请输入标题" :rules="[{ required: true, message: '请输入标题' }]">
        <template #label><span style="color: red">*</span> 标题</template>
      </van-field>
      <van-field v-model="form.structureIdLabel" name="structureId" placeholder="请选择史料类别" is-link readonly
        :rules="[{ required: true, message: '请选择史料类别' }]" @click="showCategoryPicker = true">
        <template #label><span style="color: red">*</span> 史料类别</template>
      </van-field>
      <van-field v-model="form.notificationLabel" name="notificationId" placeholder="请选择征集公告" is-link readonly
        :rules="[{ required: true, message: '请选择征集公告' }]" @click="showNoticePicker = true">
        <template #label><span style="color: red">*</span> 征集公告</template>
      </van-field>
      <van-popup v-model:show="showCategoryPicker" position="bottom">
        <van-picker :columns="categoryOptions" :columns-field-names="{ text: 'text', value: 'value' }"
          @confirm="onCategoryConfirm" @cancel="showCategoryPicker = false" />
      </van-popup>
      <van-popup v-model:show="showNoticePicker" position="bottom">
        <van-picker :columns="NoticeOptions" :columns-field-names="{ text: 'text', value: 'value' }"
          @confirm="onNoticeConfirm" @cancel="showNoticePicker = false" />
      </van-popup>
      <van-field v-if="false" v-model="form.sort" name="sort" placeholder="请输入排序" type="digit"
        :rules="[{ required: true, message: '请输入排序' }]">
        <template #label><span style="color: red">*</span> 排序</template>
      </van-field>
      <div class="uploader-row">
        <span>上传附件</span>
        <van-uploader v-model="fileList" :max-count='3' :after-read="afterRead" ref="chatImg">
        </van-uploader>
      </div>
      <van-field v-model="form.content" name="content" type="textarea" placeholder="请输入内容" rows="4"
        :rules="[{ required: true, message: '请输入内容' }]">
        <template #label><span style="color: red">*</span> 内容</template>
      </van-field>
      <div style="margin: 16px;">
        <van-button round block type="primary" native-type="submit">
          提交
        </van-button>
      </div>
    </van-form>
    <!-- 引入通用用户信息弹窗组件 -->
    <UserInfoPopup v-model:show="showInfoPopup" @close="handlePopupClose" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { showToast } from 'vant'
import api from '@/api'
import { useRouter } from 'vue-router'
import UserInfoPopup from '@/components/UserInfoPopup.vue'
const router = useRouter()
const formRef = ref(null)
const form = ref({
  accountId: '1',
  title: '',
  structureId: '',
  structureIdLabel: '',
  notificationId: '',
  notificationLabel: '',
  sort: '',
  content: '',
  submitter: '1',
})
const nImgs = ref([])
const chatImg = ref(null)
const fileList = ref([])

const showCategoryPicker = ref(false)
const categoryOptions = ref([])
const showNoticePicker = ref(false)
const NoticeOptions = ref([])
const showInfoPopup = ref(false)
const publicUser = sessionStorage.getItem('public_user')

// 获取史料类别
const getCategoryOptions = async () => {
  try {
    const { data, code } = await api.historicalAccountsStructureList({ pageNo: 1, pageSize: 99 })
    if (code === 200 && Array.isArray(data)) {
      categoryOptions.value = data.map(item => ({ text: item.name, value: item.id }))
    } else {
      showToast('史料类别获取失败')
    }
  } catch (e) {
    showToast('史料类别获取失败')
  }
}
// 获取征集公告
const getNoticeOptions = async () => {
  try {
    const { data, code } = await api.notificationList({ pageNo: 1, pageSize: 99, tableId: 'id_message_notification', query: { channelId: '1935275534502072322', isDraft: 0 } })
    if (code === 200 && Array.isArray(data)) {
      NoticeOptions.value = data.map(item => ({ text: item.theme, value: item.id }))
    } else {
      showToast('获取征集公告失败')
    }
  } catch (e) {
    showToast('获取征集公告失败')
  }
}
onMounted(() => {
  getCategoryOptions()
  getNoticeOptions()
  document.title = '文史资料'
  if (!publicUser) {
    showInfoPopup.value = true
  }
})

// 处理弹窗关闭
const handlePopupClose = () => {
  showInfoPopup.value = false
}

// 史料类别选择
const onCategoryConfirm = ({ selectedOptions }) => {
  const option = selectedOptions[0]
  if (option) {
    form.value.structureId = option.value
    form.value.structureIdLabel = option.text
  }
  showCategoryPicker.value = false
}

// 选择征集公告
const onNoticeConfirm = ({ selectedOptions }) => {
  const option = selectedOptions[0]
  if (option) {
    form.value.notificationId = option.value
    form.value.notificationLabel = option.text
  }
  showNoticePicker.value = false
}

// 附件上传处理，使用api.globalUpload
const afterRead = async (file) => {
  const item = { url: file.content, uploadUrl: '', uploadId: '', state: 0, module: '' }
  const formData = new FormData()
  formData.append('file', file.file)
  const ret = await api.globalUpload(formData, () => { })
  if (ret) {
    var info = ret.data
    item.state = 2
    item.uploadUrl = api.fileURL(info.id) + info.newFileName || ''
    item.uploadId = info.id || ''
  } else {
    item.state = 3
    item.error = ret.errmsg || ''
  }
  nImgs.value.push(item)
  console.log(nImgs.value)
}

// vant-form 自动校验，onSubmit 只会在校验通过时触发
const onSubmit = async () => {
  const params = {
    form: {
      accountId: form.value.accountId,
      title: form.value.title,
      structureId: form.value.structureId,
      sort: form.value.sort,
      content: form.value.content,
      submitter: form.value.submitter,
      notificationId: form.value.notificationId,
      attachmentIds: nImgs.value.map(v => v.uploadId).join(',')
    }
  }
  try {
    console.log('publicUser==>', publicUser)
    if (!publicUser) {
      showToast('请提交信息')
      showInfoPopup.value = true
    } else {
      await api.historicalAccountsCollectAdd(params)
      showToast('提交成功')
      setTimeout(() => {
        router.back()
      }, 1000)
    }
  } catch (e) {
    showToast('提交失败')
  }
}
</script>

<style lang="scss">
.CultureHistoryAdd {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: #fff;

  .uploader-row {
    display: flex;
    align-items: center;
    padding: 16px;

    span {
      flex: none;
      margin-right: 12px;
    }

    .van-uploader {
      flex: 1;
    }
  }
}
</style>
