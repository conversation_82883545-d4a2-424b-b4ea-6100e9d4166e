<template>
  <div class="processItem">
    <div v-for="item in itemData"
         :key="item.id"
         class="processItemBox">
      <div class="rightLine"><img class="rightLineImg"
             :src="require('@/assets/img/Group_11.png')"></div>
      <div class="contentItem">
        <div class="contentItemBox">
          <div class="flex_box">
            <div class="status"
                 :class="{ color: item.statusName === '待办理' }"> {{ item.statusName }}</div>
            <div class="stationName"
                 v-if="item.masterStationUsers.length === 0"> {{ item.stationName }}</div>
          </div>
          <template v-if="item.masterStationUsers && item.masterStationUsers.length > 0">
            <div class="userName"
                 v-for="(user, a) in item.masterStationUsers"
                 :key="a + 'masterStationUsers'"> {{ user.userName }}:{{ user.mobile }}</div>
            <div class="time">{{ formatDate(item.recordDate) }}</div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default { name: 'processItem' }
</script>
<script setup>
// import { ref } from 'vue'
import { formatDate } from '@/assets/js/utils.js'
defineProps({
  itemData: {
    type: Object,
    default: () => { }
  }
})



</script>
<style lang="scss">
.processItem {
  padding: 16px;
  background-color: #fff;
  border-top: 1px solid #F4F4F4;

  .processItemBox {
    .rightLine {
      .rightLineImg {
        width: 16px;
        height: 16px;
      }
    }

    .contentItem {
      border-left: 1px solid #4488EB;
      margin-left: 7px;
      margin-top: -5px;
      padding: 10px;

      .contentItemBox {
        background: #F1F1F1;
        border-radius: 4px 4px 4px 4px;
        margin-top: -20px;
        margin-left: 10px;
        padding: 11px 13px;

        .status {
          color: #4488EB;
          font-weight: 400;
          font-size: 15px;
          color: #4488EB;
          line-height: 15px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          margin-right: 10px;
        }

        .color {
          color: #FF0705;
        }

        .stationName {
          font-weight: 400;
          font-size: 15px;
          color: #333;
          line-height: 15px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }

        .userName {
          margin-top: 10px;
          font-weight: 400;
          font-size: 13px;
          color: #666666;
          line-height: 15px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }

        .time {
          margin-top: 10px;
          font-weight: 400;
          font-size: 12px;
          color: #999999;
          line-height: 12px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
    }
  }
}
</style>
