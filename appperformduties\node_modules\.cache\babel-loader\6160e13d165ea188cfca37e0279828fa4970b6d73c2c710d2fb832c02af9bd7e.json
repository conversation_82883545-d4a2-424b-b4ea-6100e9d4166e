{"ast": null, "code": "import { openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"ArchiveVisitBooking\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1"], "sources": ["D:\\zy\\xm\\h5\\i西安\\appperformduties\\src\\views\\ArchiveVisitBooking\\ArchiveVisitBooking.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ArchiveVisitBooking\">\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ArchiveVisitBooking' }\r\n</script>\r\n<script setup>\r\nimport { onMounted } from 'vue'\r\nonMounted(() => {\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.ArchiveVisitBooking {}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAqB;;uBAAhCC,mBAAA,CACM,OADNC,UACM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}