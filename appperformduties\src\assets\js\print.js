import { MicroGlobal } from '@/common/config/qiankun'
export const Print = {
  /**
  * 初始化
  */
  init (dom) { this.writeIframe(this.getStyle() + dom.outerHTML) },
  /**
  * 复制原网页的样式
  */
  getStyle () {
    var str = ''
    var styles = []
    const qiankun = new MicroGlobal()
    if (window.__POWERED_BY_QIANKUN__) {
      styles = document.querySelector(`#${qiankun.name}`).querySelectorAll('style,link')
    }
    for (var i = 0; i < styles.length; i++) { str += styles[i].outerHTML }
    str += '<style>*{margin: 0;padding: 0;}html,body,div{height: auto!important;box-sizing: border-box;}.no-print{display:none;}</style>'
    return str
  },
  /**
  * 创建iframe
  */
  writeIframe (content) {
    var w, doc, iframe = document.createElement('iframe') // eslint-disable-line
    var f = document.body.appendChild(iframe)
    iframe.id = 'myIframe'
    iframe.setAttribute('style', 'position:absolute;width:0;height:0;top:-10px;left:-10px;')
    w = f.contentWindow || f.contentDocument
    doc = f.contentDocument || f.contentWindow.document
    doc.open()
    doc.write(content)
    setTimeout(() => {
      doc.close()
      this.toPrint(w)
      setTimeout(() => { document.body.removeChild(iframe) }, 100)
    }, 1000)
  },
  /**
  打印
  */
  toPrint (f) {
    try {
      setTimeout(() => {
        f.focus()
        try {
          if (!f.document.execCommand('print', false, null)) { f.print() }
        } catch (e) {
          f.print()
        }
        f.close()
      }, 10)
    } catch (err) {
      console.log('err', err)
    }
  }
}
