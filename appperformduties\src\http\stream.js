import config from '../config'
import router from '@/router'
import store from '@/store'
import { fetchEventSource } from '@microsoft/fetch-event-source'

const headersConfig = () => {
  const headers = { 'Content-Type': 'application/json' }
  const token = sessionStorage.getItem('token') || 'basic enlzb2Z0Onp5c29mdCo2MDc5' // 用户token
  const microAppToken = sessionStorage.getItem('microAppToken') || 'basic YW5vbnltb3VzOmFub255bW91cyo2MDc5' // 用户token
  const AreaId = sessionStorage.getItem('AreaId') || '' // 用户地区
  headers['u-login-areaId'] = AreaId
  if (router.currentRoute.value.meta.moduleName === 'PUBLIC') {
    headers.Authorization = microAppToken
    headers['u-terminal'] = 'PUBLIC'
  } else {
    headers.Authorization = token
    headers['u-terminal'] = 'PC'
  }
  headers['u-system-name'] = store.getters.getReadOpenConfig?.systemPlatform
  return headers
}
const http_stream = (url, options = {}) => {
  const {
    onMessage,
    onOpen,
    onClose,
    onError,
    timeout = 0,
    retryOnError = true,
    retryDelay = 1000,
    retryMax = 0,
    ...fetchOptions
  } = options

  const finalOptions = {
    method: 'POST',
    headers: headersConfig(),
    ...fetchOptions
  }

  const controller = new AbortController()
  let timeoutId = null
  let retryCount = 0
  // 启动流式请求
  const executeRequest = () => new Promise((resolve, reject) => {
    // 设置请求超时
    if (timeout > 0) {
      timeoutId = setTimeout(() => {
        reject(new Error(`请求超时，超过 ${timeout}ms`))
        controller.abort()
      }, timeout)
    }
    fetchEventSource(config.API_URL + url, {
      ...finalOptions,
      signal: controller.signal,
      openWhenHidden: true,
      retryOnError,
      retryDelay,
      retryMax,
      async onopen (response) {
        // 检查是否是JSON响应
        const contentType = response.headers.get('content-type')
        if (contentType && contentType.includes('application/json')) {
          const data = await response.json()
          if (data.code && data.code !== 200) {
            throw new Error('收到JSON格式的错误响应')
          }
        } else {
          if (onOpen) onOpen(response)
          if (response.ok) return
          throw new Error(`流式接口请求失败: ${response.statusText}`)
        }
      },
      onmessage (event) {
        try {
          if (onMessage) onMessage(event)
        } catch (err) {
          console.error('消息处理错误:', err)
          if (onError) onError(err)
        }
      },
      onclose () {
        if (timeoutId) clearTimeout(timeoutId)
        if (onClose) onClose()
        resolve()
      },
      onerror (err) {
        if (timeoutId) clearTimeout(timeoutId)
        if (onError) onError(err)
        if (retryCount < retryMax && retryOnError) {
          retryCount++
          console.log(`正在进行第 ${retryCount} 次重试...`)
          setTimeout(() => {
            executeRequest()
          }, retryDelay)
        } else {
          throw reject(err)
        }
      }
    })
  })
  const request = {
    abort: () => controller.abort(),
    retry: () => {
      retryCount++
      console.log(`手动触发第 ${retryCount} 次重试...`)
      return executeRequest()
    }
  }
  request.promise = executeRequest()
  return request
}
export default http_stream