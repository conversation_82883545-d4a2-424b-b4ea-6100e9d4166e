<template>
  <div class="NetworkDiscussList">
    <van-sticky>
      <van-tabs v-if="switchs.data.length" v-model:active="switchs.value" :color="'#4488EB '" @click-tab="onClickTab">
        <van-tab :title="item.label" :name="item.value" v-for="item in switchs.data" :key="item.value"></van-tab>
      </van-tabs>
      <van-search v-model="keyword" shape="round" placeholder="请输入关键词" @search="onSearch"></van-search>
    </van-sticky>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list v-model:loading="loading" :finished="finished" :finished-text="dataList.length == 0 ? '' : '没有更多了'"
        offset="52" @load="onLoad">
        <div v-for="item in dataList" :key="item.id" class="list_box" @click="openDetails(item)">
          <div class="list_item">
            <!-- <span v-if="item.isRedDot" class="red-dot"></span> -->
            <div class="list_item_header" v-if="item.collectType">
              <div class="list_item_tag">{{ item.collectType?.name }}</div>
              <div style="flex: 1;"></div>
            </div>
            <div class="list_item_title">{{ item.title }}</div>
            <div class="list_item_info">
              <span class="icon-time"></span>
              <span v-if="item.statusText !== '征集已结束'">
                {{ item.statusText }}
                <span class="blue">{{ item.days }}天</span>
              </span>
              <span v-else>
                {{ item.statusText }}
              </span>
            </div>
            <div class="list_item_org">发布机构：{{ item.publishOrganize }}</div>
            <div class="list_item_opinion">{{ item.opinionText }}</div>
            <div class="list_item_btn_wrap">
              <van-button v-if="item.canJoin" type="primary" block size="small"
                @click.stop="openDetails(item)">立即参与</van-button>
              <div v-else class="view-detail" @click.stop="openDetails(item)">查看详情</div>
            </div>
          </div>
        </div>
        <van-empty v-if="dataList.length == 0 && !loading" description="暂无数据" />
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
export default { name: 'NetworkDiscussList' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
const router = useRouter()

const title = ref('网络议政列表')
const switchs = ref({ value: '0', data: [{ label: '全部', value: '0' }, { label: '进行中', value: '2' }, { label: '未开始', value: '1' }, { label: '已结束', value: '3' }] })
const keyword = ref('')//搜索关键词
const loading = ref(false)//加载中
const finished = ref(false)//是否加载完成
const refreshing = ref(false)//下拉刷新
const pageNo = ref(1)//当前页码
const pageSize = ref(15)//每页条数
const total = ref(0)//总条数
const dataList = ref([])
onMounted(() => {
  if (title.value) {
    document.title = title.value
  }
  setTimeout(() => {
    onRefresh()
  }, 100)
})
const onSearch = () => {
  onRefresh()
}
const onRefresh = () => {
  pageNo.value = 1
  dataList.value = []
  finished.value = false
  loading.value = true
  getList()
}
const onLoad = () => {
  if (dataList.value.length < total.value) {
    pageNo.value = pageNo.value + 1
    getList()
  } else {
    finished.value = true
  }
}
// 获取网络议政列表
const getList = async () => {
  const params = {
    collectStatus: switchs.value.value,
    keyword: keyword.value,
    pageNo: pageNo.value,
    pageSize: pageSize.value,
    tableId: 'id_opinion_collect'
  }
  const { data, code, total: totals } = await api.opinioncollectList(params)
  if (code === 200) {
    const now = new Date().getTime()
    const oneDay = 24 * 60 * 60 * 1000
    const processedData = (data || []).map(item => {
      const start = new Date(item.startDate).getTime()
      const end = new Date(item.endDate).getTime()
      let statusText = ''
      let days = 0
      if (now > end) {
        statusText = '征集已结束'
        days = 0
      } else if (now > start) {
        statusText = '征集结束剩余：'
        days = Math.floor((end - now) / oneDay)
      } else {
        statusText = '征集开始还有：'
        days = Math.floor((start - now) / oneDay)
      }
      let opinionText = ''
      if (now > new Date(item.startDate).getTime()) {
        opinionText = (item.isJoinName || '') + '，已有' + (item.commentCount || 0) + '条意见'
      } else {
        opinionText = '征集暂未开始'
      }
      const canJoin = now > start && now < end && item.isJoinName === '未参与'
      return {
        ...item,
        statusText,
        days,
        opinionText,
        canJoin
      }
    })
    dataList.value = dataList.value.concat(processedData)
    total.value = totals
    loading.value = false
    refreshing.value = false
    if (dataList.value.length >= total.value) {
      finished.value = true
    }
  } else {
    loading.value = false
    finished.value = true
    refreshing.value = false
  }
}
// 切换tab
const onClickTab = () => {
  onRefresh()
}
// 跳转网络议政详情
const openDetails = (item) => {
  router.push({ path: '/NetworkDiscussDetails', query: { id: item.id } })
}
</script>
<style lang="scss">
.NetworkDiscussList {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: #F4F5F9;

  .list_box {
    // padding: 10px 12px;
    background-color: #fff;
    border-top: 1px solid #F4F4F4;
    margin: 12px 16px;
    box-shadow: rgba(24, 64, 118, 0.08) 0px 2px 10px 1px;
    border-radius: 4px;

    .list_item {
      padding: 10px 12px;
      position: relative;

      .red-dot {
        width: 8px;
        height: 8px;
        background: #ff3b30;
        border-radius: 50%;
        display: inline-block;
        margin-left: auto;
        position: absolute;
        right: 10px;
        top: 10px;
      }

      .list_item_header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-bottom: 10px;

        .list_item_tag {
          background: rgba(246, 147, 28, 0.1);
          color: rgb(246, 147, 28);
          border-radius: 8px;
          padding: 2px 8px;
          font-size: 14px;
          margin-right: 8px;
        }
      }

      .list_item_title {
        font-size: 17px;
        font-weight: 600;
        color: #333333;
        margin-bottom: 10px;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .list_item_info {
        font-size: 14px;
        color: #333;
        margin-bottom: 6px;
        font-weight: 600;

        .blue {
          color: #4488eb;
          font-weight: 600;
        }

        .icon-time {
          display: inline-block;
          width: 16px;
          height: 16px;
          background: url('@/assets/img/icon_countdown.png') no-repeat center/contain;
          vertical-align: middle;
          margin-right: 4px;
        }
      }

      .list_item_org {
        margin-bottom: 6px;
        font-size: 14px;
        color: rgb(51, 51, 51);
      }

      .list_item_opinion {
        font-size: 14px;
        color: #888;
        margin-bottom: 4px;
      }

      .list_item_status {
        font-size: 13px;
        color: #888;
        margin-bottom: 8px;
      }

      .list_item_btn_wrap {
        display: flex;
        justify-content: center;
        margin-top: 15px;

        .view-detail {
          text-align: center;
          color: #333;
          padding: 7px 0;
          width: 100%;
          font-size: 15px;
          cursor: pointer;
        }
      }
    }
  }

}
</style>
