<template>
  <div class="LayoutHome">
    <!-- 全局loading遮罩 -->
    <div v-if="loadingPage"
      class="global-loading-mask flex_box flex_flex_direction_column flex_align_center flex_justify_content">
      <div class="global-loading-spinner"></div>
      <div class="global-loading-text">加载中...</div>
    </div>
    <!-- 顶部背景和logo -->
    <div class="top_bg">
      <img src="@/assets/img/icon_top_bg.png" alt="" class="bg_img">
      <div class="logo">
        <img src="@/assets/img/icon_top_logo.png" alt="" style="width: 180px;height: 30px;">
      </div>
      <div class="top_text">
        <img src="@/assets/img/icon_top_text.png" alt="" style="width: 277px;height: 55px;">
      </div>
    </div>
    <!-- 政协资讯 -->
    <div class="new_box">
      <div class="header_box flex_box flex_align_center flex_justify_between">
        <span class="header_title">政协资讯</span>
        <span class="header_all" @click="openNewMore()">全部 &gt;</span>
      </div>
      <template v-if="loading">
        <div class="loading">
          <span class="loading_spinner"></span>
        </div>
      </template>
      <template v-else-if="carouselList.length > 0">
        <div class="carousel">
          <van-swipe class="swiper-container" :autoplay="3000" indicator-color="#fff" :show-indicators="true"
            :loop="true">
            <van-swipe-item v-for="(item, index) in carouselList" :key="index" class="swiper-item"
              @click="onCarouselClick(item)">
              <div class="swiper-image-container">
                <img :src="config.API_URL + '/image/' + item.infoPic" :alt="item.title" class="swiper-image">
                <div class="swiper-overlay">
                  <div class="swiper-title">{{ item.infoTitle }}</div>
                </div>
                <div class="carousel-dots">
                  <span v-for="(item, idx) in carouselList" :key="idx" class="dot"
                    :class="{ active: idx === carouselIndex }" @click="goToNewsCarousel(idx)"></span>
                </div>
              </div>
            </van-swipe-item>
          </van-swipe>
        </div>
      </template>
      <template v-else>
        <div class="loading">
          <span>暂无数据</span>
        </div>
      </template>
    </div>
    <!-- 协商活动预热 -->
    <div class="negotiation_activities_preheating">
      <!-- 协商活动预热头部 -->
      <div class="header_box flex_box flex_align_center flex_justify_between">
        <span class="header_title">协商活动预热</span>
      </div>
      <!-- 协商活动预热图片 -->
      <div class="negotiation_activity_preheat" v-if="isPreheat == '1'">
        <img :src="config.API_URL + '/pageImg/open/ixian_preheat_bg'" alt="" class="negotiation_activity_preheat_img"
          @click="openActivety(null)">
      </div>
      <!-- 协商议题区域 -->
      <div class="negotiation_topics">
        <div class="negotiation_topics_title">协商议题</div>
        <template v-if="negotiationTopicsLoading">
          <div class="loading">
            <span class="loading_spinner"></span>
          </div>
        </template>
        <template v-else-if="negotiationTopics.length > 0">
          <div class="negotiation_topics_content">
            <div class="negotiation-topic-card main" @click="openActivety(negotiationTopics[0])">
              <div class="topic-title">{{ negotiationTopics[0].title }}</div>
              <div class="topic-image">
                <img v-if="negotiationTopics[0].titlePic"
                  :src="config.API_URL + '/image/' + negotiationTopics[0].titlePic" alt="icon" />
              </div>
              <button class="topic-btn">去看看</button>
            </div>
            <div class="negotiation-topic-card sub" v-for="item in negotiationTopics.slice(1)" :key="item.id"
              @click="openActivety(item)">
              <div class="topic-title">{{ item.title }}</div>
              <div class="sub-topic-btn">
                <button class="topic-btn" style="margin:0;">去看看</button>
                <div class="topic-image small" style="margin:0;">
                  <img v-if="item.titlePic" :src="config.API_URL + '/image/' + item.titlePic" alt="icon" />
                </div>
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="loading">
            <span>暂无数据</span>
          </div>
        </template>
        <div class="negotiation-submit-btn-wrapper" @click="openActivety(null)">
          <button class="negotiation-submit-btn">提交意见建议</button>
        </div>
      </div>
      <!-- 网络议政与问卷调查卡片区域 -->
      <div class="custom-action-cards">
        <div class="action-card network" @click="openNetwork">
          <div class="action-card-content">
            <span class="action-title">参加网络议政</span>
            <img class="action-arrow" src="@/assets/img/icon_arrow.png" alt=">" />
          </div>
          <img class="action-icon" src="@/assets/img/icon_network.png" alt="网络议政" />
        </div>
        <div class="action-card survey" @click="openSurvey">
          <div class="action-card-content">
            <span class="action-title">参加问卷调查</span>
            <img class="action-arrow" src="@/assets/img/icon_arrow.png" alt=">" />
          </div>
          <img class="action-icon" src="@/assets/img/icon_survey.png" alt="问卷调查" />
        </div>
      </div>
      <!-- 视频+图文直播 -->
      <div class="video_graphic_live" v-if="false">
        <div class="video_graphic_live_title">视频+图文直播</div>
        <template v-if="videoGraphicLiveLoading">
          <div class="loading">
            <span class="loading_spinner"></span>
          </div>
        </template>
        <template v-else-if="videoGraphicLive.length > 0">
          <div class="video_graphic_live_content">
            <div class="vg-carousel-img-wrapper" @touchstart="onVGTouchStart" @touchmove="onVGTouchMove"
              @touchend="onVGTouchEnd" @click="onVGCarouselClick(videoGraphicLive[vgIndex])">
              <img class="vg-carousel-img" :src="videoGraphicLive[vgIndex].img"
                :alt="videoGraphicLive[vgIndex].title" />
              <div class="vg-carousel-bottom-bar">
                <div class="vg-carousel-title">{{ videoGraphicLive[vgIndex].title }}</div>
                <div class="vg-carousel-dots">
                  <span v-for="(item, idx) in videoGraphicLive" :key="idx" class="vg-dot"
                    :class="{ active: idx === vgIndex }" @click.stop="goToVGCarousel(idx)"></span>
                </div>
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="loading">
            <span>暂无数据</span>
          </div>
        </template>
      </div>
    </div>
    <!-- 委员会客厅 -->
    <div class="committee_living_room">
      <img src="@/assets/img/icon_committee_living_room.png" alt="" class="committee_living_room_img"
        @click="openCommitteeLivingRoom()">
    </div>
    <!-- 协商议题征集 -->
    <div class="negotiation_topics_collect"
      v-if="negotiationtopicsCollectList && negotiationtopicsCollectList.length > 0">
      <div class="header_box flex_box flex_align_center flex_justify_between">
        <span class="header_title">协商议题征集</span>
      </div>
      <div class="negotiation_topics_collect_box">
        <template v-if="negotiationTopicsCollectLoading">
          <div class="loading">
            <span class="loading_spinner"></span>
          </div>
        </template>
        <template v-else-if="negotiationtopicsCollectList.length > 0">
          <div class="topics_collect_title">{{ negotiationtopicsCollectList[0].title }}</div>
          <div class="topics_collect_content">
            {{ negotiationtopicsCollectList[0].content }}
          </div>
          <div class="topics_collect_organize">发布机构：{{ negotiationtopicsCollectList[0].publishOrganize }}</div>
          <div class="topics_collect_flex">
            <div class="topics_collect_flex_text1">
              <span v-if="negotiationtopicsCollectList[0].statusText !== '征集已结束'">
                {{ negotiationtopicsCollectList[0].statusText }}
                <span class="blue">{{ negotiationtopicsCollectList[0].days }}天</span>
              </span>
              <span v-else>
                {{ negotiationtopicsCollectList[0].statusText }}
              </span>
            </div>
            <div class="topics_collect_flex_text2">已有{{ negotiationtopicsCollectList[0].commentCount }}条建议</div>
          </div>
          <div class="negotiation-submit-btn-wrapper" @click="openCollectInfo(negotiationtopicsCollectList[0])">
            <button class="negotiation-submit-btn">立即参与</button>
          </div>
        </template>
        <template v-else>
          <div class="loading">
            <span>暂无数据</span>
          </div>
        </template>
      </div>
    </div>
    <!-- 政协概况与文史资料卡片区域 -->
    <div class="overview_history_card">
      <div class="action_card overview" @click="openOverview">
        <div class="overview_history_card_content">
          <div class="action_title">政协概况</div>
          <div class="action_info">查看详情</div>
        </div>
      </div>
      <div class="action_card history" @click="openHistory">
        <div class="overview_history_card_content">
          <div class="action_title">文史资料</div>
          <div class="action_info">查看详情</div>
        </div>
      </div>
    </div>
    <!-- 文史资料征集 -->
    <div class="cultural_historical_collection" v-if="culturalHistoryList && culturalHistoryList.length > 0">
      <div class="header_box flex_box flex_align_center flex_justify_between">
        <span class="header_title">文史资料征集</span>
        <span class="header_all" @click="openHistoricalCollecMore()">全部 &gt;</span>
      </div>
      <div class="cultural_historical_collection_content">
        <div class="historical_collection_content_flex">
          <div class="cultural-history-card" v-for="item in culturalHistoryList" :key="item.id"
            @click="goToCulturalDetail(item)">
            <div class="card-title">{{ item.theme }}</div>
            <div class="card-btn-icon">
              <button class="card-btn">去看看</button>
              <img class="card-icon" :src="item.icon" alt="icon" />
            </div>
          </div>
        </div>
        <div class="historical_collection_wrapper" @click="openSubmitHistorical()">
          <button class="historical_collection_wrapper_btn">我要提交文史资料</button>
        </div>
      </div>
    </div>
    <!-- 文史馆 -->
    <div class="archives_institute">
      <img src="@/assets/img/icon_committee_living_room.png" alt="" class="archives_institute_img"
        @click="openArchivesInstitute()">
    </div>
  </div>
</template>
<script>
export default { name: 'LayoutHome' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import config from '@/config'
import { showToast } from 'vant'
import store from '@/store'
import { getAuthCode, removeTag } from '@/utils/utils'
import { isInThirdPartyApp, getDefaultUser, getEnvironmentType } from '@/utils/environment'
const router = useRouter()
const route = useRoute()

const carouselList = ref([])
const loading = ref(false)

const carouselIndex = ref(0)
let carouselTimer = null
// const touchStartX = ref(0)
// const touchEndX = ref(0)

const isPreheat = ref('')
const defaultActivityId = ref('')
const negotiationTopics = ref([])
const negotiationTopicsLoading = ref(false)

const videoGraphicLiveLoading = ref(false)
const videoGraphicLive = ref([
  {
    img: require('@/assets/img/bg_survey_list_zx.png'),
    title: '市政协召开全市“深化六个改革”重点工作宣讲会',
    id: 1
  },
  {
    img: require('@/assets/img/icon_top_bg.png'),
    title: '政协委员积极建言献策 助力城市发展',
    id: 2
  },
  {
    img: require('@/assets/img/icon_top_logo.png'),
    title: '协商议政新模式 推动社会治理创新',
    id: 3
  }
])
const vgIndex = ref(0)
let vgTimer = null
const vgTouchStartX = ref(0)
const vgTouchEndX = ref(0)

const negotiationTopicsCollectLoading = ref(false)
const negotiationtopicsCollectList = ref([])

const culturalHistoryList = ref([])
const loadingPage = ref(true)

onMounted(() => {
  document.title = '西安政协'
  // 获取i西安token
  getIXiAnToken()
})
onBeforeUnmount(() => {
  stopNewsCarousel()
  stopVGCarousel()
})
// 获取i西安的token
const getIXiAnToken = async () => {
  loadingPage.value = true

  // 检测环境，如果不在第三方app中，使用默认账号
  const inThirdPartyApp = isInThirdPartyApp()
  const envType = getEnvironmentType()

  console.log(`环境检测结果: ${inThirdPartyApp ? '第三方app' : '普通浏览器'} (${envType})`)

  if (!inThirdPartyApp) {
    console.log('当前不在第三方app环境中，使用默认账号登录')
    const publicUser = sessionStorage.getItem('public_user')
    if (!publicUser) {
      const defaultUser = getDefaultUser()
      autoLogin(defaultUser)
    } else {
      await getTopCarousel()
      loadingPage.value = false
    }
    return
  }

  // 在第三方app环境中，继续原有逻辑
  try {
    const res = await api.ixaToekn({})
    if (res.data.data) {
      getIXiAnCode(res.data.data.jsapiToken)
    } else {
      console.warn('JsapiToken获取失败，尝试使用默认账号')
      loadingPage.value = false
      setTimeout(() => {
        showToast('JsapiToken获取失败，使用默认账号登录')
        const defaultUser = getDefaultUser()
        autoLogin(defaultUser)
      }, 500)
    }
  } catch (error) {
    console.error('获取JsapiToken异常:', error)
    loadingPage.value = false
    setTimeout(() => {
      showToast('网络异常，使用默认账号登录')
      const defaultUser = getDefaultUser()
      autoLogin(defaultUser)
    }, 500)
  }
}
// 获取i西安的code
const getIXiAnCode = async (jsapiToken) => {
  // 再次检测环境，确保在第三方app中
  if (!isInThirdPartyApp()) {
    console.log('getIXiAnCode: 当前不在第三方app环境中，使用默认账号')
    const defaultUser = getDefaultUser()
    autoLogin(defaultUser)
    return
  }

  try {
    getAuthCode({
      // "appId": "9de99f6934b94cf38c75ce40c9ddf8a2", //测试应用id
      "appId": "b105ea5a2fca4b28afdd52558952552f", //正式应用id
      "forceScopes": ['ixa_user_info'], //授权的能力标识数组，
      "jsapiToken": jsapiToken //应用授权的请求码
    }, function (result) {
      if (result.code === 0) {
        console.log('✅ 第三方app授权成功')
        getIXiAnUser(result.data.authCode)
      } else {
        console.error('❌ 第三方app授权失败:', result.message)
        // 如果授权失败，降级到默认账号
        console.log('授权失败，降级使用默认账号登录')
        showToast('授权失败，使用默认账号登录')
        const defaultUser = getDefaultUser()
        autoLogin(defaultUser)
      }
    })
  } catch (error) {
    console.error('getAuthCode调用异常:', error)
    console.log('getAuthCode异常，降级使用默认账号登录')
    showToast('授权异常，使用默认账号登录')
    const defaultUser = getDefaultUser()
    autoLogin(defaultUser)
  }
}
// 获取i西安的用户信息
const getIXiAnUser = async (code) => {
  const res = await api.ixaUser({ authCode: code })
  console.log('用户信息-->' + JSON.stringify(res))
  let ixaUser = res.data.data.userInfo
  autoLogin(ixaUser)
  localStorage.setItem('ixaUser', JSON.stringify(ixaUser))
}
// 公众登录
const autoLogin = async (user) => {
  if (!user.userName) {
    loadingPage.value = false
    showToast('请您先进行实名认证！')
    setTimeout(() => {
      window.history.back()
    }, 800)
    return
  }
  try {
    const { data } = await api.login({
      grant_type: 'anonymoussso',
      userName: user.userName,
      mobile: user.phone
    }, {
      headers: {
        authorization: 'basic enlzb2Z0Onp5c29mdCo2MDc5'
      }
    })
    sessionStorage.setItem('token', data.token)
    sessionStorage.setItem('refresh_token', `bearer ${data.refreshToken.value}`)
    sessionStorage.setItem('expires', data.expires_in)
    sessionStorage.setItem('expiration', data.refreshToken.expiration)
    sessionStorage.setItem('verify', data.passwordElementMatchReg ? 1 : 0)
    loadingPage.value = false // 登录成功后loading消失
    await store.dispatch('loginUser')
    // 获取置顶资讯
    await getTopCarousel()
  } catch (error) {
    loadingPage.value = false // 登录失败也消失loading
    showToast(error.message || '登录失败')
  }
}
// 获取置顶资讯
const getTopCarousel = async () => {
  loading.value = true
  const params = {
    objectParam: {},
    pageNo: 1,
    pageSize: 5,
    keyword: '',
    query: {
      columnId: route.query.columnId || '1887325961586761729',
      moduleId: 1,
      passFlag: 1
    },
    wheres: [{ columnId: 'zy_news_content_1_is_top', queryType: 'EQ', value: '1' }],
    tableId: 'zy_news_content_1'
  }
  const { data } = await api.newsContentList(params)
  carouselList.value = data
  loading.value = false
  startNewsCarousel()
  getConfig()
}
// 获取配置
const getConfig = async () => {
  const { data } = await api.globalReadOpenConfig({ codes: ['ixian_preheat'] })
  isPreheat.value = data.ixian_preheat
  getConsultActivityList()
}
// 获取协商活动
const getConsultActivityList = async (pageSize) => {
  if (!pageSize) {
    negotiationTopicsLoading.value = true
  }
  const params = {
    pageNo: 1,
    pageSize: pageSize || 3,
    year: 2025,
    objectParam: {}
  }
  if (pageSize) {
    params.objectParam.showPublic = '1'
  } else {
    params.objectParam.ifLatest = '1'
  }
  const { data } = await api.consultActivityList(params)
  if (pageSize) {
    if (data.length > 0) {
      defaultActivityId.value = data[0].id
    } else {
      showToast('暂无设置当前协商！')
    }
  } else {
    negotiationTopics.value = data
    negotiationTopicsLoading.value = false
    getVideoGraphicLive()
    getNegotiationTopicsCollect()
  }
}
// 获取视频+图文直播
const getVideoGraphicLive = async () => {
  videoGraphicLiveLoading.value = false
  startVGCarousel()
}
// 获取协商议题征集
const getNegotiationTopicsCollect = async () => {
  negotiationTopicsCollectLoading.value = true
  const { code, data } = await api.opinioncollectList({
    pageNo: 1,
    pageSize: 1,
    collectStatus: 2,
    query: { businessCode: 'discussioncollect' }
  })
  if (code === 200) {
    const now = new Date().getTime()
    const oneDay = 24 * 60 * 60 * 1000
    const processedData = (data || []).map(item => {
      const start = new Date(item.startDate).getTime()
      const end = new Date(item.endDate).getTime()
      let statusText = ''
      let days = 0
      if (now > end) {
        statusText = '征集已结束'
        days = 0
      } else if (now > start) {
        statusText = '征集结束剩余：'
        days = Math.floor((end - now) / oneDay)
      } else {
        statusText = '征集开始还有：'
        days = Math.floor((start - now) / oneDay)
      }
      const canJoin = now > start && now < end && item.isJoinName === '未参与'
      return {
        ...item,
        statusText,
        days,
        canJoin
      }
    })
    negotiationtopicsCollectList.value = negotiationtopicsCollectList.value.concat(processedData)
    if (negotiationtopicsCollectList.value[0]) {
      negotiationtopicsCollectList.value[0].content = removeTag(negotiationtopicsCollectList.value[0].content)
    }
    negotiationTopicsCollectLoading.value = false
    getCulturalHistoricalCollection()
  }
}
// 通用轮播控制方法
const startCarousel = (timer, index, list, callback) => {
  if (timer) clearInterval(timer)
  return setInterval(() => {
    index.value = (index.value + 1) % list.value.length
  }, 30000)
}
const stopCarousel = (timer) => {
  if (timer) {
    clearInterval(timer)
    return null
  }
  return null
}
const goToCarousel = (idx, index, timer, list, callback) => {
  timer = stopCarousel(timer)
  index.value = idx
  return startCarousel(timer, index, list, callback)
}
const prevCarousel = (index, list) => {
  index.value = (index.value - 1 + list.value.length) % list.value.length
}
const nextCarousel = (index, list) => {
  index.value = (index.value + 1) % list.value.length
}
// 通用触摸处理
const onTouchStart = (e, touchStartX, touchEndX) => {
  touchStartX.value = e.touches[0].clientX
  touchEndX.value = e.touches[0].clientX
}
const onTouchMove = (e, touchEndX) => {
  touchEndX.value = e.touches[0].clientX
}
const onTouchEnd = (e, touchStartX, touchEndX, stopFn, nextFn, prevFn, startFn) => {
  const deltaX = touchEndX.value - touchStartX.value
  if (Math.abs(deltaX) > 50) {
    stopFn()
    if (deltaX < 0) {
      nextFn()
    } else {
      prevFn()
    }
    startFn()
  }
  touchStartX.value = 0
  touchEndX.value = 0
}
// 资讯轮播图相关方法
const startNewsCarousel = () => {
  carouselTimer = startCarousel(carouselTimer, carouselIndex, carouselList)
}
const stopNewsCarousel = () => {
  carouselTimer = stopCarousel(carouselTimer)
}
// 打开资讯详情
const onCarouselClick = (row) => {
  router.push({ path: '/NewDetails', query: { id: row.id } })
}
// 打开全部资讯
const openNewMore = () => {
  router.push({ path: '/NewList', query: { utype: '1' } })
}
// 跳转协商活动
const openActivety = async (_item) => {
  if (_item) {
    router.push({ path: '/NegotiationActivityPage', query: { id: _item.id } })
  } else {
    await getConsultActivityList(1)
    if (defaultActivityId.value) {
      router.push({ path: '/NegotiationActivityPage', query: { id: defaultActivityId.value } })
    }
  }
}
// 跳转网络议政
const openNetwork = () => {
  router.push({ path: '/NetworkDiscussList' })
}
// 跳转问卷调查
const openSurvey = () => {
  router.push({ path: '/QuestionnaireList' })
}
// 视频+图文直播轮播图相关方法
const startVGCarousel = () => {
  vgTimer = startCarousel(vgTimer, vgIndex, videoGraphicLive)
}
const stopVGCarousel = () => {
  vgTimer = stopCarousel(vgTimer)
}
const goToVGCarousel = (idx) => {
  vgTimer = goToCarousel(idx, vgIndex, vgTimer, videoGraphicLive)
}
const prevVGCarousel = () => {
  prevCarousel(vgIndex, videoGraphicLive)
}
const nextVGCarousel = () => {
  nextCarousel(vgIndex, videoGraphicLive)
}
const onVGTouchStart = (e) => {
  onTouchStart(e, vgTouchStartX, vgTouchEndX)
}
const onVGTouchMove = (e) => {
  onTouchMove(e, vgTouchEndX)
}
const onVGTouchEnd = (e) => {
  onTouchEnd(e, vgTouchStartX, vgTouchEndX, stopVGCarousel, nextVGCarousel, prevVGCarousel, startVGCarousel)
}
// 跳转视频+图文直播详情
const onVGCarouselClick = (_item) => {
  // 跳转或弹窗逻辑
  // router.push({ path: '/VideoDetail', query: { id: _item.id } })
}
// 跳转委员会客厅
const openCommitteeLivingRoom = () => {
  // router.push({ path: '/CommitteeLivingRoomList' })
}
// 跳转协商议题征集详情
const openCollectInfo = (_item) => {
  router.push({ path: '/NegotiationTopicsDetails', query: { id: _item.id } })
}
// 跳转政协概况
const openOverview = () => {
  window.open('https://www.xa-cppcc.gov.cn/zxgk/zyzn/4.html', '_blank');
}
// 跳转文史资料
const openHistory = () => {
  router.push({ path: '/CultureHistory' })
}
// 获取文史资料征集
const getCulturalHistoricalCollection = async () => {
  const params = {
    pageNo: 1,
    pageSize: 2,
    query: {
      channelId: '1935275534502072322',
      isDraft: 0
    },
    tableId: 'id_message_notification',
    wheres: []
  }
  const { data, code } = await api.notificationList(params)
  if (code == 200) {
    const icons = [
      require('@/assets/img/icon_culture_history01.png'),
      require('@/assets/img/icon_culture_history02.png')
    ]
    culturalHistoryList.value = (data || []).map((item, idx) => ({
      ...item,
      icon: icons[idx]
    }))
  } else {
    showToast('文史资料征集请求失败！')
  }
}
// 文史资料征集进详情
const goToCulturalDetail = (_item) => {
  router.push({ path: '/NoticeDetails', query: { id: _item.id } })
}
// 打开文史资料征集进全部
const openHistoricalCollecMore = () => {
  router.push({ path: '/NoticeList', query: {} })
}
// 提交文史资料
const openSubmitHistorical = () => {
  router.push({ path: '/CultureHistoryAdd' })
}
// 打开文史馆
const openArchivesInstitute = () => {
  router.push({ path: '/ArchiveVisitBooking' })
}
</script>
<style lang="scss">
.LayoutHome {
  width: 100%;
  height: 100%;
  overflow: auto;

  // 顶部背景和logo
  .top_bg {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 220px;
    overflow: hidden;

    .bg_img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      z-index: 1;
    }

    .logo {
      position: relative;
      z-index: 2;
      align-self: flex-start;
      margin: 32px 0 0 15px;
    }

    .top_text {
      position: relative;
      z-index: 2;
      margin-top: auto;
      margin-bottom: 43px;
    }
  }

  // 政协资讯
  .new_box {
    margin-top: -32px;
    background-image: url('@/assets/img/icon_new_bg.png');
    background-size: 100% 100%;
    height: 340px;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 3;

    .carousel {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 15px;
      height: 100%;

      .swiper-container {
        width: 100%;
        height: 100%;

        .swiper-item {
          width: 100%;

          .swiper-image-container {
            position: relative;
            width: 100%;
            height: 100%;
            box-sizing: border-box;

            .swiper-image {
              width: 100%;
              height: 180px;
              object-fit: cover;
              border-radius: 5px;
            }

            .swiper-overlay {
              padding: 5px;

              .swiper-title {
                color: #3A3A3A;
                font-size: 14px;
                margin: 0 0 8px 0;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
              }

            }
          }
        }
      }
    }
  }

  // 协商活动预热
  .negotiation_activities_preheating {
    background-image: url('@/assets/img/icon_preheat_bg.png');
    background-size: 100% 100%;
    // height: 750px;
    display: flex;
    flex-direction: column;
    position: relative;

    .negotiation_activity_preheat {
      margin: 20px 26px 0;
      flex-shrink: 0;

      .negotiation_activity_preheat_img {
        width: 100%;
        height: auto;
        display: block;
      }
    }

    .negotiation_topics {
      background: #EDF7FF;
      border-radius: 8px;
      box-shadow: 0px 0px 10px 0px rgba(107, 108, 109, 0.18);
      padding: 14px 12px;
      margin: 20px 26px 15px 26px;

      .negotiation_topics_title {
        font-size: 16px;
        color: #3368C6;
        margin-bottom: 12px;
        letter-spacing: 1px;
      }

      .negotiation_topics_content {
        display: grid;
        grid-template-columns: 1.2fr 1fr;
        grid-template-rows: 1fr 1fr;
        gap: 10px;
        margin-bottom: 15px;

        .negotiation-topic-card {
          background: #fff;
          border-radius: 8px;
          box-shadow: 0 2px 12px 0 rgba(51, 104, 198, 0.06);
          padding: 10px;
          display: flex;
          flex-direction: column;
          align-items: flex-start;

          .topic-title {
            font-size: 12px;
            color: #000;
            line-height: 1.4;
          }

          .topic-image {
            width: 74px;
            height: 62px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: auto;

            img {
              width: 100%;
              height: 100%;
              object-fit: contain;
            }
          }

          .sub-topic-btn {
            display: flex;
            align-items: flex-end;
            justify-content: space-between;
            width: 100%;
          }

          .topic-btn {
            margin: 0 auto;
            background: #3368C6;
            border-radius: 20px;
            padding: 2px 10px;
            font-size: 12px;
            border: none;
            color: #fff;
          }
        }

        .main {
          grid-row: 1 / span 2;
          grid-column: 1 / 2;
          min-width: 0;
          width: 115px;
          background: #E3EFFF;

          .topic-title {
            font-size: 12px;
            color: #000;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 4;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .topic-image {
            width: 74px;
            height: 62px;
          }
        }

        .sub {
          grid-column: 2 / 3;
          width: 170px;
          background: #FFFFFF;
          border-radius: 8px;

          .topic-title {
            font-size: 12px;
            color: #000;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .topic-image {
            width: 26px;
            height: 33px;
          }
        }
      }

      .negotiation-submit-btn-wrapper {
        display: flex;
        justify-content: center;
        margin-bottom: 0;

        .negotiation-submit-btn {
          background: linear-gradient(270deg, #4E80D1 0%, #C2E6FF 100%);
          color: #fff;
          font-size: 15px;
          border: none;
          border-radius: 6px;
          padding: 8px 0;
          width: 100%;
          max-width: 480px;
          letter-spacing: 1px;
        }
      }
    }

    .custom-action-cards {
      display: flex;
      flex-direction: row;
      gap: 16px;
      margin: 0 26px 15px 26px;

      .action-card {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-image: url('@/assets/img/icon_participate_bg.png');
        background-size: 100% 100%;
        padding: 20px 18px 20px 12px;
        position: relative;
      }

      .action-card-content {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-right: 10px;
      }

      .action-title {
        font-size: 14px;
        color: #3368C6;
        font-weight: 500;
      }

      .action-arrow {
        width: 4px;
        height: 5px;
      }

      .action-icon {
        width: 17px;
        height: 17px;
      }
    }

    .video_graphic_live {
      background: #EDF7FF;
      border-radius: 8px;
      box-shadow: 0px 0px 10px 0px rgba(107, 108, 109, 0.18);
      padding: 10px 12px;
      margin: 0px 26px 30px 26px;
      height: 200px;

      .video_graphic_live_title {
        font-size: 16px;
        color: #3368C6;
        margin-bottom: 12px;
        letter-spacing: 1px;
      }

      .video_graphic_live_content {
        position: relative;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .vg-carousel-img-wrapper {
          position: relative;
          width: 100%;
          height: 150px;
          border-radius: 10px;
          overflow: hidden;
          box-shadow: 0 2px 12px 0 rgba(51, 104, 198, 0.08);
          cursor: pointer;
          transition: box-shadow 0.3s;
          background: #fff;

          .vg-carousel-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
          }

          .vg-carousel-bottom-bar {
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: rgba(0, 0, 0, 0.45);
            padding: 0 16px 0 16px;
            height: 38px;
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
          }

          .vg-carousel-title {
            color: #fff;
            font-size: 13px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 80%;
          }

          .vg-carousel-dots {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 0 0 0 16px;

            .vg-dot {
              width: 4px;
              height: 4px;
              background: #C2E6FF;
              border-radius: 2px;
            }

            .vg-dot.active {
              width: 13px;
              height: 3px;
              background: #3368C6;
            }
          }
        }
      }
    }
  }

  // 委员会客厅
  .committee_living_room {
    margin: 4px 8px 8px;

    .committee_living_room_img {
      width: 100%;
      height: 100%;
    }
  }

  // 协商议题征集
  .negotiation_topics_collect {
    background-image: url('@/assets/img/negotiation_topics_collect_bg.png');
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    min-height: 344px;

    .negotiation_topics_collect_box {
      background: #EDF7FF;
      border-radius: 8px;
      box-shadow: 0px 0px 10px 0px rgba(107, 108, 109, 0.18);
      padding: 10px 12px;
      margin: 26px 26px 30px 26px;
      min-height: 270px;

      .topics_collect_title {
        font-size: 16px;
        color: #3368C6;
        text-align: center;
        font-weight: bold;
      }

      .topics_collect_content {
        font-size: 15px;
        color: #333;
        line-height: 24px;
        margin-top: 20px;
        display: -webkit-box;
        -webkit-line-clamp: 8;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .topics_collect_organize {
        font-size: 13px;
        color: #999999;
        margin-top: 20px;
      }

      .topics_collect_flex {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 13px;
        margin-top: 10px;

        .topics_collect_flex_text1 {
          font-size: 13px;
          color: #DA1718;
        }

        .topics_collect_flex_text2 {
          font-size: 13px;
          color: #333333;
        }
      }

      .negotiation-submit-btn-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 16px;

        .negotiation-submit-btn {
          background: linear-gradient(270deg, #4E80D1 0%, #C2E6FF 100%);
          color: #fff;
          font-size: 15px;
          border: none;
          border-radius: 6px;
          padding: 8px 0;
          width: 100%;
          max-width: 480px;
          letter-spacing: 1px;
        }
      }
    }
  }

  // 政协概况与文史资料卡片区域
  .overview_history_card {
    display: flex;
    flex-direction: row;
    gap: 16px;
    margin: 10px 10px;

    .action_card {
      padding: 20px 18px 20px 12px;
      position: relative;
      height: 94px;
      width: 50%;

      .overview_history_card_content {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        margin-right: 10px;

        .action_title {
          font-size: 17px;
          font-weight: bold;
          color: #3368C6;
        }

        .action_info {
          background: #3368C6;
          border-radius: 20px;
          font-size: 12px;
          color: #FFFFFF;
          line-height: 18px;
          padding: 1px 8px;
        }
      }
    }

    .overview {
      background-image: url('@/assets/img/icon_overview_bg.png');
      background-size: 100% 100%;
    }

    .history {
      background-image: url('@/assets/img/icon_history_bg.png');
      background-size: 100% 100%;
    }
  }

  // 文史资料征集
  .cultural_historical_collection {
    background-image: url('@/assets/img/icon_cultural_historical_collection_bg.png');
    background-size: 100% 100%;
    height: 240px;
    margin: 0 10px;

    .cultural_historical_collection_content {

      .historical_collection_content_flex {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        padding: 20px 15px 5px 15px;

        .cultural-history-card {
          background: #EDF7FF;
          border-radius: 12px;
          box-shadow: 0 2px 8px rgba(51, 104, 198, 0.08);
          padding: 10px 12px 6px 12px;
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: flex-start;

          .card-title {
            font-size: 14px;
            color: #333;
            font-weight: bold;
            margin-bottom: 16px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            // min-height: 40px;
          }

          .card-btn-icon {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;

            .card-btn {
              background: #3368C6;
              color: #fff;
              border: none;
              border-radius: 16px;
              padding: 4px 10px;
              font-size: 13px;
              align-self: flex-start;
            }

            .card-icon {
              width: 26px;
              height: 26px;
            }
          }
        }
      }

      .historical_collection_wrapper {
        display: flex;
        justify-content: center;
        margin: 15px 30px;

        .historical_collection_wrapper_btn {
          background: linear-gradient(270deg, #4E80D1 0%, #C2E6FF 100%);
          color: #fff;
          font-size: 15px;
          border: none;
          border-radius: 6px;
          padding: 8px 0;
          width: 100%;
          max-width: 480px;
          letter-spacing: 1px;
        }
      }
    }
  }

  // 文史馆
  .archives_institute {
    margin: 4px 10px;

    .archives_institute_img {
      width: 100%;
      height: 100%;
    }
  }

  .header_box {
    padding: 20px 25px 0;
    z-index: 4;

    .header_title {
      font-size: 18px;
      font-weight: bold;
      color: #000;
      letter-spacing: 1px;
      display: inline-block;
      position: relative;
    }

    .header_title::after {
      content: '';
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      width: 20px;
      height: 3px;
      background: #3368C6;
      border-radius: 2px;
      margin-top: 4px;
      margin-bottom: 2px;
      bottom: -9px;
    }

    .header_all {
      font-size: 14px;
      color: #999999;
    }
  }

  .loading {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #8d8d8d;
    padding: 42px 15px 20px;

    .loading_spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #e0e0e0;
      border-top: 4px solid #3368C6;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      display: inline-block;
    }
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
}

// 全局loading遮罩样式
.global-loading-mask {
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.85);
}

.global-loading-spinner {
  width: 48px;
  height: 48px;
  border: 5px solid #e0e0e0;
  border-top: 5px solid #3368C6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.global-loading-text {
  font-size: 18px;
  color: #3368C6;
}

.van-swipe__indicators {
  bottom: 10px !important;
}

.van-swipe__indicator {
  width: 18px;
  height: 6px;
  border-radius: 3px;
  background: #D8E0F3 !important;
}

.van-swipe__indicator--active {
  background: #3368C6 !important;
}
</style>
