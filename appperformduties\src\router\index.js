import { createRouter, createWebHashHistory } from 'vue-router'

const LoginView = () => import('@/views/LoginView/LoginView.vue')
const LayoutHome = () => import('@/views/LayoutHome/LayoutHome.vue')
const NewList = () => import('@/views/NewList/NewList.vue') // 新闻资讯列表
const NewDetails = () => import('@/views/NewList/NewDetails.vue') // 新闻资讯详情
const NegotiationActivityPage = () => import('@/views/NegotiationActivityPage/NegotiationActivityPage.vue') // 协商活动
const NetworkDiscussList = () => import('@/views/NetworkDiscussList/NetworkDiscussList.vue') // 网络议政列表
const NetworkDiscussDetails = () => import('@/views/NetworkDiscussList/NetworkDiscussDetails.vue') // 网络议政详情
const NegotiationTopicsDetails = () => import('@/views/NetworkDiscussList/NegotiationTopicsDetails.vue') // 协商议题征集详情
const QuestionnaireList = () => import('@/views/QuestionnaireList/QuestionnaireList.vue') // 问卷调查列表
const QuestionnaireDetails = () => import('@/views/QuestionnaireList/QuestionnaireDetails.vue') // 问卷调查详情
const CommitteeLivingRoomList = () => import('@/views/CommitteeLivingRoom/CommitteeLivingRoomList.vue') // 委员会客厅列表
const CommitteeLivingRoomDetails = () => import('@/views/CommitteeLivingRoom/CommitteeLivingRoomDetails.vue') // 委员会客厅详情
const CommitteeLivingRoomMessage = () => import('@/views/CommitteeLivingRoom/CommitteeLivingRoomMessage.vue') // 委员会客厅留言
const CultureHistory = () => import('@/views/CultureHistory/CultureHistory.vue') // 史料征集
const CultureHistoryDetails = () => import('@/views/CultureHistory/CultureHistoryDetails.vue') // 史料征集详情
const CultureHistoryAdd = () => import('@/views/CultureHistory/CultureHistoryAdd.vue') // 史料征集新增
const NoticeList = () => import('@/views/NoticeList/NoticeList.vue') // 通知公告列表
const NoticeDetails = () => import('@/views/NoticeList/NoticeDetails.vue') // 通知公告详情
const NegotiationActivityList = () => import('@/views/NegotiationActivityPage/NegotiationActivityList.vue') // 通知公告详情
const ArchiveVisitBooking = () => import('@/views/ArchiveVisitBooking/ArchiveVisitBooking.vue') // 文史馆

const EnvironmentTest = () => import('@/views/EnvironmentTest.vue') // 环境检测测试页面

// const casualPhoto = () => import('@/views/casualPhoto/casualPhoto')
// const addCasualPhoto = () => import('@/views/casualPhoto/addCasualPhoto')
// const casualPhotoList = () => import('@/views/casualPhoto/casualPhotoList')
// const casualPhotoDetails = () => import('@/views/casualPhoto/casualPhotoDetails')
// const addAnswer = () => import('@/views/casualPhoto/addAnswer')
// const addAssigned = () => import('@/views/casualPhoto/addAssigned')
// const smartPage = () => import('@/views/casualPhoto/smartPage')
const routes = [
  { path: '/LoginView', name: 'LoginView', component: LoginView },
  { path: '/LayoutHome', name: 'LayoutHome', component: LayoutHome },
  { path: '/NewList', name: 'NewList', component: NewList },
  { path: '/NewDetails', name: 'NewDetails', component: NewDetails },
  { path: '/NegotiationActivityPage', name: 'NegotiationActivityPage', component: NegotiationActivityPage },
  { path: '/NetworkDiscussList', name: 'NetworkDiscussList', component: NetworkDiscussList },
  { path: '/NetworkDiscussDetails', name: 'NetworkDiscussDetails', component: NetworkDiscussDetails },
  { path: '/NegotiationTopicsDetails', name: 'NegotiationTopicsDetails', component: NegotiationTopicsDetails },
  { path: '/QuestionnaireList', name: 'QuestionnaireList', component: QuestionnaireList },
  { path: '/QuestionnaireDetails', name: 'QuestionnaireDetails', component: QuestionnaireDetails },
  { path: '/CommitteeLivingRoomList', name: 'CommitteeLivingRoomList', component: CommitteeLivingRoomList },
  { path: '/CommitteeLivingRoomDetails', name: 'CommitteeLivingRoomDetails', component: CommitteeLivingRoomDetails },
  { path: '/CommitteeLivingRoomMessage', name: 'CommitteeLivingRoomMessage', component: CommitteeLivingRoomMessage },
  { path: '/CultureHistory', name: 'CultureHistory', component: CultureHistory },
  { path: '/CultureHistoryDetails', name: 'CultureHistoryDetails', component: CultureHistoryDetails },
  { path: '/CultureHistoryAdd', name: 'CultureHistoryAdd', component: CultureHistoryAdd },
  { path: '/NoticeList', name: 'NoticeList', component: NoticeList },
  { path: '/NoticeDetails', name: 'NoticeDetails', component: NoticeDetails },
  { path: '/NegotiationActivityList', name: 'NegotiationActivityList', component: NegotiationActivityList },
  { path: '/ArchiveVisitBooking', name: 'ArchiveVisitBooking', component: ArchiveVisitBooking },
  { path: '/EnvironmentTest', name: 'EnvironmentTest', component: EnvironmentTest },

  // { path: '/', name: 'casualPhoto', component: casualPhoto },
  // { path: '/addCasualPhoto', name: 'addCasualPhoto', component: addCasualPhoto },
  // { path: '/casualPhotoList', name: 'casualPhotoList', component: casualPhotoList },
  // { path: '/casualPhotoDetails', name: 'casualPhotoDetails', component: casualPhotoDetails },
  // { path: '/addAnswer', name: 'addAnswer', component: addAnswer },
  // { path: '/addAssigned', name: 'addAssigned', component: addAssigned },
  // { path: '/smartPage', name: 'smartPage', component: smartPage },
]
const history = createWebHashHistory()
const router = createRouter({ history, routes })

export { history }
export default router
