{"name": "ixian", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "amfe-flexible": "^2.2.1", "animate.css": "^4.1.1", "axios": "^1.9.0", "clipboard": "^2.0.10", "core-js": "^3.8.3", "crypto-js": "^4.1.1", "file-saver": "^2.0.5", "gm-crypto": "^0.1.12", "postcss-pxtorem": "^6.1.0", "qs": "^6.14.0", "vant": "^4.9.19", "vconsole": "^3.15.1", "vue": "^3.4.38", "vue-router": "^4.3.2", "vuex": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "sass": "^1.32.7", "sass-loader": "^12.0.0", "unplugin-vue-components": "^0.25.2"}}