import { createStore } from 'vuex'
import router from '@/router'
import api from '@/api'
import menuData from './HTTP_MENU.json'

const stateData = {
  theme: {},
  user: {},
  menu: menuData
}
export default createStore({
  state: stateData,
  getters: {
    getThemeFn (state) {
      return state.theme
    },
    getUserFn (state) {
      return state.user || JSON.parse(sessionStorage.getItem('user'))
    },
    getMenuFn (state) {
      return state.menu || JSON.parse(sessionStorage.getItem('menu'))
    }
  },
  mutations: {
    setState (state) {
      for (const key in stateData) {
        if (Object.prototype.hasOwnProperty.call(stateData, key)) {
          state[key] = stateData[key]
        }
      }
    },
    setTheme (state, theme = {}) {
      state.theme = theme
    },
    setUser (state, user = {}) {
      state.user = user
    },
    setMenu (state, menu = []) {
      state.menu = menu
    }
  },
  actions: {
    async loginUser ({ commit, dispatch }, type) {
      const { data: user } = await api.loginUser()
      sessionStorage.setItem('user', JSON.stringify(user))
      sessionStorage.setItem('AreaId', user.areaId)
      commit('setUser', user)
      dispatch('loginMenu')
      if (type) {
        if (['login'].includes(type)) {
          router.push({ path: '/' })
        } else {
          router.push({ path: type })
        }
      }
    },
    async loginMenu () {
      // const { data: menu } = await api.loginMenu()
      // if (!menu?.length && !['FilePrevieOpen'].includes(router?.currentRoute?.value?.name)) router.push({ path: '/NotFoundPage' })
      // sessionStorage.setItem('menu', JSON.stringify(menu))
      // commit('setMenu', menu)
    }
  }
})
