<template>
  <div class="commentItem">
    <div class=" flex_box">
      <van-image :src="itemData.headImg"
                 alt="用户头像"
                 fit="cover"
                 class="avatar" />
      <div class="details flex_box">
        <div class="flex_box">
          <div class="name">{{ itemData.commentUserName }}</div>
        </div>
        <div class="flex_placeholder"></div>
        <div class="flex_box">
          <div class="flex_box commentBox"
               @click.stop="handlePraises(itemData)">
            <van-icon :name="itemData.hasClickPraises ? 'good-job' : 'good-job-o'"
                      :class="{ 'active': itemData.hasClickPraises }" />
            <div class="count"
                 :class="{ 'active': itemData.hasClickPraises }">{{ itemData.praisesCount || 0 }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="content">
      <p>{{ itemData.commentContent }}</p>
    </div>
    <div class="flex_box">
      <div class="time">{{ formatDate(itemData.createDate) }}</div>
      <div class="answer"
           v-if="user.id !== itemData.createBy"
           @click.stop="reply(itemData)">回复</div>
      <div class="flex_placeholder"></div>
      <van-icon @click.stop="deleteComment(itemData)"
                v-if="user.id === itemData.createBy"
                name="delete-o" />
    </div>
    <div class="footer">
      <!-- <div class="flex_box reply-imgBox">
        <van-image v-for="(item, index) in itemData.attachmentIds"
                   :src="item.url"
                   :key="item"
                   class="reply-img"
                   fit="cover"
                   @click.stop="openImagePreview(index)"></van-image>
      </div> -->
    </div>
    <div class="commentBox"
         v-if="itemData.children && itemData.children.length">
      <div v-for="item in itemData.children"
           :key="item.id">
        <div class="flex_box">
          <van-image :src="item.headImg"
                     alt="用户头像"
                     fit="cover"
                     class="avatar" />
          <div class="name">{{ item.commentUserName }}</div>
          <div class="flex_placeholder"></div>
        </div>
        <div class="content">{{ item.commentContent }}</div>
        <div class="flex_box">
          <div class="time">{{ formatDate(item.createDate) }}</div>
          <div class="answer"
               v-if="user.id !== item.createBy"
               @click.stop="reply(item)">回复</div>
          <div class="flex_placeholder"></div>
          <van-icon @click.stop="deleteComment(item, itemData)"
                    v-if="user.id === item.createBy"
                    name="delete-o" />
        </div>
      </div>

    </div>
    <van-popup v-model:show="commentShow"
               position="bottom">
      <commentInput :businessCode="'convenientlyPat'"
                    :pid="pid"
                    :id="props.id"
                    :placeholder="placeholderText"
                    @callback="callbacks"></commentInput>
    </van-popup>
  </div>
</template>
<script>
export default { name: 'commentItem' }
</script>
<script setup>
import api from '@/api'
import { ref } from 'vue'
// import { showImagePreview } from 'vant'
import { showToast, showConfirmDialog } from 'vant'
import { formatDate } from '@/assets/js/utils.js'
import commentInput from './commentInput.vue'
const props = defineProps({
  itemData: {
    type: Object,
    default: () => { }
  },
  id: {
    type: String,
    default: () => { }
  }
})
const emit = defineEmits(['callback'])
const commentShow = ref(false)
const pid = ref('')
const placeholderText = ref()
const user = ref(JSON.parse(sessionStorage.getItem('user')) || '')
const handlePraises = async (itemData) => {
  const params = {
    form: {
      relationId: itemData.id,
      userId: user.value.id,
    }
  }
  itemData.hasClickPraises = !itemData.hasClickPraises
  if (itemData.hasClickPraises) {
    itemData.praisesCount++
    await api.zyConvenientlyBrowseAdd(params)
  } else {
    itemData.praisesCount--
    await api.zyConvenientlyBrowseDels(params)
  }
}
const deleteComment = (itemData, pInfo) => {
  showConfirmDialog({
    title: '提示',
    confirmButtonColor: '#4488EB',
    message: '是否删除？'
  }).then(async () => {
    const { code } = await api.zyConvenientlyCommentDels({ ids: [itemData.id] })
    if (code === 200) {
      emit('callback', itemData.id)
      if (pInfo) {
        emit('reload', pInfo)
      }
      showToast('删除成功')
    }
  })
}
const callbacks = (data) => {
  commentShow.value = false
  emit('reload', data)
}
const reply = (itemData) => {
  pid.value = itemData.id
  placeholderText.value = `回复${itemData.commentUserName}`
  commentShow.value = true
}

// const openImagePreview = (index) => {
//   const imgList = props.itemData.replydata.imgList.map(item => item.url)
//   showImagePreview({
//     images: imgList,
//     startPosition: index
//   })
// }

</script>
<style lang="scss">
.commentItem {
  padding: 16px;
  background-color: #fff;
  border-top: 1px solid #F4F4F4;

  .avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 8px;
    overflow: hidden;
  }

  .details {
    width: 100%;
    flex: 1;

    .name {
      font-weight: 600;
      font-size: 15px;
      color: #333333;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

  }

  .time {
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .answer {
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-left: 15px;
  }

  .commentBox {
    margin-left: 15px;

    .count {
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #999999;
      line-height: 12px;
      text-align: left;
      margin-left: 5px;
    }

    .active {
      color: #4488EB;
    }
  }

  .content {
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-bottom: 8px;
    padding-left: 40px;
  }

  .commentBox {
    margin-top: 10px;
    background: #F4F4F4;
    padding: 5px;
    border-radius: 5px;
  }

  .footer {
    font-size: 12px;
    color: #888;

    .reply {
      margin-top: 8px;
      padding: 14px;
      background: #F1F1F1;
      border-radius: 8px 8px 8px 8px;
    }

    .reply-imgBox {
      flex-wrap: wrap;
    }

    .reply-img {
      width: 73px;
      height: 73px;
      border-radius: 0px 0px 0px 0px;
      margin-bottom: 8px;
      margin-right: 4px;
    }
  }

}
</style>
