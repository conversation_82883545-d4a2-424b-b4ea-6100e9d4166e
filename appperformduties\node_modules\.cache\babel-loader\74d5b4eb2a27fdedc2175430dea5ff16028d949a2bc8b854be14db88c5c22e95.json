{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createStaticVNode as _createStaticVNode } from \"vue\";\nimport _imports_0 from '../../assets/img/icon_museum.png';\nvar _hoisted_1 = {\n  class: \"archive-visit-booking\"\n};\nvar _hoisted_2 = {\n  class: \"section\"\n};\nvar _hoisted_3 = {\n  class: \"date-grid flex_box\"\n};\nvar _hoisted_4 = [\"onClick\"];\nvar _hoisted_5 = {\n  class: \"day\"\n};\nvar _hoisted_6 = {\n  class: \"date\"\n};\nvar _hoisted_7 = {\n  class: \"status\"\n};\nvar _hoisted_8 = {\n  class: \"section\"\n};\nvar _hoisted_9 = {\n  class: \"time-slots\"\n};\nvar _hoisted_10 = [\"onClick\"];\nvar _hoisted_11 = {\n  class: \"time\"\n};\nvar _hoisted_12 = {\n  class: \"capacity\"\n};\nvar _hoisted_13 = {\n  key: 0,\n  class: \"active-indicator\"\n};\nvar _hoisted_14 = {\n  class: \"section\"\n};\nvar _hoisted_15 = {\n  class: \"upload-container\"\n};\nvar _hoisted_16 = {\n  class: \"upload-placeholder\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_van_field = _resolveComponent(\"van-field\");\n  var _component_van_icon = _resolveComponent(\"van-icon\");\n  var _component_van_uploader = _resolveComponent(\"van-uploader\");\n  var _component_van_cell_group = _resolveComponent(\"van-cell-group\");\n  var _component_van_picker = _resolveComponent(\"van-picker\");\n  var _component_van_popup = _resolveComponent(\"van-popup\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 选择入馆日期 \"), _createElementVNode(\"div\", _hoisted_2, [_cache[8] || (_cache[8] = _createStaticVNode(\"<div class=\\\"section-header flex_box flex_justify_between\\\" data-v-b40b4332><div class=\\\"flex_box flex_align_center\\\" data-v-b40b4332><img src=\\\"\" + _imports_0 + \"\\\" alt=\\\"\\\" class=\\\"icon_museum\\\" data-v-b40b4332><div class=\\\"section-title\\\" data-v-b40b4332>选择入馆日期</div></div><div class=\\\"year\\\" data-v-b40b4332>2025年</div></div>\", 1)), _createElementVNode(\"div\", _hoisted_3, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.dateList, function (date) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: _normalizeClass([\"date-item\", {\n        active: date.active,\n        disabled: date.disabled\n      }]),\n      key: date.day,\n      onClick: function onClick($event) {\n        return $setup.selectDate(date);\n      }\n    }, [_createElementVNode(\"div\", _hoisted_5, _toDisplayString(date.day), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_6, _toDisplayString(date.date), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_7, _toDisplayString(date.status), 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_4);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 选择入馆时段 \"), _createElementVNode(\"div\", _hoisted_8, [_cache[9] || (_cache[9] = _createElementVNode(\"div\", {\n    class: \"section-header flex_box flex_justify_between\"\n  }, [_createElementVNode(\"div\", {\n    class: \"flex_box flex_align_center\"\n  }, [_createElementVNode(\"img\", {\n    src: _imports_0,\n    alt: \"\",\n    class: \"icon_museum\"\n  }), _createElementVNode(\"div\", {\n    class: \"section-title\"\n  }, \"选择入馆时段\")])], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_9, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.timeSlots, function (slot) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: _normalizeClass([\"time-slot\", {\n        active: slot.active\n      }]),\n      key: slot.id,\n      onClick: function onClick($event) {\n        return $setup.selectTimeSlot(slot);\n      }\n    }, [_createElementVNode(\"div\", _hoisted_11, _toDisplayString(slot.time), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_12, _toDisplayString(slot.status) + \" (\" + _toDisplayString(slot.count) + \"人)\", 1 /* TEXT */), slot.active ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13)) : _createCommentVNode(\"v-if\", true)], 10 /* CLASS, PROPS */, _hoisted_10);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 预约人员信息 \"), _createElementVNode(\"div\", _hoisted_14, [_cache[11] || (_cache[11] = _createElementVNode(\"div\", {\n    class: \"section-header flex_box flex_justify_between\"\n  }, [_createElementVNode(\"div\", {\n    class: \"flex_box flex_align_center\"\n  }, [_createElementVNode(\"span\", {\n    class: \"section-line\"\n  }), _createElementVNode(\"div\", {\n    class: \"section-title\"\n  }, \"预约人员信息\")])], -1 /* CACHED */)), _createVNode(_component_van_cell_group, {\n    inset: \"\",\n    class: \"form-group\"\n  }, {\n    default: _withCtx(function () {\n      return [_createCommentVNode(\" 联系人 \"), _createVNode(_component_van_field, {\n        modelValue: $setup.contactName,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n          return $setup.contactName = $event;\n        }),\n        label: \"联系人\",\n        placeholder: \"请输入姓名\",\n        required: \"\",\n        \"label-width\": \"80px\",\n        class: \"form-field\"\n      }, null, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 联系方式 \"), _createVNode(_component_van_field, {\n        modelValue: $setup.contactPhone,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n          return $setup.contactPhone = $event;\n        }),\n        label: \"联系方式\",\n        placeholder: \"请输入手机号\",\n        required: \"\",\n        \"label-width\": \"80px\",\n        class: \"form-field\"\n      }, null, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 团体名称 \"), _createVNode(_component_van_field, {\n        modelValue: $setup.groupName,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n          return $setup.groupName = $event;\n        }),\n        label: \"团体名称\",\n        placeholder: \"请输入单位名称\",\n        required: \"\",\n        \"label-width\": \"80px\",\n        class: \"form-field\"\n      }, null, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 团体性质 \"), _createVNode(_component_van_field, {\n        modelValue: $setup.groupTypeText,\n        \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n          return $setup.groupTypeText = $event;\n        }),\n        label: \"团体性质\",\n        placeholder: \"请选择团体性质\",\n        readonly: \"\",\n        clickable: \"\",\n        required: \"\",\n        \"label-width\": \"80px\",\n        class: \"form-field\",\n        onClick: _cache[4] || (_cache[4] = function ($event) {\n          return $setup.showGroupTypePicker = true;\n        })\n      }, null, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 附件 \"), _createVNode(_component_van_field, {\n        label: \"附件\",\n        required: \"\",\n        \"label-width\": \"80px\",\n        class: \"form-field upload-field\"\n      }, {\n        input: _withCtx(function () {\n          return [_createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_van_uploader, {\n            modelValue: $setup.attachmentFiles,\n            \"onUpdate:modelValue\": _cache[5] || (_cache[5] = function ($event) {\n              return $setup.attachmentFiles = $event;\n            }),\n            \"max-count\": 3,\n            \"after-read\": $setup.onAttachmentRead,\n            \"after-delete\": $setup.onAttachmentDelete,\n            accept: \"image/*\",\n            class: \"image-uploader\",\n            multiple: \"\",\n            \"preview-size\": 80,\n            \"upload-icon\": 'plus'\n          }, {\n            default: _withCtx(function () {\n              return [_createElementVNode(\"div\", _hoisted_16, [_createVNode(_component_van_icon, {\n                name: \"plus\",\n                size: \"20\",\n                color: \"#ff6b35\"\n              }), _cache[10] || (_cache[10] = _createElementVNode(\"div\", {\n                class: \"upload-text\"\n              }, \"上传图片\", -1 /* CACHED */))])];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])])];\n        }),\n        _: 1 /* STABLE */\n      })];\n    }),\n    _: 1 /* STABLE */\n  })]), _createCommentVNode(\" 底部按钮 \"), _cache[12] || (_cache[12] = _createElementVNode(\"div\", {\n    class: \"submit-buttons\"\n  }, [_createElementVNode(\"div\", {\n    class: \"btn\"\n  }, \"提交预约\"), _createElementVNode(\"div\", {\n    class: \"btn\"\n  }, \"我的预约\")], -1 /* CACHED */)), _createCommentVNode(\" 团体性质选择弹窗 \"), _createVNode(_component_van_popup, {\n    show: $setup.showGroupTypePicker,\n    \"onUpdate:show\": _cache[7] || (_cache[7] = function ($event) {\n      return $setup.showGroupTypePicker = $event;\n    }),\n    position: \"bottom\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_van_picker, {\n        columns: $setup.groupTypeOptions,\n        onConfirm: $setup.onGroupTypeConfirm,\n        onCancel: _cache[6] || (_cache[6] = function ($event) {\n          return $setup.showGroupTypePicker = false;\n        })\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"show\"])]);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_Fragment", "_renderList", "$setup", "dateList", "date", "_normalizeClass", "active", "disabled", "key", "day", "onClick", "$event", "selectDate", "_hoisted_5", "_toDisplayString", "_hoisted_6", "_hoisted_7", "status", "_hoisted_8", "src", "alt", "_hoisted_9", "timeSlots", "slot", "id", "selectTimeSlot", "_hoisted_11", "time", "_hoisted_12", "count", "_hoisted_13", "_hoisted_14", "_createVNode", "_component_van_cell_group", "inset", "_component_van_field", "contactName", "label", "placeholder", "required", "contactPhone", "groupName", "groupTypeText", "readonly", "clickable", "_cache", "showGroupTypePicker", "input", "_withCtx", "_hoisted_15", "_component_van_uploader", "attachmentFiles", "onAttachmentRead", "onAttachmentDelete", "accept", "multiple", "default", "_hoisted_16", "_component_van_icon", "name", "size", "color", "_component_van_popup", "show", "position", "_component_van_picker", "columns", "groupTypeOptions", "onConfirm", "onGroupTypeConfirm", "onCancel"], "sources": ["D:\\zy\\xm\\h5\\i西安\\appperformduties\\src\\views\\ArchiveVisitBooking\\ArchiveVisitBooking.vue"], "sourcesContent": ["<template>\r\n  <div class=\"archive-visit-booking\">\r\n    <!-- 选择入馆日期 -->\r\n    <div class=\"section\">\r\n      <div class=\"section-header flex_box flex_justify_between\">\r\n        <div class=\"flex_box flex_align_center\">\r\n          <img src=\"../../assets/img/icon_museum.png \" alt=\"\" class=\"icon_museum\">\r\n          <div class=\"section-title\">选择入馆日期</div>\r\n        </div>\r\n        <div class=\"year\">2025年</div>\r\n      </div>\r\n      <div class=\"date-grid flex_box\">\r\n        <div class=\"date-item\" v-for=\"date in dateList\" :key=\"date.day\"\r\n          :class=\"{ active: date.active, disabled: date.disabled }\" @click=\"selectDate(date)\">\r\n          <div class=\"day\">{{ date.day }}</div>\r\n          <div class=\"date\">{{ date.date }}</div>\r\n          <div class=\"status\">{{ date.status }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 选择入馆时段 -->\r\n    <div class=\"section\">\r\n      <div class=\"section-header flex_box flex_justify_between\">\r\n        <div class=\"flex_box flex_align_center\">\r\n          <img src=\"../../assets/img/icon_museum.png \" alt=\"\" class=\"icon_museum\">\r\n          <div class=\"section-title\">选择入馆时段</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"time-slots\">\r\n        <div class=\"time-slot\" v-for=\"slot in timeSlots\" :key=\"slot.id\" :class=\"{ active: slot.active }\"\r\n          @click=\"selectTimeSlot(slot)\">\r\n          <div class=\"time\">{{ slot.time }}</div>\r\n          <div class=\"capacity\">{{ slot.status }} ({{ slot.count }}人)</div>\r\n          <div v-if=\"slot.active\" class=\"active-indicator\"></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 预约人员信息 -->\r\n    <div class=\"section\">\r\n      <div class=\"section-header flex_box flex_justify_between\">\r\n        <div class=\"flex_box flex_align_center\">\r\n          <span class=\"section-line\"></span>\r\n          <div class=\"section-title\">预约人员信息</div>\r\n        </div>\r\n      </div>\r\n      <van-cell-group inset class=\"form-group\">\r\n        <!-- 联系人 -->\r\n        <van-field v-model=\"contactName\" label=\"联系人\" placeholder=\"请输入姓名\" required label-width=\"80px\"\r\n          class=\"form-field\" />\r\n\r\n        <!-- 联系方式 -->\r\n        <van-field v-model=\"contactPhone\" label=\"联系方式\" placeholder=\"请输入手机号\" required label-width=\"80px\"\r\n          class=\"form-field\" />\r\n\r\n        <!-- 团体名称 -->\r\n        <van-field v-model=\"groupName\" label=\"团体名称\" placeholder=\"请输入单位名称\" required label-width=\"80px\"\r\n          class=\"form-field\" />\r\n\r\n        <!-- 团体性质 -->\r\n        <van-field v-model=\"groupTypeText\" label=\"团体性质\" placeholder=\"请选择团体性质\" readonly clickable required\r\n          label-width=\"80px\" class=\"form-field\" @click=\"showGroupTypePicker = true\" />\r\n\r\n        <!-- 附件 -->\r\n        <van-field label=\"附件\" required label-width=\"80px\" class=\"form-field upload-field\">\r\n          <template #input>\r\n            <div class=\"upload-container\">\r\n              <van-uploader v-model=\"attachmentFiles\" :max-count=\"3\" :after-read=\"onAttachmentRead\"\r\n                :after-delete=\"onAttachmentDelete\" accept=\"image/*\" class=\"image-uploader\" multiple :preview-size=\"80\"\r\n                :upload-icon=\"'plus'\">\r\n                <template #default>\r\n                  <div class=\"upload-placeholder\">\r\n                    <van-icon name=\"plus\" size=\"20\" color=\"#ff6b35\" />\r\n                    <div class=\"upload-text\">上传图片</div>\r\n                  </div>\r\n                </template>\r\n              </van-uploader>\r\n            </div>\r\n          </template>\r\n        </van-field>\r\n      </van-cell-group>\r\n    </div>\r\n\r\n    <!-- 底部按钮 -->\r\n    <div class=\"submit-buttons\">\r\n      <div class=\"btn\">提交预约</div>\r\n      <div class=\"btn\">我的预约</div>\r\n    </div>\r\n\r\n    <!-- 团体性质选择弹窗 -->\r\n    <van-popup v-model:show=\"showGroupTypePicker\" position=\"bottom\">\r\n      <van-picker :columns=\"groupTypeOptions\" @confirm=\"onGroupTypeConfirm\" @cancel=\"showGroupTypePicker = false\" />\r\n    </van-popup>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default { name: 'ArchiveVisitBooking' }\r\n</script>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue'\r\nimport { showToast } from 'vant'\r\n\r\n// 响应式数据\r\nconst contactName = ref('')\r\nconst contactPhone = ref('')\r\nconst groupName = ref('')\r\nconst groupType = ref('')\r\nconst groupTypeText = ref('')\r\nconst showGroupTypePicker = ref(false)\r\n\r\n// 文件上传相关\r\nconst attachmentFiles = ref([])\r\n\r\n// 团体性质选项\r\nconst groupTypeOptions = [\r\n  { text: '机关单位', value: '机关单位' },\r\n  { text: '企事业单位', value: '企事业单位' },\r\n  { text: '学校', value: '学校' },\r\n  { text: '社会团体', value: '社会团体' },\r\n  { text: '其他', value: '其他' }\r\n]\r\n\r\n// 日期数据\r\nconst dateList = ref([\r\n  { day: '二', date: '9-2', status: '可预约', active: false, disabled: false },\r\n  { day: '三', date: '9-3', status: '可预约', active: false, disabled: false },\r\n  { day: '四', date: '9-4', status: '可预约', active: false, disabled: false },\r\n  { day: '五', date: '9-5', status: '可预约', active: true, disabled: false },\r\n  { day: '六', date: '9-6', status: '可预约', active: false, disabled: false },\r\n  { day: '日', date: '9-7', status: '可预约', active: false, disabled: false },\r\n  { day: '一', date: '9-8', status: '可预约', active: false, disabled: true }\r\n])\r\n\r\n// 时段数据\r\nconst timeSlots = ref([\r\n  { id: 1, time: '09:00-11:00', status: '可预约', count: 49, active: false },\r\n  { id: 2, time: '13:30-16:30', status: '可预约', count: 160, active: true },\r\n  { id: 3, time: '13:30-16:30', status: '可预约', count: 160, active: true },\r\n  { id: 4, time: '13:30-16:30', status: '可预约', count: 160, active: true }\r\n])\r\n\r\n// 方法\r\nconst selectDate = (date) => {\r\n  if (date.disabled) return\r\n  dateList.value.forEach(item => item.active = false)\r\n  date.active = true\r\n}\r\n\r\nconst selectTimeSlot = (slot) => {\r\n  timeSlots.value.forEach(item => item.active = false)\r\n  slot.active = true\r\n}\r\n\r\n// 团体性质选择确认\r\nconst onGroupTypeConfirm = ({ selectedOptions }) => {\r\n  groupType.value = selectedOptions[0].value\r\n  groupTypeText.value = selectedOptions[0].text\r\n  showGroupTypePicker.value = false\r\n}\r\n\r\n// 附件上传处理\r\nconst onAttachmentRead = (file) => {\r\n  console.log('附件上传:', file)\r\n\r\n  // 验证文件类型\r\n  if (!file.file.type.startsWith('image/')) {\r\n    showToast('请选择图片文件')\r\n    return\r\n  }\r\n\r\n  // 验证文件大小 (限制为5MB)\r\n  const maxSize = 5 * 1024 * 1024\r\n  if (file.file.size > maxSize) {\r\n    showToast('图片大小不能超过5MB')\r\n    return\r\n  }\r\n\r\n  // 这里可以添加文件上传到服务器的逻辑\r\n  // 例如：uploadFile(file.file)\r\n  showToast('图片上传成功')\r\n}\r\n\r\n// 附件删除处理\r\nconst onAttachmentDelete = (file, detail) => {\r\n  console.log('删除附件:', file, detail)\r\n  showToast('图片已删除')\r\n}\r\n\r\nonMounted(() => {\r\n  // 初始化逻辑\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.archive-visit-booking {\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow: auto;\r\n  background: #f9f9f9;\r\n  padding: 10px 10px;\r\n\r\n  .section {\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    margin-bottom: 12px;\r\n    padding: 10px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n    .section-header {\r\n      margin-bottom: 15px;\r\n\r\n      .icon_museum {\r\n        width: 16px;\r\n        height: 15px;\r\n        margin-right: 8px;\r\n      }\r\n\r\n      .section-line {\r\n        width: 4px;\r\n        height: 16px;\r\n        background: #A54E3B;\r\n        margin-right: 8px;\r\n      }\r\n\r\n      .section-title {\r\n        font-size: 15px;\r\n        font-weight: bold;\r\n        color: #333;\r\n      }\r\n\r\n      .year {\r\n        font-size: 14px;\r\n        color: #666;\r\n        font-weight: normal;\r\n      }\r\n    }\r\n\r\n    // 日期选择样式\r\n    .date-grid {\r\n      gap: 6px;\r\n      overflow-x: auto;\r\n\r\n      .date-item {\r\n        flex: 1;\r\n        max-width: 70px;\r\n        height: 70px;\r\n        background: #f8f8f8;\r\n        border-radius: 4px;\r\n        position: relative;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: space-evenly;\r\n\r\n        .day {\r\n          font-size: 12px;\r\n          color: #666;\r\n          margin-bottom: 3px;\r\n          line-height: 1;\r\n          font-weight: normal;\r\n        }\r\n\r\n        .date {\r\n          font-size: 13px;\r\n          font-weight: 500;\r\n          color: #333;\r\n          margin-bottom: 3px;\r\n          line-height: 1;\r\n        }\r\n\r\n        .status {\r\n          font-size: 10px;\r\n          color: #999;\r\n          line-height: 1;\r\n          font-weight: normal;\r\n        }\r\n\r\n        &.active {\r\n          background: #fff3e0;\r\n          border-color: #ff6b35;\r\n\r\n          .day,\r\n          .date {\r\n            color: #ff6b35;\r\n          }\r\n\r\n          .status {\r\n            color: #ff6b35;\r\n          }\r\n        }\r\n\r\n        &.disabled {\r\n          opacity: 0.4;\r\n          background: #f5f5f5;\r\n\r\n          .day,\r\n          .date,\r\n          .status {\r\n            color: #ccc;\r\n          }\r\n        }\r\n\r\n        &:hover:not(.disabled):not(.active) {\r\n          background: #f0f0f0;\r\n          border-color: #e0e0e0;\r\n        }\r\n      }\r\n    }\r\n\r\n    // 时段选择样式\r\n    .time-slots {\r\n      display: flex;\r\n      gap: 8px;\r\n\r\n      .time-slot {\r\n        flex: 1;\r\n        background: #f5f5f5;\r\n        border-radius: 6px;\r\n        padding: 12px 8px;\r\n        transition: all 0.3s ease;\r\n        border: 1px solid transparent;\r\n        position: relative;\r\n        text-align: center;\r\n\r\n        .time {\r\n          font-size: 14px;\r\n          font-weight: 500;\r\n          color: #333;\r\n          margin-bottom: 2px;\r\n          line-height: 1.2;\r\n        }\r\n\r\n        .capacity {\r\n          font-size: 12px;\r\n          color: #666;\r\n          line-height: 1.2;\r\n        }\r\n\r\n        .active-indicator {\r\n          position: absolute;\r\n          top: 4px;\r\n          right: 4px;\r\n          width: 6px;\r\n          height: 6px;\r\n          background: #ff4444;\r\n          border-radius: 50%;\r\n        }\r\n\r\n        &.active {\r\n          background: #fff3e0;\r\n          border-color: #ff9800;\r\n\r\n          .time {\r\n            color: #ff6b35;\r\n          }\r\n\r\n          .capacity {\r\n            color: #ff6b35;\r\n          }\r\n        }\r\n\r\n        &:hover:not(.active) {\r\n          background: #eeeeee;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Vant表单组件样式\r\n    .form-group {\r\n      margin: 12px 0 0 0;\r\n      border-radius: 8px;\r\n      overflow: hidden;\r\n\r\n      .van-cell {\r\n        padding: 12px 0;\r\n        background: white;\r\n\r\n        &::after {\r\n          border-bottom: 1px solid #f0f0f0;\r\n        }\r\n\r\n        &:last-child::after {\r\n          border-bottom: none;\r\n        }\r\n      }\r\n\r\n      :deep(.van-field__label) {\r\n        color: #333;\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n        width: 80px;\r\n        flex-shrink: 0;\r\n\r\n        &::before {\r\n          content: '*';\r\n          color: #ff4444;\r\n          margin-right: 4px;\r\n        }\r\n      }\r\n\r\n      :deep(.van-field__control) {\r\n        font-size: 14px;\r\n        color: #333;\r\n\r\n        &::placeholder {\r\n          color: #999;\r\n        }\r\n      }\r\n\r\n      // 上传字段样式\r\n      .upload-field {\r\n        :deep(.van-field__control) {\r\n          padding: 0;\r\n        }\r\n\r\n        .upload-container {\r\n          width: 100%;\r\n\r\n          .image-uploader {\r\n            :deep(.van-uploader__wrapper) {\r\n              display: flex;\r\n              flex-wrap: wrap;\r\n              gap: 16px;\r\n              align-items: flex-start;\r\n              padding: 8px 0;\r\n            }\r\n\r\n            :deep(.van-uploader__preview) {\r\n              position: relative;\r\n              border-radius: 12px;\r\n              overflow: hidden;\r\n              box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08);\r\n              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n\r\n              &:hover {\r\n                transform: translateY(-2px) scale(1.02);\r\n                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);\r\n              }\r\n\r\n              .van-uploader__preview-image {\r\n                border-radius: 12px;\r\n                object-fit: cover;\r\n                transition: transform 0.3s ease;\r\n\r\n                &:hover {\r\n                  transform: scale(1.05);\r\n                }\r\n              }\r\n\r\n              .van-uploader__preview-delete {\r\n                position: absolute;\r\n                top: 6px;\r\n                right: 6px;\r\n                width: 24px;\r\n                height: 24px;\r\n                background: linear-gradient(135deg, rgba(255, 59, 48, 0.9) 0%, rgba(255, 69, 58, 0.9) 100%);\r\n                border-radius: 50%;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                backdrop-filter: blur(10px);\r\n                border: 2px solid rgba(255, 255, 255, 0.3);\r\n                transition: all 0.3s ease;\r\n\r\n                &:hover {\r\n                  background: linear-gradient(135deg, rgba(255, 59, 48, 1) 0%, rgba(255, 69, 58, 1) 100%);\r\n                  transform: scale(1.1);\r\n                  box-shadow: 0 4px 12px rgba(255, 59, 48, 0.4);\r\n                }\r\n\r\n                .van-icon {\r\n                  color: white;\r\n                  font-size: 14px;\r\n                  font-weight: bold;\r\n                  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));\r\n                }\r\n              }\r\n            }\r\n\r\n            :deep(.van-uploader__upload) {\r\n              margin: 0;\r\n\r\n              .upload-placeholder {\r\n                width: 100px;\r\n                height: 100px;\r\n                border: 2px dashed #ff6b35;\r\n                border-radius: 12px;\r\n                background: linear-gradient(135deg, #fff9f5 0%, #fff3e0 100%);\r\n                display: flex;\r\n                flex-direction: column;\r\n                align-items: center;\r\n                justify-content: center;\r\n                cursor: pointer;\r\n                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n                position: relative;\r\n                overflow: hidden;\r\n\r\n                &::before {\r\n                  content: '';\r\n                  position: absolute;\r\n                  top: 0;\r\n                  left: 0;\r\n                  right: 0;\r\n                  bottom: 0;\r\n                  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);\r\n                  transform: translateX(-100%);\r\n                  transition: transform 0.6s ease;\r\n                }\r\n\r\n                .van-icon {\r\n                  margin-bottom: 6px;\r\n                  filter: drop-shadow(0 2px 4px rgba(255, 107, 53, 0.2));\r\n                }\r\n\r\n                .upload-text {\r\n                  font-size: 13px;\r\n                  color: #ff6b35;\r\n                  font-weight: 600;\r\n                  letter-spacing: 0.5px;\r\n                  text-shadow: 0 1px 2px rgba(255, 107, 53, 0.1);\r\n                }\r\n\r\n                &:hover {\r\n                  border-color: #ff4500;\r\n                  background: linear-gradient(135deg, #fff5f0 0%, #ffe8d6 100%);\r\n                  transform: translateY(-3px) scale(1.02);\r\n                  box-shadow: 0 8px 25px rgba(255, 107, 53, 0.25), 0 3px 10px rgba(0, 0, 0, 0.1);\r\n\r\n                  &::before {\r\n                    transform: translateX(100%);\r\n                  }\r\n\r\n                  .upload-text {\r\n                    color: #ff4500;\r\n                  }\r\n                }\r\n\r\n                &:active {\r\n                  transform: translateY(-1px) scale(1.01);\r\n                  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.2);\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .counter {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 16px;\r\n\r\n      .counter-btn {\r\n        width: 32px;\r\n        height: 32px;\r\n        border: 1px solid #ddd;\r\n        border-radius: 6px;\r\n        background: white;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        cursor: pointer;\r\n        font-size: 18px;\r\n        color: #666;\r\n        transition: all 0.3s ease;\r\n\r\n        &:hover {\r\n          border-color: #ff6b35;\r\n          color: #ff6b35;\r\n        }\r\n\r\n        &:active {\r\n          background: #f0f0f0;\r\n        }\r\n      }\r\n\r\n      .count {\r\n        font-size: 18px;\r\n        font-weight: 600;\r\n        color: #333;\r\n        min-width: 24px;\r\n        text-align: center;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 底部操作区域\r\n.submit-buttons {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  align-items: center;\r\n\r\n  .btn {\r\n    color: #ffffff;\r\n    background-color: #e1b97b;\r\n    font-weight: normal;\r\n    border-radius: 30px;\r\n    padding: 10px 0;\r\n    width: 46%;\r\n    text-align: center;\r\n    font-size: 14px;\r\n  }\r\n}\r\n</style>"], "mappings": ";OAMeA,UAAuC;;EAL/CC,KAAK,EAAC;AAAuB;;EAE3BA,KAAK,EAAC;AAAS;;EAQbA,KAAK,EAAC;AAAoB;;;EAGtBA,KAAK,EAAC;AAAK;;EACXA,KAAK,EAAC;AAAM;;EACZA,KAAK,EAAC;AAAQ;;EAMpBA,KAAK,EAAC;AAAS;;EAObA,KAAK,EAAC;AAAY;;;EAGdA,KAAK,EAAC;AAAM;;EACZA,KAAK,EAAC;AAAU;;;EACGA,KAAK,EAAC;;;EAM/BA,KAAK,EAAC;AAAS;;EA2BPA,KAAK,EAAC;AAAkB;;EAKlBA,KAAK,EAAC;AAAoB;;;;;;;;uBAvE/CC,mBAAA,CA6FM,OA7FNC,UA6FM,GA5FJC,mBAAA,YAAe,EACfC,mBAAA,CAgBM,OAhBNC,UAgBM,G,+XARJD,mBAAA,CAOM,OAPNE,UAOM,I,kBANJL,mBAAA,CAKMM,SAAA,QAAAC,WAAA,CALgCC,MAAA,CAAAC,QAAQ,YAAhBC,IAAI;yBAAlCV,mBAAA,CAKM;MALDD,KAAK,EAAAY,eAAA,EAAC,WAAW;QAAAC,MAAA,EACFF,IAAI,CAACE,MAAM;QAAAC,QAAA,EAAYH,IAAI,CAACG;MAAQ;MADPC,GAAG,EAAEJ,IAAI,CAACK,GAAG;MACDC,OAAK,WAALA,OAAKA,CAAAC,MAAA;QAAA,OAAET,MAAA,CAAAU,UAAU,CAACR,IAAI;MAAA;QACjFP,mBAAA,CAAqC,OAArCgB,UAAqC,EAAAC,gBAAA,CAAjBV,IAAI,CAACK,GAAG,kBAC5BZ,mBAAA,CAAuC,OAAvCkB,UAAuC,EAAAD,gBAAA,CAAlBV,IAAI,CAACA,IAAI,kBAC9BP,mBAAA,CAA2C,OAA3CmB,UAA2C,EAAAF,gBAAA,CAApBV,IAAI,CAACa,MAAM,iB;sCAKxCrB,mBAAA,YAAe,EACfC,mBAAA,CAeM,OAfNqB,UAeM,G,0BAdJrB,mBAAA,CAKM;IALDJ,KAAK,EAAC;EAA8C,IACvDI,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAA4B,IACrCI,mBAAA,CAAwE;IAAnEsB,GAAuC,EAnBvC3B,UAAuC;IAmBC4B,GAAG,EAAC,EAAE;IAAC3B,KAAK,EAAC;MAC1DI,mBAAA,CAAuC;IAAlCJ,KAAK,EAAC;EAAe,GAAC,QAAM,E,uBAGrCI,mBAAA,CAOM,OAPNwB,UAOM,I,kBANJ3B,mBAAA,CAKMM,SAAA,QAAAC,WAAA,CALgCC,MAAA,CAAAoB,SAAS,YAAjBC,IAAI;yBAAlC7B,mBAAA,CAKM;MALDD,KAAK,EAAAY,eAAA,EAAC,WAAW;QAAAC,MAAA,EAA4DiB,IAAI,CAACjB;MAAM;MAA3CE,GAAG,EAAEe,IAAI,CAACC,EAAE;MAC3Dd,OAAK,WAALA,OAAKA,CAAAC,MAAA;QAAA,OAAET,MAAA,CAAAuB,cAAc,CAACF,IAAI;MAAA;QAC3B1B,mBAAA,CAAuC,OAAvC6B,WAAuC,EAAAZ,gBAAA,CAAlBS,IAAI,CAACI,IAAI,kBAC9B9B,mBAAA,CAAiE,OAAjE+B,WAAiE,EAAAd,gBAAA,CAAxCS,IAAI,CAACN,MAAM,IAAG,IAAE,GAAAH,gBAAA,CAAGS,IAAI,CAACM,KAAK,IAAG,IAAE,iBAChDN,IAAI,CAACjB,MAAM,I,cAAtBZ,mBAAA,CAAuD,OAAvDoC,WAAuD,K;sCAK7DlC,mBAAA,YAAe,EACfC,mBAAA,CA0CM,OA1CNkC,WA0CM,G,4BAzCJlC,mBAAA,CAKM;IALDJ,KAAK,EAAC;EAA8C,IACvDI,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAA4B,IACrCI,mBAAA,CAAkC;IAA5BJ,KAAK,EAAC;EAAc,IAC1BI,mBAAA,CAAuC;IAAlCJ,KAAK,EAAC;EAAe,GAAC,QAAM,E,uBAGrCuC,YAAA,CAkCiBC,yBAAA;IAlCDC,KAAK,EAAL,EAAK;IAACzC,KAAK,EAAC;;sBAC1B;MAAA,OAAY,CAAZG,mBAAA,SAAY,EACZoC,YAAA,CACuBG,oBAAA;oBADHjC,MAAA,CAAAkC,WAAW;;iBAAXlC,MAAA,CAAAkC,WAAW,GAAAzB,MAAA;QAAA;QAAE0B,KAAK,EAAC,KAAK;QAACC,WAAW,EAAC,OAAO;QAACC,QAAQ,EAAR,EAAQ;QAAC,aAAW,EAAC,MAAM;QAC1F9C,KAAK,EAAC;+CAERG,mBAAA,UAAa,EACboC,YAAA,CACuBG,oBAAA;oBADHjC,MAAA,CAAAsC,YAAY;;iBAAZtC,MAAA,CAAAsC,YAAY,GAAA7B,MAAA;QAAA;QAAE0B,KAAK,EAAC,MAAM;QAACC,WAAW,EAAC,QAAQ;QAACC,QAAQ,EAAR,EAAQ;QAAC,aAAW,EAAC,MAAM;QAC7F9C,KAAK,EAAC;+CAERG,mBAAA,UAAa,EACboC,YAAA,CACuBG,oBAAA;oBADHjC,MAAA,CAAAuC,SAAS;;iBAATvC,MAAA,CAAAuC,SAAS,GAAA9B,MAAA;QAAA;QAAE0B,KAAK,EAAC,MAAM;QAACC,WAAW,EAAC,SAAS;QAACC,QAAQ,EAAR,EAAQ;QAAC,aAAW,EAAC,MAAM;QAC3F9C,KAAK,EAAC;+CAERG,mBAAA,UAAa,EACboC,YAAA,CAC8EG,oBAAA;oBAD1DjC,MAAA,CAAAwC,aAAa;;iBAAbxC,MAAA,CAAAwC,aAAa,GAAA/B,MAAA;QAAA;QAAE0B,KAAK,EAAC,MAAM;QAACC,WAAW,EAAC,SAAS;QAACK,QAAQ,EAAR,EAAQ;QAACC,SAAS,EAAT,EAAS;QAACL,QAAQ,EAAR,EAAQ;QAC/F,aAAW,EAAC,MAAM;QAAC9C,KAAK,EAAC,YAAY;QAAEiB,OAAK,EAAAmC,MAAA,QAAAA,MAAA,gBAAAlC,MAAA;UAAA,OAAET,MAAA,CAAA4C,mBAAmB;QAAA;+CAEnElD,mBAAA,QAAW,EACXoC,YAAA,CAeYG,oBAAA;QAfDE,KAAK,EAAC,IAAI;QAACE,QAAQ,EAAR,EAAQ;QAAC,aAAW,EAAC,MAAM;QAAC9C,KAAK,EAAC;;QAC3CsD,KAAK,EAAAC,QAAA,CACd;UAAA,OAWM,CAXNnD,mBAAA,CAWM,OAXNoD,WAWM,GAVJjB,YAAA,CASekB,uBAAA;wBATQhD,MAAA,CAAAiD,eAAe;;qBAAfjD,MAAA,CAAAiD,eAAe,GAAAxC,MAAA;YAAA;YAAG,WAAS,EAAE,CAAC;YAAG,YAAU,EAAET,MAAA,CAAAkD,gBAAgB;YACjF,cAAY,EAAElD,MAAA,CAAAmD,kBAAkB;YAAEC,MAAM,EAAC,SAAS;YAAC7D,KAAK,EAAC,gBAAgB;YAAC8D,QAAQ,EAAR,EAAQ;YAAE,cAAY,EAAE,EAAE;YACpG,aAAW,EAAE;;YACHC,OAAO,EAAAR,QAAA,CAChB;cAAA,OAGM,CAHNnD,mBAAA,CAGM,OAHN4D,WAGM,GAFJzB,YAAA,CAAkD0B,mBAAA;gBAAxCC,IAAI,EAAC,MAAM;gBAACC,IAAI,EAAC,IAAI;gBAACC,KAAK,EAAC;8CACtChE,mBAAA,CAAmC;gBAA9BJ,KAAK,EAAC;cAAa,GAAC,MAAI,oB;;;;;;;;;QAU7CG,mBAAA,UAAa,E,4BACbC,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAgB,IACzBI,mBAAA,CAA2B;IAAtBJ,KAAK,EAAC;EAAK,GAAC,MAAI,GACrBI,mBAAA,CAA2B;IAAtBJ,KAAK,EAAC;EAAK,GAAC,MAAI,E,qBAGvBG,mBAAA,cAAiB,EACjBoC,YAAA,CAEY8B,oBAAA;IAFOC,IAAI,EAAE7D,MAAA,CAAA4C,mBAAmB;;aAAnB5C,MAAA,CAAA4C,mBAAmB,GAAAnC,MAAA;IAAA;IAAEqD,QAAQ,EAAC;;sBACrD;MAAA,OAA8G,CAA9GhC,YAAA,CAA8GiC,qBAAA;QAAjGC,OAAO,EAAEhE,MAAA,CAAAiE,gBAAgB;QAAGC,SAAO,EAAElE,MAAA,CAAAmE,kBAAkB;QAAGC,QAAM,EAAAzB,MAAA,QAAAA,MAAA,gBAAAlC,MAAA;UAAA,OAAET,MAAA,CAAA4C,mBAAmB;QAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}