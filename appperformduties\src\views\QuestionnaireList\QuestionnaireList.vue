<template>
  <div class="QuestionnaireList">
    <van-sticky>
      <img src="@/assets/img/bg_survey_list_zx.png" alt="" style="width: 100%;height: 100%;">
    </van-sticky>
    <div class="section">
      <div class="section-header">
        <div class="section-header-top">
          <span class="line"></span>
          <span class="section-title">进行中</span>
        </div>
        <span class="section-more">更多</span>
      </div>
      <div v-if="doingList.length" class="card-list">
        <div v-for="item in doingList" :key="item.id" class="card" @click="openDetails(item)">
          <img class="card-icon" src="@/assets/img/icon-paper-end.png" />
          <div class="card-content">
            <div class="card-title">{{ item.name }}</div>
            <div class="card-time">{{ formatDate(item.beginTime, 'YYYY-MM-DD HH:mm') }}至{{
              formatDate(item.endTime, 'YYYY-MM-DD HH:mm') }}</div>
            <div class="card-footer">
              <span class="card-status">
                <span
                  :style="`font-size: 14px;font-weight: 600;margin-right: 5px;color: ${item.examStatus == '3' ? 'rgb(80, 198, 20)' : 'rgb(246, 147, 28)'};`">·</span>
                {{ item.examStatus == '3' ? '已参与' : '未参与' }}
              </span>
              <!-- <span class="card-count">答卷数：<span class="count">{{ item.joinCount }}</span></span> -->
            </div>
          </div>
        </div>
      </div>
      <div v-else class="nodata">
        <span>暂无数据</span>
      </div>
    </div>
    <div class="section">
      <div class="section-header">
        <div class="section-header-top">
          <span class="line"></span>
          <span class="section-title">已结束</span>
        </div>
        <span class="section-more">更多</span>
      </div>
      <div v-if="finishedList.length" class="card-list">
        <div v-for="item in finishedList" :key="item.id" class="card" @click="openDetails(item)">
          <img class="card-icon" src="@/assets/img/icon-paper-proceed.png" />
          <div class="card-content">
            <div class="card-title">{{ item.name }}</div>
            <div class="card-time">{{ formatDate(item.beginTime, 'YYYY-MM-DD HH:mm') }}至{{
              formatDate(item.endTime, 'YYYY-MM-DD HH:mm') }}</div>
            <div class="card-footer">
              <span class="card-status">
                <span style="font-size: 14px;font-weight: 600;margin-right: 5px;color: rgb(246, 147, 28);">·</span>
                {{ item.examStatus == '3' ? '已参与' : '未参与' }}
              </span>
              <!-- <span class="card-count">答卷数：<span class="count">{{ item.joinCount }}</span></span> -->
            </div>
          </div>
        </div>
      </div>
      <div v-else class="nodata">
        <span>暂无数据</span>
      </div>
    </div>
  </div>
</template>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import { formatDate } from '@/assets/js/utils.js'
import { useRouter } from 'vue-router'
const router = useRouter()
const title = ref('问卷调查列表')
const pageNo = ref(1)//当前页码
const pageSize = ref(3)//每页条数
const doingList = ref([])
const finishedList = ref([])
onMounted(() => {
  if (title.value) {
    document.title = title.value
  }
  getProgressList()
})
// 获取问卷调查列表 进行中
const getProgressList = async () => {
  const params = {
    examineStatus: 2,
    isAnd: 1,
    pageNo: pageNo.value,
    pageSize: pageSize.value,
    query: {
      businessCode: 'questionnaire'
    }
  }
  const { data, code } = await api.studypaperList(params)
  if (code === 200) {
    doingList.value = doingList.value.concat(data)
    getEndedList()
  }
}
// 获取问卷调查列表 已结束
const getEndedList = async () => {
  const params = {
    examineStatus: 3,
    isAnd: 1,
    pageNo: pageNo.value,
    pageSize: pageSize.value,
    query: {
      businessCode: 'questionnaire'
    }
  }
  const { data, code } = await api.studypaperList(params)
  if (code === 200) {
    finishedList.value = finishedList.value.concat(data)
  }
}
// 进详情
const openDetails = (_item) => {
  console.log(_item)
  router.push({ path: '/QuestionnaireDetails', query: { id: _item.id, examineId: _item.examineId, paperStatus: _item.paperStatus.name, examStatus: _item.examStatus } })
}
</script>
<style lang="scss">
.QuestionnaireList {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: #fff;

  .section {
    margin-top: 16px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 16px 8px 16px;

      .section-header-top {
        display: flex;
        align-items: center;

        .line {
          width: 3px;
          height: 15px;
          border-radius: 1.5px;
          margin-right: 5px;
          background: rgb(54, 87, 192);
        }

        .section-title {
          font-weight: bold;
          font-size: 18px;
        }
      }

      .section-more {
        color: #999;
        font-size: 14px;
        cursor: pointer;
      }
    }

    .card-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
      padding: 0 12px;

      .card {
        display: flex;
        background: #fff;
        border-radius: 12px;
        box-shadow: rgba(24, 64, 118, 0.08) 0px 2px 10px 1px;
        padding: 16px;
        align-items: flex-start;

        .card-icon {
          width: 24px;
          height: 24px;
          margin-right: 12px;
        }

        .card-content {
          flex: 1;

          .card-title {
            font-size: 17px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #222;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .card-time {
            font-size: 13px;
            color: rgb(51, 51, 51);
            margin-bottom: 10px;
          }

          .card-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .card-status {
              color: #000;
              font-size: 13px;
            }

            .card-count {
              color: #000;
              font-size: 13px;

              .count {
                color: #3A4B8C;
                font-weight: bold;
              }
            }
          }
        }
      }
    }

    .nodata {
      text-align: center;
      color: #ccc;
      font-size: 14px;
    }
  }
}
</style>
