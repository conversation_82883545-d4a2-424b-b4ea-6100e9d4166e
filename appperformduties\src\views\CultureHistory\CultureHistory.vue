<template>
  <div class="CultureHistory">
    <!-- 轮播图 -->
    <div class="CultureHistorySwiper">
      <van-swipe class="swiper-container" :autoplay="3000" indicator-color="#fff" :show-indicators="false" :loop="true">
        <van-swipe-item v-for="(item, index) in swiperList" :key="index" class="swiper-item"
          @click="openCarouselDetails(item)">
          <div class="swiper-image-container">
            <img :src="config.API_URL + '/image/' + item.infoPic" :alt="item.title" class="swiper-image">
            <div class="swiper-overlay">
              <div class="swiper-content">
                <div class="swiper-title">{{ item.title }}</div>
                <p class="swiper-time">{{ formatDate(item.pubTime, 'YYYY-MM-DD HH:ss') }}</p>
              </div>
            </div>
          </div>
        </van-swipe-item>
      </van-swipe>
    </div>
    <div class="noticeList">
      <div class="notice-grid">
        <div v-for="(item, idx) in noticeItems" :key="idx" class="notice-item" :style="{ background: item.bg }"
          @click="handleNoticeClick(item)">
          <div class="notice-icon">
            <img :src="item.icon" :alt="item.title" />
          </div>
          <div class="notice-main">{{ item.title }}</div>
          <div class="notice-sub">{{ item.subTitle }}</div>
        </div>
      </div>
    </div>
    <!-- 书画院精选 -->
    <div class="academyBox">
      <div class="academy-header">
        <span class="academy-title">书画院精选</span>
        <span class="academy-more" @click="goToArtGallery">查看更多</span>
      </div>
      <div class="academy-grid">
        <div class="academy-card" v-for="(item, idx) in academyList" :key="idx" @click="goToArtDetail(item)">
          <div class="academy-img-wrap">
            <img :src="config.API_URL + '/image/' + item.infoPic" :alt="item.title" class="academy-img" />
          </div>
          <div class="academy-main-title">{{ item.title }}</div>
          <div class="academy-source">{{ item.source }}</div>
        </div>
      </div>
    </div>
    <!-- vr入口 -->
    <div style="text-align: center;" v-if="false">
      <img src="@/assets/img/icon_bg_vr.png" alt="" @click="openVr()">
    </div>
  </div>
</template>
<script>
export default { name: 'CultureHistory' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import config from '@/config'
import { formatDate } from '@/assets/js/utils.js'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
const router = useRouter()
const title = ref('文史资料')
const swiperList = ref([])
const noticeItems = ref([
  {
    icon: require('@/assets/img/icon_materials.png'),
    title: '文史资料查询',
    subTitle: '浏览历史文献资料',
    bg: 'rgba(232, 242, 255, 0.6)',
    route: '1893948473322008578',
  },
  {
    icon: require('@/assets/img/icon_collect.png'),
    title: '文史征集',
    subTitle: '投稿历史文献资料',
    bg: 'rgba(232, 255, 247, 0.6)',
    route: '1789181208614170626',
  },
  {
    icon: require('@/assets/img/icon_Academy.png'),
    title: '书画院展示',
    subTitle: '欣赏书画作品',
    bg: 'rgba(245, 241, 255, 0.6)',
    route: '1893945764258803713',
  },
  {
    icon: require('@/assets/img/icon_vr.png'),
    title: 'VR文史馆',
    subTitle: '虚拟展厅导览',
    bg: 'rgba(255, 251, 232, 0.6)',
    route: '',
  },
])
const academyList = ref([])

onMounted(() => {
  if (title.value) {
    document.title = title.value
  }
  getCarousel()
})
// 获取轮播图
const getCarousel = async () => {
  const { data } = await api.newsContentBatchList({
    pageNo: 1,
    pageSize: 10,
    query: {
      columnId: "1689554782383009794",
      moduleId: 4,
      passFlag: 1
    }
  })
  swiperList.value = data
  getAcademy()
}
// 点击轮播图进详情
const openCarouselDetails = (_item) => {
  router.push({ path: '/CultureHistoryDetails', query: { id: _item.id } })
}
// 点击通知公告四个板块
const handleNoticeClick = (_item) => {
  if (!_item.route) { return showToast('暂未接入') }
  if (_item.route == '1789181208614170626') {
    router.push({ path: '/CultureHistoryAdd' })
  } else if (_item.route == '1893948473322008578') {
    router.push({ path: '/NewList', query: { title: '文史资料查询', parentId: _item.route, tableId: 'content_information_4', moduleId: '4', utype: '1' } })
  } else {
    router.push({ path: '/NewList', query: { title: '文史资料查询', id: _item.route, tableId: 'content_information_4', moduleId: '4' } })
  }
}
// 获取书画院精选
const getAcademy = async () => {
  const { data } = await api.newsContentBatchList({
    pageNo: 1,
    pageSize: 10,
    query: {
      columnId: "1893945764258803713",
      moduleId: 4,
      passFlag: 1
    }
  })
  academyList.value = data
}
// 跳转到书画院精选更多
const goToArtGallery = () => {
  router.push({ path: '/NewList', query: { id: '1893945764258803713', tableId: 'content_information_4', moduleId: '4' } })
}
const goToArtDetail = (_item) => {
  router.push({ path: '/CultureHistoryDetails', query: { id: _item.id } })
}
// 跳转vr
const openVr = () => {
  showToast('暂未接入')
}
</script>
<style lang="scss">
.CultureHistory {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: rgb(253, 249, 239);
}

.CultureHistorySwiper {
  height: 200px;
  margin: 15px;

  .swiper-container {
    width: 100%;
    height: 100%;
    border-radius: 10px;

    .swiper-item {
      width: 100%;

      .swiper-image-container {
        position: relative;
        width: 100%;
        height: 100%;
        box-sizing: border-box;

        .swiper-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .swiper-overlay {
          position: absolute;
          bottom: 5px;
          left: 0;
          right: 0;
          background: rgb(0, 0, 0, 0.5);
          padding: 5px;

          .swiper-content {
            .swiper-title {
              color: #fff;
              font-size: 15px;
              margin: 0 0 8px 0;
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .swiper-time {
              color: rgba(255, 255, 255, 0.8);
              font-size: 13px;
              margin: 0;
              line-height: 1.2;
            }
          }
        }
      }
    }
  }
}

// 自定义指示器样式
:deep(.van-swipe__indicators) {
  bottom: 15px;

  .van-swipe__indicator {
    background: rgba(255, 255, 255, 0.6);

    &.van-swipe__indicator--active {
      background: #fff;
    }
  }
}

.noticeList {
  margin: 20px 15px;

  .notice-title {
    font-size: 18px;
    font-weight: bold;
    color: #222;
    margin-bottom: 14px;
  }

  .notice-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;

    .notice-item {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
      border-radius: 8px;
      padding: 18px 14px 14px 14px;

      .notice-icon {
        width: 28px;
        height: 28px;
        margin-bottom: 8px;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }

      .notice-main {
        font-size: 17px;
        margin-top: 10px;
      }

      .notice-sub {
        font-size: 14px;
        color: rgb(75, 85, 99);
        margin-top: 4px;
      }
    }
  }
}

.academyBox {
  margin: 30px 15px 20px 15px;

  .academy-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 18px;

    .academy-title {
      font-size: 18px;
      font-weight: bold;
      color: #222;
    }

    .academy-more {
      font-size: 15px;
      color: #e6b200;
      cursor: pointer;
      font-weight: 500;
    }
  }

  .academy-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;

    .academy-card {
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);
      padding: 0 0 12px 0;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      height: 233px;

      .academy-img-wrap {
        width: 100%;
        height: 160px;
        // aspect-ratio: 1.2/1;
        border-radius: 10px 10px 0 0;
        overflow: hidden;
        background: #f5f5f5;

        .academy-img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          display: block;
        }
      }

      .academy-main-title {
        font-size: 16px;
        color: #222;
        margin: 10px 0 2px 5px;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .academy-sub-title {
        font-size: 14px;
        color: #888;
        margin: 0 0 2px 5px;
      }

      .academy-source {
        font-size: 14px;
        color: rgb(75, 85, 99);
        margin: 4px 0 0 5px;
      }
    }
  }
}
</style>
