{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElement<PERSON>lock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, resolveComponent as _resolveComponent, withCtx as _withCtx, createBlock as _createBlock, createVNode as _createVNode, withModifiers as _withModifiers, createTextVNode as _createTextVNode, createStaticVNode as _createStaticVNode } from \"vue\";\nimport _imports_0 from '@/assets/img/icon_top_bg.png';\nimport _imports_1 from '@/assets/img/icon_top_logo.png';\nimport _imports_2 from '@/assets/img/icon_top_text.png';\nimport _imports_3 from '@/assets/img/icon_arrow.png';\nimport _imports_4 from '@/assets/img/icon_network.png';\nimport _imports_5 from '@/assets/img/icon_survey.png';\nimport _imports_6 from '@/assets/img/icon_committee_living_room.png';\nvar _hoisted_1 = {\n  class: \"LayoutHome\"\n};\nvar _hoisted_2 = {\n  key: 0,\n  class: \"global-loading-mask flex_box flex_flex_direction_column flex_align_center flex_justify_content\"\n};\nvar _hoisted_3 = {\n  class: \"new_box\"\n};\nvar _hoisted_4 = {\n  class: \"header_box\"\n};\nvar _hoisted_5 = {\n  key: 0,\n  class: \"loading\"\n};\nvar _hoisted_6 = {\n  key: 1,\n  class: \"carousel\"\n};\nvar _hoisted_7 = {\n  class: \"swiper-image-container\"\n};\nvar _hoisted_8 = [\"src\", \"alt\"];\nvar _hoisted_9 = {\n  class: \"swiper-overlay\"\n};\nvar _hoisted_10 = {\n  class: \"swiper-title\"\n};\nvar _hoisted_11 = {\n  class: \"carousel-dots\"\n};\nvar _hoisted_12 = [\"onClick\"];\nvar _hoisted_13 = {\n  key: 2,\n  class: \"loading\"\n};\nvar _hoisted_14 = {\n  class: \"negotiation_activities_preheating\"\n};\nvar _hoisted_15 = {\n  key: 0,\n  class: \"negotiation_activity_preheat\"\n};\nvar _hoisted_16 = [\"src\"];\nvar _hoisted_17 = {\n  class: \"negotiation_topics\"\n};\nvar _hoisted_18 = {\n  key: 0,\n  class: \"loading\"\n};\nvar _hoisted_19 = {\n  key: 1,\n  class: \"negotiation_topics_content\"\n};\nvar _hoisted_20 = {\n  class: \"topic-title\"\n};\nvar _hoisted_21 = {\n  class: \"topic-image\"\n};\nvar _hoisted_22 = [\"src\"];\nvar _hoisted_23 = [\"onClick\"];\nvar _hoisted_24 = {\n  class: \"topic-title\"\n};\nvar _hoisted_25 = {\n  class: \"sub-topic-btn\"\n};\nvar _hoisted_26 = {\n  class: \"topic-image small\",\n  style: {\n    \"margin\": \"0\"\n  }\n};\nvar _hoisted_27 = [\"src\"];\nvar _hoisted_28 = {\n  key: 2,\n  class: \"loading\"\n};\nvar _hoisted_29 = {\n  key: 1,\n  class: \"video_graphic_live\"\n};\nvar _hoisted_30 = {\n  key: 0,\n  class: \"loading\"\n};\nvar _hoisted_31 = {\n  key: 1,\n  class: \"video_graphic_live_content\"\n};\nvar _hoisted_32 = [\"src\", \"alt\"];\nvar _hoisted_33 = {\n  class: \"vg-carousel-bottom-bar\"\n};\nvar _hoisted_34 = {\n  class: \"vg-carousel-title\"\n};\nvar _hoisted_35 = {\n  class: \"vg-carousel-dots\"\n};\nvar _hoisted_36 = [\"onClick\"];\nvar _hoisted_37 = {\n  key: 2,\n  class: \"loading\"\n};\nvar _hoisted_38 = {\n  class: \"committee_living_room\"\n};\nvar _hoisted_39 = {\n  key: 1,\n  class: \"negotiation_topics_collect\"\n};\nvar _hoisted_40 = {\n  class: \"negotiation_topics_collect_box\"\n};\nvar _hoisted_41 = {\n  key: 0,\n  class: \"loading\"\n};\nvar _hoisted_42 = {\n  class: \"topics_collect_title\"\n};\nvar _hoisted_43 = {\n  class: \"topics_collect_content\"\n};\nvar _hoisted_44 = {\n  class: \"topics_collect_organize\"\n};\nvar _hoisted_45 = {\n  class: \"topics_collect_flex\"\n};\nvar _hoisted_46 = {\n  class: \"topics_collect_flex_text1\"\n};\nvar _hoisted_47 = {\n  key: 0\n};\nvar _hoisted_48 = {\n  class: \"blue\"\n};\nvar _hoisted_49 = {\n  key: 1\n};\nvar _hoisted_50 = {\n  class: \"topics_collect_flex_text2\"\n};\nvar _hoisted_51 = {\n  key: 2,\n  class: \"loading\"\n};\nvar _hoisted_52 = {\n  key: 2,\n  class: \"cultural_historical_collection\"\n};\nvar _hoisted_53 = {\n  class: \"header_box\"\n};\nvar _hoisted_54 = {\n  class: \"cultural_historical_collection_content\"\n};\nvar _hoisted_55 = {\n  class: \"historical_collection_content_flex\"\n};\nvar _hoisted_56 = [\"onClick\"];\nvar _hoisted_57 = {\n  class: \"card-title\"\n};\nvar _hoisted_58 = {\n  class: \"card-btn-icon\"\n};\nvar _hoisted_59 = [\"src\"];\nvar _hoisted_60 = {\n  class: \"archives_institute\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_van_swipe_item = _resolveComponent(\"van-swipe-item\");\n  var _component_van_swipe = _resolveComponent(\"van-swipe\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 全局loading遮罩 \"), $setup.loadingPage ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, _cache[10] || (_cache[10] = [_createElementVNode(\"div\", {\n    class: \"global-loading-spinner\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"div\", {\n    class: \"global-loading-text\"\n  }, \"加载中...\", -1 /* CACHED */)]))) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 顶部背景和logo \"), _cache[35] || (_cache[35] = _createStaticVNode(\"<div class=\\\"top_bg\\\"><img src=\\\"\" + _imports_0 + \"\\\" alt=\\\"\\\" class=\\\"bg_img\\\"><div class=\\\"logo\\\"><img src=\\\"\" + _imports_1 + \"\\\" alt=\\\"\\\" style=\\\"width:180px;height:30px;\\\"></div><div class=\\\"top_text\\\"><img src=\\\"\" + _imports_2 + \"\\\" alt=\\\"\\\" style=\\\"width:277px;height:55px;\\\"></div></div>\", 1)), _createCommentVNode(\" 政协资讯 \"), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_cache[11] || (_cache[11] = _createElementVNode(\"span\", {\n    class: \"header_title\"\n  }, \"政协资讯\", -1 /* CACHED */)), _createElementVNode(\"span\", {\n    class: \"header_all\",\n    onClick: _cache[0] || (_cache[0] = function ($event) {\n      return $setup.openNewMore();\n    })\n  }, \"全部 >\")]), $setup.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, _cache[12] || (_cache[12] = [_createElementVNode(\"span\", {\n    class: \"loading_spinner\"\n  }, null, -1 /* CACHED */)]))) : $setup.carouselList.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [_createVNode(_component_van_swipe, {\n    class: \"swiper-container\",\n    autoplay: 3000,\n    \"indicator-color\": \"#fff\",\n    \"show-indicators\": true,\n    loop: true\n  }, {\n    default: _withCtx(function () {\n      return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.carouselList, function (item, index) {\n        return _openBlock(), _createBlock(_component_van_swipe_item, {\n          key: index,\n          class: \"swiper-item\",\n          onClick: function onClick($event) {\n            return $setup.onCarouselClick(item);\n          }\n        }, {\n          default: _withCtx(function () {\n            return [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"img\", {\n              src: $setup.config.API_URL + '/image/' + item.infoPic,\n              alt: item.title,\n              class: \"swiper-image\"\n            }, null, 8 /* PROPS */, _hoisted_8), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, _toDisplayString(item.infoTitle), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_11, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.carouselList, function (item, idx) {\n              return _openBlock(), _createElementBlock(\"span\", {\n                key: idx,\n                class: _normalizeClass([\"dot\", {\n                  active: idx === $setup.carouselIndex\n                }]),\n                onClick: function onClick($event) {\n                  return _ctx.goToNewsCarousel(idx);\n                }\n              }, null, 10 /* CLASS, PROPS */, _hoisted_12);\n            }), 128 /* KEYED_FRAGMENT */))])])];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]);\n      }), 128 /* KEYED_FRAGMENT */))];\n    }),\n    _: 1 /* STABLE */\n  })])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_13, _cache[13] || (_cache[13] = [_createElementVNode(\"span\", null, \"暂无数据\", -1 /* CACHED */)])))]), _createCommentVNode(\" 协商活动预热 \"), _createElementVNode(\"div\", _hoisted_14, [_createCommentVNode(\" 协商活动预热头部 \"), _cache[25] || (_cache[25] = _createElementVNode(\"div\", {\n    class: \"header_box\"\n  }, [_createElementVNode(\"span\", {\n    class: \"header_title\"\n  }, \"协商活动预热\")], -1 /* CACHED */)), _createCommentVNode(\" 协商活动预热图片 \"), $setup.isPreheat == '1' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [_createElementVNode(\"img\", {\n    src: $setup.config.API_URL + '/pageImg/open/ixian_preheat_bg',\n    alt: \"\",\n    class: \"negotiation_activity_preheat_img\",\n    onClick: _cache[1] || (_cache[1] = function ($event) {\n      return $setup.openActivety(null);\n    })\n  }, null, 8 /* PROPS */, _hoisted_16)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 协商议题区域 \"), _createElementVNode(\"div\", _hoisted_17, [_cache[19] || (_cache[19] = _createElementVNode(\"div\", {\n    class: \"negotiation_topics_title\"\n  }, \"协商议题\", -1 /* CACHED */)), $setup.negotiationTopicsLoading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, _cache[14] || (_cache[14] = [_createElementVNode(\"span\", {\n    class: \"loading_spinner\"\n  }, null, -1 /* CACHED */)]))) : $setup.negotiationTopics.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_19, [_createElementVNode(\"div\", {\n    class: \"negotiation-topic-card main\",\n    onClick: _cache[2] || (_cache[2] = function ($event) {\n      return $setup.openActivety($setup.negotiationTopics[0]);\n    })\n  }, [_createElementVNode(\"div\", _hoisted_20, _toDisplayString($setup.negotiationTopics[0].title), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_21, [$setup.negotiationTopics[0].titlePic ? (_openBlock(), _createElementBlock(\"img\", {\n    key: 0,\n    src: $setup.config.API_URL + '/image/' + $setup.negotiationTopics[0].titlePic,\n    alt: \"icon\"\n  }, null, 8 /* PROPS */, _hoisted_22)) : _createCommentVNode(\"v-if\", true)]), _cache[15] || (_cache[15] = _createElementVNode(\"button\", {\n    class: \"topic-btn\"\n  }, \"去看看\", -1 /* CACHED */))]), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.negotiationTopics.slice(1), function (item) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"negotiation-topic-card sub\",\n      key: item.id,\n      onClick: function onClick($event) {\n        return $setup.openActivety(item);\n      }\n    }, [_createElementVNode(\"div\", _hoisted_24, _toDisplayString(item.title), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_25, [_cache[16] || (_cache[16] = _createElementVNode(\"button\", {\n      class: \"topic-btn\",\n      style: {\n        \"margin\": \"0\"\n      }\n    }, \"去看看\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_26, [item.titlePic ? (_openBlock(), _createElementBlock(\"img\", {\n      key: 0,\n      src: $setup.config.API_URL + '/image/' + item.titlePic,\n      alt: \"icon\"\n    }, null, 8 /* PROPS */, _hoisted_27)) : _createCommentVNode(\"v-if\", true)])])], 8 /* PROPS */, _hoisted_23);\n  }), 128 /* KEYED_FRAGMENT */))])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_28, _cache[17] || (_cache[17] = [_createElementVNode(\"span\", null, \"暂无数据\", -1 /* CACHED */)]))), _createElementVNode(\"div\", {\n    class: \"negotiation-submit-btn-wrapper\",\n    onClick: _cache[3] || (_cache[3] = function ($event) {\n      return $setup.openActivety(null);\n    })\n  }, _cache[18] || (_cache[18] = [_createElementVNode(\"button\", {\n    class: \"negotiation-submit-btn\"\n  }, \"提交意见建议\", -1 /* CACHED */)]))]), _createCommentVNode(\" 网络议政与问卷调查卡片区域 \"), _createElementVNode(\"div\", {\n    class: \"custom-action-cards\"\n  }, [_createElementVNode(\"div\", {\n    class: \"action-card network\",\n    onClick: $setup.openNetwork\n  }, _cache[20] || (_cache[20] = [_createElementVNode(\"div\", {\n    class: \"action-card-content\"\n  }, [_createElementVNode(\"span\", {\n    class: \"action-title\"\n  }, \"参加网络议政\"), _createElementVNode(\"img\", {\n    class: \"action-arrow\",\n    src: _imports_3,\n    alt: \">\"\n  })], -1 /* CACHED */), _createElementVNode(\"img\", {\n    class: \"action-icon\",\n    src: _imports_4,\n    alt: \"网络议政\"\n  }, null, -1 /* CACHED */)])), _createElementVNode(\"div\", {\n    class: \"action-card survey\",\n    onClick: $setup.openSurvey\n  }, _cache[21] || (_cache[21] = [_createElementVNode(\"div\", {\n    class: \"action-card-content\"\n  }, [_createElementVNode(\"span\", {\n    class: \"action-title\"\n  }, \"参加问卷调查\"), _createElementVNode(\"img\", {\n    class: \"action-arrow\",\n    src: _imports_3,\n    alt: \">\"\n  })], -1 /* CACHED */), _createElementVNode(\"img\", {\n    class: \"action-icon\",\n    src: _imports_5,\n    alt: \"问卷调查\"\n  }, null, -1 /* CACHED */)]))]), _createCommentVNode(\" 视频+图文直播 \"), false ? (_openBlock(), _createElementBlock(\"div\", _hoisted_29, [_cache[24] || (_cache[24] = _createElementVNode(\"div\", {\n    class: \"video_graphic_live_title\"\n  }, \"视频+图文直播\", -1 /* CACHED */)), $setup.videoGraphicLiveLoading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_30, _cache[22] || (_cache[22] = [_createElementVNode(\"span\", {\n    class: \"loading_spinner\"\n  }, null, -1 /* CACHED */)]))) : $setup.videoGraphicLive.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_31, [_createElementVNode(\"div\", {\n    class: \"vg-carousel-img-wrapper\",\n    onTouchstart: $setup.onVGTouchStart,\n    onTouchmove: $setup.onVGTouchMove,\n    onTouchend: $setup.onVGTouchEnd,\n    onClick: _cache[4] || (_cache[4] = function ($event) {\n      return $setup.onVGCarouselClick($setup.videoGraphicLive[$setup.vgIndex]);\n    })\n  }, [_createElementVNode(\"img\", {\n    class: \"vg-carousel-img\",\n    src: $setup.videoGraphicLive[$setup.vgIndex].img,\n    alt: $setup.videoGraphicLive[$setup.vgIndex].title\n  }, null, 8 /* PROPS */, _hoisted_32), _createElementVNode(\"div\", _hoisted_33, [_createElementVNode(\"div\", _hoisted_34, _toDisplayString($setup.videoGraphicLive[$setup.vgIndex].title), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_35, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.videoGraphicLive, function (item, idx) {\n    return _openBlock(), _createElementBlock(\"span\", {\n      key: idx,\n      class: _normalizeClass([\"vg-dot\", {\n        active: idx === $setup.vgIndex\n      }]),\n      onClick: _withModifiers(function ($event) {\n        return $setup.goToVGCarousel(idx);\n      }, [\"stop\"])\n    }, null, 10 /* CLASS, PROPS */, _hoisted_36);\n  }), 128 /* KEYED_FRAGMENT */))])])], 32 /* NEED_HYDRATION */)])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_37, _cache[23] || (_cache[23] = [_createElementVNode(\"span\", null, \"暂无数据\", -1 /* CACHED */)])))])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 委员会客厅 \"), _createElementVNode(\"div\", _hoisted_38, [_createElementVNode(\"img\", {\n    src: _imports_6,\n    alt: \"\",\n    class: \"committee_living_room_img\",\n    onClick: _cache[5] || (_cache[5] = function ($event) {\n      return $setup.openCommitteeLivingRoom();\n    })\n  })]), _createCommentVNode(\" 协商议题征集 \"), $setup.negotiationtopicsCollectList && $setup.negotiationtopicsCollectList.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_39, [_cache[29] || (_cache[29] = _createElementVNode(\"div\", {\n    class: \"header_box\"\n  }, [_createElementVNode(\"span\", {\n    class: \"header_title\"\n  }, \"协商议题征集\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_40, [$setup.negotiationTopicsCollectLoading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_41, _cache[26] || (_cache[26] = [_createElementVNode(\"span\", {\n    class: \"loading_spinner\"\n  }, null, -1 /* CACHED */)]))) : $setup.negotiationtopicsCollectList.length > 0 ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createElementVNode(\"div\", _hoisted_42, _toDisplayString($setup.negotiationtopicsCollectList[0].title), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_43, _toDisplayString($setup.negotiationtopicsCollectList[0].content), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_44, \"发布机构：\" + _toDisplayString($setup.negotiationtopicsCollectList[0].publishOrganize), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_45, [_createElementVNode(\"div\", _hoisted_46, [$setup.negotiationtopicsCollectList[0].statusText !== '征集已结束' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_47, [_createTextVNode(_toDisplayString($setup.negotiationtopicsCollectList[0].statusText) + \" \", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_48, _toDisplayString($setup.negotiationtopicsCollectList[0].days) + \"天\", 1 /* TEXT */)])) : (_openBlock(), _createElementBlock(\"span\", _hoisted_49, _toDisplayString($setup.negotiationtopicsCollectList[0].statusText), 1 /* TEXT */))]), _createElementVNode(\"div\", _hoisted_50, \"已有\" + _toDisplayString($setup.negotiationtopicsCollectList[0].commentCount) + \"条建议\", 1 /* TEXT */)]), _createElementVNode(\"div\", {\n    class: \"negotiation-submit-btn-wrapper\",\n    onClick: _cache[6] || (_cache[6] = function ($event) {\n      return $setup.openCollectInfo($setup.negotiationtopicsCollectList[0]);\n    })\n  }, _cache[27] || (_cache[27] = [_createElementVNode(\"button\", {\n    class: \"negotiation-submit-btn\"\n  }, \"立即参与\", -1 /* CACHED */)]))], 64 /* STABLE_FRAGMENT */)) : (_openBlock(), _createElementBlock(\"div\", _hoisted_51, _cache[28] || (_cache[28] = [_createElementVNode(\"span\", null, \"暂无数据\", -1 /* CACHED */)])))])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 政协概况与文史资料卡片区域 \"), _createElementVNode(\"div\", {\n    class: \"overview_history_card\"\n  }, [_createElementVNode(\"div\", {\n    class: \"action_card overview\",\n    onClick: $setup.openOverview\n  }, _cache[30] || (_cache[30] = [_createElementVNode(\"div\", {\n    class: \"overview_history_card_content\"\n  }, [_createElementVNode(\"div\", {\n    class: \"action_title\"\n  }, \"政协概况\"), _createElementVNode(\"div\", {\n    class: \"action_info\"\n  }, \"查看详情\")], -1 /* CACHED */)])), _createElementVNode(\"div\", {\n    class: \"action_card history\",\n    onClick: $setup.openHistory\n  }, _cache[31] || (_cache[31] = [_createElementVNode(\"div\", {\n    class: \"overview_history_card_content\"\n  }, [_createElementVNode(\"div\", {\n    class: \"action_title\"\n  }, \"文史资料\"), _createElementVNode(\"div\", {\n    class: \"action_info\"\n  }, \"查看详情\")], -1 /* CACHED */)]))]), _createCommentVNode(\" 文史资料征集 \"), $setup.culturalHistoryList && $setup.culturalHistoryList.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_52, [_createElementVNode(\"div\", _hoisted_53, [_cache[32] || (_cache[32] = _createElementVNode(\"span\", {\n    class: \"header_title\"\n  }, \"文史资料征集\", -1 /* CACHED */)), _createElementVNode(\"span\", {\n    class: \"header_all\",\n    onClick: _cache[7] || (_cache[7] = function ($event) {\n      return $setup.openHistoricalCollecMore();\n    })\n  }, \"全部 >\")]), _createElementVNode(\"div\", _hoisted_54, [_createElementVNode(\"div\", _hoisted_55, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.culturalHistoryList, function (item) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"cultural-history-card\",\n      key: item.id,\n      onClick: function onClick($event) {\n        return $setup.goToCulturalDetail(item);\n      }\n    }, [_createElementVNode(\"div\", _hoisted_57, _toDisplayString(item.theme), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_58, [_cache[33] || (_cache[33] = _createElementVNode(\"button\", {\n      class: \"card-btn\"\n    }, \"去看看\", -1 /* CACHED */)), _createElementVNode(\"img\", {\n      class: \"card-icon\",\n      src: item.icon,\n      alt: \"icon\"\n    }, null, 8 /* PROPS */, _hoisted_59)])], 8 /* PROPS */, _hoisted_56);\n  }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"div\", {\n    class: \"historical_collection_wrapper\",\n    onClick: _cache[8] || (_cache[8] = function ($event) {\n      return $setup.openSubmitHistorical();\n    })\n  }, _cache[34] || (_cache[34] = [_createElementVNode(\"button\", {\n    class: \"historical_collection_wrapper_btn\"\n  }, \"我要提交文史资料\", -1 /* CACHED */)]))])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 文史馆 \"), _createElementVNode(\"div\", _hoisted_60, [_createElementVNode(\"img\", {\n    src: _imports_6,\n    alt: \"\",\n    class: \"archives_institute_img\",\n    onClick: _cache[9] || (_cache[9] = function ($event) {\n      return _ctx.openArchivesInstitute();\n    })\n  })])]);\n}", "map": {"version": 3, "names": ["_imports_0", "_imports_1", "_imports_2", "_imports_3", "_imports_4", "_imports_5", "_imports_6", "class", "style", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "$setup", "loadingPage", "_hoisted_2", "_cache", "_createElementVNode", "_hoisted_3", "_hoisted_4", "onClick", "$event", "openNewMore", "loading", "_hoisted_5", "carouselList", "length", "_hoisted_6", "_createVNode", "_component_van_swipe", "autoplay", "loop", "_Fragment", "_renderList", "item", "index", "_createBlock", "_component_van_swipe_item", "key", "onCarouselClick", "_hoisted_7", "src", "config", "API_URL", "infoPic", "alt", "title", "_hoisted_9", "_hoisted_10", "_toDisplayString", "infoTitle", "_hoisted_11", "idx", "_normalizeClass", "active", "carouselIndex", "_ctx", "goToNewsCarousel", "_hoisted_13", "_hoisted_14", "isPreheat", "_hoisted_15", "openActivety", "_hoisted_17", "negotiationTopicsLoading", "_hoisted_18", "negotiationTopics", "_hoisted_19", "_hoisted_20", "_hoisted_21", "titlePic", "slice", "id", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_28", "openNetwork", "openSurvey", "_hoisted_29", "videoGraphicLiveLoading", "_hoisted_30", "videoGraphicLive", "_hoisted_31", "onTouchstart", "onVGTouchStart", "onTouchmove", "onVGTouchMove", "onTouchend", "onVGTouchEnd", "onVGCarouselClick", "vgIndex", "img", "_hoisted_33", "_hoisted_34", "_hoisted_35", "_withModifiers", "goToVGCarousel", "_hoisted_37", "_hoisted_38", "openCommitteeLivingRoom", "negotiationtopicsCollectList", "_hoisted_39", "_hoisted_40", "negotiationTopicsCollectLoading", "_hoisted_41", "_hoisted_42", "_hoisted_43", "content", "_hoisted_44", "publishOrganize", "_hoisted_45", "_hoisted_46", "statusText", "_hoisted_47", "_hoisted_48", "days", "_hoisted_49", "_hoisted_50", "commentCount", "openCollectInfo", "_hoisted_51", "openOverview", "openHistory", "culturalHistoryList", "_hoisted_52", "_hoisted_53", "openHistoricalCollecMore", "_hoisted_54", "_hoisted_55", "goToCulturalDetail", "_hoisted_57", "theme", "_hoisted_58", "icon", "openSubmitHistorical", "_hoisted_60", "openArchivesInstitute"], "sources": ["D:\\zy\\xm\\h5\\i西安\\appperformduties\\src\\views\\LayoutHome\\LayoutHome.vue"], "sourcesContent": ["<template>\r\n  <div class=\"LayoutHome\">\r\n    <!-- 全局loading遮罩 -->\r\n    <div v-if=\"loadingPage\"\r\n      class=\"global-loading-mask flex_box flex_flex_direction_column flex_align_center flex_justify_content\">\r\n      <div class=\"global-loading-spinner\"></div>\r\n      <div class=\"global-loading-text\">加载中...</div>\r\n    </div>\r\n    <!-- 顶部背景和logo -->\r\n    <div class=\"top_bg\">\r\n      <img src=\"@/assets/img/icon_top_bg.png\" alt=\"\" class=\"bg_img\">\r\n      <div class=\"logo\">\r\n        <img src=\"@/assets/img/icon_top_logo.png\" alt=\"\" style=\"width: 180px;height: 30px;\">\r\n      </div>\r\n      <div class=\"top_text\">\r\n        <img src=\"@/assets/img/icon_top_text.png\" alt=\"\" style=\"width: 277px;height: 55px;\">\r\n      </div>\r\n    </div>\r\n    <!-- 政协资讯 -->\r\n    <div class=\"new_box\">\r\n      <div class=\"header_box\">\r\n        <span class=\"header_title\">政协资讯</span>\r\n        <span class=\"header_all\" @click=\"openNewMore()\">全部 &gt;</span>\r\n      </div>\r\n      <template v-if=\"loading\">\r\n        <div class=\"loading\">\r\n          <span class=\"loading_spinner\"></span>\r\n        </div>\r\n      </template>\r\n      <template v-else-if=\"carouselList.length > 0\">\r\n        <div class=\"carousel\">\r\n          <van-swipe class=\"swiper-container\" :autoplay=\"3000\" indicator-color=\"#fff\" :show-indicators=\"true\"\r\n            :loop=\"true\">\r\n            <van-swipe-item v-for=\"(item, index) in carouselList\" :key=\"index\" class=\"swiper-item\"\r\n              @click=\"onCarouselClick(item)\">\r\n              <div class=\"swiper-image-container\">\r\n                <img :src=\"config.API_URL + '/image/' + item.infoPic\" :alt=\"item.title\" class=\"swiper-image\">\r\n                <div class=\"swiper-overlay\">\r\n                  <div class=\"swiper-title\">{{ item.infoTitle }}</div>\r\n                </div>\r\n                <div class=\"carousel-dots\">\r\n                  <span v-for=\"(item, idx) in carouselList\" :key=\"idx\" class=\"dot\"\r\n                    :class=\"{ active: idx === carouselIndex }\" @click=\"goToNewsCarousel(idx)\"></span>\r\n                </div>\r\n              </div>\r\n            </van-swipe-item>\r\n          </van-swipe>\r\n        </div>\r\n      </template>\r\n      <template v-else>\r\n        <div class=\"loading\">\r\n          <span>暂无数据</span>\r\n        </div>\r\n      </template>\r\n    </div>\r\n    <!-- 协商活动预热 -->\r\n    <div class=\"negotiation_activities_preheating\">\r\n      <!-- 协商活动预热头部 -->\r\n      <div class=\"header_box\">\r\n        <span class=\"header_title\">协商活动预热</span>\r\n      </div>\r\n      <!-- 协商活动预热图片 -->\r\n      <div class=\"negotiation_activity_preheat\" v-if=\"isPreheat == '1'\">\r\n        <img :src=\"config.API_URL + '/pageImg/open/ixian_preheat_bg'\" alt=\"\" class=\"negotiation_activity_preheat_img\"\r\n          @click=\"openActivety(null)\">\r\n      </div>\r\n      <!-- 协商议题区域 -->\r\n      <div class=\"negotiation_topics\">\r\n        <div class=\"negotiation_topics_title\">协商议题</div>\r\n        <template v-if=\"negotiationTopicsLoading\">\r\n          <div class=\"loading\">\r\n            <span class=\"loading_spinner\"></span>\r\n          </div>\r\n        </template>\r\n        <template v-else-if=\"negotiationTopics.length > 0\">\r\n          <div class=\"negotiation_topics_content\">\r\n            <div class=\"negotiation-topic-card main\" @click=\"openActivety(negotiationTopics[0])\">\r\n              <div class=\"topic-title\">{{ negotiationTopics[0].title }}</div>\r\n              <div class=\"topic-image\">\r\n                <img v-if=\"negotiationTopics[0].titlePic\"\r\n                  :src=\"config.API_URL + '/image/' + negotiationTopics[0].titlePic\" alt=\"icon\" />\r\n              </div>\r\n              <button class=\"topic-btn\">去看看</button>\r\n            </div>\r\n            <div class=\"negotiation-topic-card sub\" v-for=\"item in negotiationTopics.slice(1)\" :key=\"item.id\"\r\n              @click=\"openActivety(item)\">\r\n              <div class=\"topic-title\">{{ item.title }}</div>\r\n              <div class=\"sub-topic-btn\">\r\n                <button class=\"topic-btn\" style=\"margin:0;\">去看看</button>\r\n                <div class=\"topic-image small\" style=\"margin:0;\">\r\n                  <img v-if=\"item.titlePic\" :src=\"config.API_URL + '/image/' + item.titlePic\" alt=\"icon\" />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n        <template v-else>\r\n          <div class=\"loading\">\r\n            <span>暂无数据</span>\r\n          </div>\r\n        </template>\r\n        <div class=\"negotiation-submit-btn-wrapper\" @click=\"openActivety(null)\">\r\n          <button class=\"negotiation-submit-btn\">提交意见建议</button>\r\n        </div>\r\n      </div>\r\n      <!-- 网络议政与问卷调查卡片区域 -->\r\n      <div class=\"custom-action-cards\">\r\n        <div class=\"action-card network\" @click=\"openNetwork\">\r\n          <div class=\"action-card-content\">\r\n            <span class=\"action-title\">参加网络议政</span>\r\n            <img class=\"action-arrow\" src=\"@/assets/img/icon_arrow.png\" alt=\">\" />\r\n          </div>\r\n          <img class=\"action-icon\" src=\"@/assets/img/icon_network.png\" alt=\"网络议政\" />\r\n        </div>\r\n        <div class=\"action-card survey\" @click=\"openSurvey\">\r\n          <div class=\"action-card-content\">\r\n            <span class=\"action-title\">参加问卷调查</span>\r\n            <img class=\"action-arrow\" src=\"@/assets/img/icon_arrow.png\" alt=\">\" />\r\n          </div>\r\n          <img class=\"action-icon\" src=\"@/assets/img/icon_survey.png\" alt=\"问卷调查\" />\r\n        </div>\r\n      </div>\r\n      <!-- 视频+图文直播 -->\r\n      <div class=\"video_graphic_live\" v-if=\"false\">\r\n        <div class=\"video_graphic_live_title\">视频+图文直播</div>\r\n        <template v-if=\"videoGraphicLiveLoading\">\r\n          <div class=\"loading\">\r\n            <span class=\"loading_spinner\"></span>\r\n          </div>\r\n        </template>\r\n        <template v-else-if=\"videoGraphicLive.length > 0\">\r\n          <div class=\"video_graphic_live_content\">\r\n            <div class=\"vg-carousel-img-wrapper\" @touchstart=\"onVGTouchStart\" @touchmove=\"onVGTouchMove\"\r\n              @touchend=\"onVGTouchEnd\" @click=\"onVGCarouselClick(videoGraphicLive[vgIndex])\">\r\n              <img class=\"vg-carousel-img\" :src=\"videoGraphicLive[vgIndex].img\"\r\n                :alt=\"videoGraphicLive[vgIndex].title\" />\r\n              <div class=\"vg-carousel-bottom-bar\">\r\n                <div class=\"vg-carousel-title\">{{ videoGraphicLive[vgIndex].title }}</div>\r\n                <div class=\"vg-carousel-dots\">\r\n                  <span v-for=\"(item, idx) in videoGraphicLive\" :key=\"idx\" class=\"vg-dot\"\r\n                    :class=\"{ active: idx === vgIndex }\" @click.stop=\"goToVGCarousel(idx)\"></span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n        <template v-else>\r\n          <div class=\"loading\">\r\n            <span>暂无数据</span>\r\n          </div>\r\n        </template>\r\n      </div>\r\n    </div>\r\n    <!-- 委员会客厅 -->\r\n    <div class=\"committee_living_room\">\r\n      <img src=\"@/assets/img/icon_committee_living_room.png\" alt=\"\" class=\"committee_living_room_img\"\r\n        @click=\"openCommitteeLivingRoom()\">\r\n    </div>\r\n    <!-- 协商议题征集 -->\r\n    <div class=\"negotiation_topics_collect\"\r\n      v-if=\"negotiationtopicsCollectList && negotiationtopicsCollectList.length > 0\">\r\n      <div class=\"header_box\">\r\n        <span class=\"header_title\">协商议题征集</span>\r\n      </div>\r\n      <div class=\"negotiation_topics_collect_box\">\r\n        <template v-if=\"negotiationTopicsCollectLoading\">\r\n          <div class=\"loading\">\r\n            <span class=\"loading_spinner\"></span>\r\n          </div>\r\n        </template>\r\n        <template v-else-if=\"negotiationtopicsCollectList.length > 0\">\r\n          <div class=\"topics_collect_title\">{{ negotiationtopicsCollectList[0].title }}</div>\r\n          <div class=\"topics_collect_content\">\r\n            {{ negotiationtopicsCollectList[0].content }}\r\n          </div>\r\n          <div class=\"topics_collect_organize\">发布机构：{{ negotiationtopicsCollectList[0].publishOrganize }}</div>\r\n          <div class=\"topics_collect_flex\">\r\n            <div class=\"topics_collect_flex_text1\">\r\n              <span v-if=\"negotiationtopicsCollectList[0].statusText !== '征集已结束'\">\r\n                {{ negotiationtopicsCollectList[0].statusText }}\r\n                <span class=\"blue\">{{ negotiationtopicsCollectList[0].days }}天</span>\r\n              </span>\r\n              <span v-else>\r\n                {{ negotiationtopicsCollectList[0].statusText }}\r\n              </span>\r\n            </div>\r\n            <div class=\"topics_collect_flex_text2\">已有{{ negotiationtopicsCollectList[0].commentCount }}条建议</div>\r\n          </div>\r\n          <div class=\"negotiation-submit-btn-wrapper\" @click=\"openCollectInfo(negotiationtopicsCollectList[0])\">\r\n            <button class=\"negotiation-submit-btn\">立即参与</button>\r\n          </div>\r\n        </template>\r\n        <template v-else>\r\n          <div class=\"loading\">\r\n            <span>暂无数据</span>\r\n          </div>\r\n        </template>\r\n      </div>\r\n    </div>\r\n    <!-- 政协概况与文史资料卡片区域 -->\r\n    <div class=\"overview_history_card\">\r\n      <div class=\"action_card overview\" @click=\"openOverview\">\r\n        <div class=\"overview_history_card_content\">\r\n          <div class=\"action_title\">政协概况</div>\r\n          <div class=\"action_info\">查看详情</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"action_card history\" @click=\"openHistory\">\r\n        <div class=\"overview_history_card_content\">\r\n          <div class=\"action_title\">文史资料</div>\r\n          <div class=\"action_info\">查看详情</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 文史资料征集 -->\r\n    <div class=\"cultural_historical_collection\" v-if=\"culturalHistoryList && culturalHistoryList.length > 0\">\r\n      <div class=\"header_box\">\r\n        <span class=\"header_title\">文史资料征集</span>\r\n        <span class=\"header_all\" @click=\"openHistoricalCollecMore()\">全部 &gt;</span>\r\n      </div>\r\n      <div class=\"cultural_historical_collection_content\">\r\n        <div class=\"historical_collection_content_flex\">\r\n          <div class=\"cultural-history-card\" v-for=\"item in culturalHistoryList\" :key=\"item.id\"\r\n            @click=\"goToCulturalDetail(item)\">\r\n            <div class=\"card-title\">{{ item.theme }}</div>\r\n            <div class=\"card-btn-icon\">\r\n              <button class=\"card-btn\">去看看</button>\r\n              <img class=\"card-icon\" :src=\"item.icon\" alt=\"icon\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"historical_collection_wrapper\" @click=\"openSubmitHistorical()\">\r\n          <button class=\"historical_collection_wrapper_btn\">我要提交文史资料</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 文史馆 -->\r\n    <div class=\"archives_institute\">\r\n      <img src=\"@/assets/img/icon_committee_living_room.png\" alt=\"\" class=\"archives_institute_img\"\r\n        @click=\"openArchivesInstitute()\">\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'LayoutHome' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted, onBeforeUnmount } from 'vue'\r\nimport { useRouter, useRoute } from 'vue-router'\r\nimport config from '@/config'\r\nimport { showToast } from 'vant'\r\nimport store from '@/store'\r\nimport { getAuthCode, removeTag } from '@/utils/utils'\r\nimport { isInThirdPartyApp, getDefaultUser, getEnvironmentType } from '@/utils/environment'\r\nconst router = useRouter()\r\nconst route = useRoute()\r\n\r\nconst carouselList = ref([])\r\nconst loading = ref(false)\r\n\r\nconst carouselIndex = ref(0)\r\nlet carouselTimer = null\r\n// const touchStartX = ref(0)\r\n// const touchEndX = ref(0)\r\n\r\nconst isPreheat = ref('')\r\nconst defaultActivityId = ref('')\r\nconst negotiationTopics = ref([])\r\nconst negotiationTopicsLoading = ref(false)\r\n\r\nconst videoGraphicLiveLoading = ref(false)\r\nconst videoGraphicLive = ref([\r\n  {\r\n    img: require('@/assets/img/bg_survey_list_zx.png'),\r\n    title: '市政协召开全市“深化六个改革”重点工作宣讲会',\r\n    id: 1\r\n  },\r\n  {\r\n    img: require('@/assets/img/icon_top_bg.png'),\r\n    title: '政协委员积极建言献策 助力城市发展',\r\n    id: 2\r\n  },\r\n  {\r\n    img: require('@/assets/img/icon_top_logo.png'),\r\n    title: '协商议政新模式 推动社会治理创新',\r\n    id: 3\r\n  }\r\n])\r\nconst vgIndex = ref(0)\r\nlet vgTimer = null\r\nconst vgTouchStartX = ref(0)\r\nconst vgTouchEndX = ref(0)\r\n\r\nconst negotiationTopicsCollectLoading = ref(false)\r\nconst negotiationtopicsCollectList = ref([])\r\n\r\nconst culturalHistoryList = ref([])\r\nconst loadingPage = ref(true)\r\n\r\nonMounted(() => {\r\n  document.title = '西安政协'\r\n  // 获取i西安token\r\n  getIXiAnToken()\r\n})\r\nonBeforeUnmount(() => {\r\n  stopNewsCarousel()\r\n  stopVGCarousel()\r\n})\r\n// 获取i西安的token\r\nconst getIXiAnToken = async () => {\r\n  loadingPage.value = true\r\n\r\n  // 检测环境，如果不在第三方app中，使用默认账号\r\n  const inThirdPartyApp = isInThirdPartyApp()\r\n  const envType = getEnvironmentType()\r\n\r\n  console.log(`环境检测结果: ${inThirdPartyApp ? '第三方app' : '普通浏览器'} (${envType})`)\r\n\r\n  if (!inThirdPartyApp) {\r\n    console.log('当前不在第三方app环境中，使用默认账号登录')\r\n    const publicUser = sessionStorage.getItem('public_user')\r\n    if (!publicUser) {\r\n      const defaultUser = getDefaultUser()\r\n      autoLogin(defaultUser)\r\n    } else {\r\n      await getTopCarousel()\r\n      loadingPage.value = false\r\n    }\r\n    return\r\n  }\r\n\r\n  // 在第三方app环境中，继续原有逻辑\r\n  try {\r\n    const res = await api.ixaToekn({})\r\n    if (res.data.data) {\r\n      getIXiAnCode(res.data.data.jsapiToken)\r\n    } else {\r\n      console.warn('JsapiToken获取失败，尝试使用默认账号')\r\n      loadingPage.value = false\r\n      setTimeout(() => {\r\n        showToast('JsapiToken获取失败，使用默认账号登录')\r\n        const defaultUser = getDefaultUser()\r\n        autoLogin(defaultUser)\r\n      }, 500)\r\n    }\r\n  } catch (error) {\r\n    console.error('获取JsapiToken异常:', error)\r\n    loadingPage.value = false\r\n    setTimeout(() => {\r\n      showToast('网络异常，使用默认账号登录')\r\n      const defaultUser = getDefaultUser()\r\n      autoLogin(defaultUser)\r\n    }, 500)\r\n  }\r\n}\r\n// 获取i西安的code\r\nconst getIXiAnCode = async (jsapiToken) => {\r\n  // 再次检测环境，确保在第三方app中\r\n  if (!isInThirdPartyApp()) {\r\n    console.log('getIXiAnCode: 当前不在第三方app环境中，使用默认账号')\r\n    const defaultUser = getDefaultUser()\r\n    autoLogin(defaultUser)\r\n    return\r\n  }\r\n\r\n  try {\r\n    getAuthCode({\r\n      // \"appId\": \"9de99f6934b94cf38c75ce40c9ddf8a2\", //测试应用id\r\n      \"appId\": \"b105ea5a2fca4b28afdd52558952552f\", //正式应用id\r\n      \"forceScopes\": ['ixa_user_info'], //授权的能力标识数组，\r\n      \"jsapiToken\": jsapiToken //应用授权的请求码\r\n    }, function (result) {\r\n      if (result.code === 0) {\r\n        console.log('✅ 第三方app授权成功')\r\n        getIXiAnUser(result.data.authCode)\r\n      } else {\r\n        console.error('❌ 第三方app授权失败:', result.message)\r\n        // 如果授权失败，降级到默认账号\r\n        console.log('授权失败，降级使用默认账号登录')\r\n        showToast('授权失败，使用默认账号登录')\r\n        const defaultUser = getDefaultUser()\r\n        autoLogin(defaultUser)\r\n      }\r\n    })\r\n  } catch (error) {\r\n    console.error('getAuthCode调用异常:', error)\r\n    console.log('getAuthCode异常，降级使用默认账号登录')\r\n    showToast('授权异常，使用默认账号登录')\r\n    const defaultUser = getDefaultUser()\r\n    autoLogin(defaultUser)\r\n  }\r\n}\r\n// 获取i西安的用户信息\r\nconst getIXiAnUser = async (code) => {\r\n  const res = await api.ixaUser({ authCode: code })\r\n  console.log('用户信息-->' + JSON.stringify(res))\r\n  let ixaUser = res.data.data.userInfo\r\n  autoLogin(ixaUser)\r\n  localStorage.setItem('ixaUser', JSON.stringify(ixaUser))\r\n}\r\n// 公众登录\r\nconst autoLogin = async (user) => {\r\n  if (!user.userName) {\r\n    loadingPage.value = false\r\n    showToast('请您先进行实名认证！')\r\n    setTimeout(() => {\r\n      window.history.back()\r\n    }, 800)\r\n    return\r\n  }\r\n  try {\r\n    const { data } = await api.login({\r\n      grant_type: 'anonymoussso',\r\n      userName: user.userName,\r\n      mobile: user.phone\r\n    }, {\r\n      headers: {\r\n        authorization: 'basic enlzb2Z0Onp5c29mdCo2MDc5'\r\n      }\r\n    })\r\n    sessionStorage.setItem('token', data.token)\r\n    sessionStorage.setItem('refresh_token', `bearer ${data.refreshToken.value}`)\r\n    sessionStorage.setItem('expires', data.expires_in)\r\n    sessionStorage.setItem('expiration', data.refreshToken.expiration)\r\n    sessionStorage.setItem('verify', data.passwordElementMatchReg ? 1 : 0)\r\n    loadingPage.value = false // 登录成功后loading消失\r\n    await store.dispatch('loginUser')\r\n    // 获取置顶资讯\r\n    await getTopCarousel()\r\n  } catch (error) {\r\n    loadingPage.value = false // 登录失败也消失loading\r\n    showToast(error.message || '登录失败')\r\n  }\r\n}\r\n// 获取置顶资讯\r\nconst getTopCarousel = async () => {\r\n  loading.value = true\r\n  const params = {\r\n    objectParam: {},\r\n    pageNo: 1,\r\n    pageSize: 5,\r\n    keyword: '',\r\n    query: {\r\n      columnId: route.query.columnId || '1887325961586761729',\r\n      moduleId: 1,\r\n      passFlag: 1\r\n    },\r\n    wheres: [{ columnId: 'zy_news_content_1_is_top', queryType: 'EQ', value: '1' }],\r\n    tableId: 'zy_news_content_1'\r\n  }\r\n  const { data } = await api.newsContentList(params)\r\n  carouselList.value = data\r\n  loading.value = false\r\n  startNewsCarousel()\r\n  getConfig()\r\n}\r\n// 获取配置\r\nconst getConfig = async () => {\r\n  const { data } = await api.globalReadOpenConfig({ codes: ['ixian_preheat'] })\r\n  isPreheat.value = data.ixian_preheat\r\n  getConsultActivityList()\r\n}\r\n// 获取协商活动\r\nconst getConsultActivityList = async (pageSize) => {\r\n  if (!pageSize) {\r\n    negotiationTopicsLoading.value = true\r\n  }\r\n  const params = {\r\n    pageNo: 1,\r\n    pageSize: pageSize || 3,\r\n    year: 2025,\r\n    objectParam: {}\r\n  }\r\n  if (pageSize) {\r\n    params.objectParam.showPublic = '1'\r\n  } else {\r\n    params.objectParam.ifLatest = '1'\r\n  }\r\n  const { data } = await api.consultActivityList(params)\r\n  if (pageSize) {\r\n    if (data.length > 0) {\r\n      defaultActivityId.value = data[0].id\r\n    } else {\r\n      showToast('暂无设置当前协商！')\r\n    }\r\n  } else {\r\n    negotiationTopics.value = data\r\n    negotiationTopicsLoading.value = false\r\n    getVideoGraphicLive()\r\n    getNegotiationTopicsCollect()\r\n  }\r\n}\r\n// 获取视频+图文直播\r\nconst getVideoGraphicLive = async () => {\r\n  videoGraphicLiveLoading.value = false\r\n  startVGCarousel()\r\n}\r\n// 获取协商议题征集\r\nconst getNegotiationTopicsCollect = async () => {\r\n  negotiationTopicsCollectLoading.value = true\r\n  const { code, data } = await api.opinioncollectList({\r\n    pageNo: 1,\r\n    pageSize: 1,\r\n    collectStatus: 2,\r\n    query: { businessCode: 'discussioncollect' }\r\n  })\r\n  if (code === 200) {\r\n    const now = new Date().getTime()\r\n    const oneDay = 24 * 60 * 60 * 1000\r\n    const processedData = (data || []).map(item => {\r\n      const start = new Date(item.startDate).getTime()\r\n      const end = new Date(item.endDate).getTime()\r\n      let statusText = ''\r\n      let days = 0\r\n      if (now > end) {\r\n        statusText = '征集已结束'\r\n        days = 0\r\n      } else if (now > start) {\r\n        statusText = '征集结束剩余：'\r\n        days = Math.floor((end - now) / oneDay)\r\n      } else {\r\n        statusText = '征集开始还有：'\r\n        days = Math.floor((start - now) / oneDay)\r\n      }\r\n      const canJoin = now > start && now < end && item.isJoinName === '未参与'\r\n      return {\r\n        ...item,\r\n        statusText,\r\n        days,\r\n        canJoin\r\n      }\r\n    })\r\n    negotiationtopicsCollectList.value = negotiationtopicsCollectList.value.concat(processedData)\r\n    if (negotiationtopicsCollectList.value[0]) {\r\n      negotiationtopicsCollectList.value[0].content = removeTag(negotiationtopicsCollectList.value[0].content)\r\n    }\r\n    negotiationTopicsCollectLoading.value = false\r\n    getCulturalHistoricalCollection()\r\n  }\r\n}\r\n// 通用轮播控制方法\r\nconst startCarousel = (timer, index, list, callback) => {\r\n  if (timer) clearInterval(timer)\r\n  return setInterval(() => {\r\n    index.value = (index.value + 1) % list.value.length\r\n  }, 30000)\r\n}\r\nconst stopCarousel = (timer) => {\r\n  if (timer) {\r\n    clearInterval(timer)\r\n    return null\r\n  }\r\n  return null\r\n}\r\nconst goToCarousel = (idx, index, timer, list, callback) => {\r\n  timer = stopCarousel(timer)\r\n  index.value = idx\r\n  return startCarousel(timer, index, list, callback)\r\n}\r\nconst prevCarousel = (index, list) => {\r\n  index.value = (index.value - 1 + list.value.length) % list.value.length\r\n}\r\nconst nextCarousel = (index, list) => {\r\n  index.value = (index.value + 1) % list.value.length\r\n}\r\n// 通用触摸处理\r\nconst onTouchStart = (e, touchStartX, touchEndX) => {\r\n  touchStartX.value = e.touches[0].clientX\r\n  touchEndX.value = e.touches[0].clientX\r\n}\r\nconst onTouchMove = (e, touchEndX) => {\r\n  touchEndX.value = e.touches[0].clientX\r\n}\r\nconst onTouchEnd = (e, touchStartX, touchEndX, stopFn, nextFn, prevFn, startFn) => {\r\n  const deltaX = touchEndX.value - touchStartX.value\r\n  if (Math.abs(deltaX) > 50) {\r\n    stopFn()\r\n    if (deltaX < 0) {\r\n      nextFn()\r\n    } else {\r\n      prevFn()\r\n    }\r\n    startFn()\r\n  }\r\n  touchStartX.value = 0\r\n  touchEndX.value = 0\r\n}\r\n// 资讯轮播图相关方法\r\nconst startNewsCarousel = () => {\r\n  carouselTimer = startCarousel(carouselTimer, carouselIndex, carouselList)\r\n}\r\nconst stopNewsCarousel = () => {\r\n  carouselTimer = stopCarousel(carouselTimer)\r\n}\r\n// 打开资讯详情\r\nconst onCarouselClick = (row) => {\r\n  router.push({ path: '/NewDetails', query: { id: row.id } })\r\n}\r\n// 打开全部资讯\r\nconst openNewMore = () => {\r\n  router.push({ path: '/NewList', query: { utype: '1' } })\r\n}\r\n// 跳转协商活动\r\nconst openActivety = async (_item) => {\r\n  if (_item) {\r\n    router.push({ path: '/NegotiationActivityPage', query: { id: _item.id } })\r\n  } else {\r\n    await getConsultActivityList(1)\r\n    if (defaultActivityId.value) {\r\n      router.push({ path: '/NegotiationActivityPage', query: { id: defaultActivityId.value } })\r\n    }\r\n  }\r\n}\r\n// 跳转网络议政\r\nconst openNetwork = () => {\r\n  router.push({ path: '/NetworkDiscussList' })\r\n}\r\n// 跳转问卷调查\r\nconst openSurvey = () => {\r\n  router.push({ path: '/QuestionnaireList' })\r\n}\r\n// 视频+图文直播轮播图相关方法\r\nconst startVGCarousel = () => {\r\n  vgTimer = startCarousel(vgTimer, vgIndex, videoGraphicLive)\r\n}\r\nconst stopVGCarousel = () => {\r\n  vgTimer = stopCarousel(vgTimer)\r\n}\r\nconst goToVGCarousel = (idx) => {\r\n  vgTimer = goToCarousel(idx, vgIndex, vgTimer, videoGraphicLive)\r\n}\r\nconst prevVGCarousel = () => {\r\n  prevCarousel(vgIndex, videoGraphicLive)\r\n}\r\nconst nextVGCarousel = () => {\r\n  nextCarousel(vgIndex, videoGraphicLive)\r\n}\r\nconst onVGTouchStart = (e) => {\r\n  onTouchStart(e, vgTouchStartX, vgTouchEndX)\r\n}\r\nconst onVGTouchMove = (e) => {\r\n  onTouchMove(e, vgTouchEndX)\r\n}\r\nconst onVGTouchEnd = (e) => {\r\n  onTouchEnd(e, vgTouchStartX, vgTouchEndX, stopVGCarousel, nextVGCarousel, prevVGCarousel, startVGCarousel)\r\n}\r\n// 跳转视频+图文直播详情\r\nconst onVGCarouselClick = (_item) => {\r\n  // 跳转或弹窗逻辑\r\n  // router.push({ path: '/VideoDetail', query: { id: _item.id } })\r\n}\r\n// 跳转委员会客厅\r\nconst openCommitteeLivingRoom = () => {\r\n  // router.push({ path: '/CommitteeLivingRoomList' })\r\n}\r\n// 跳转协商议题征集详情\r\nconst openCollectInfo = (_item) => {\r\n  router.push({ path: '/NegotiationTopicsDetails', query: { id: _item.id } })\r\n}\r\n// 跳转政协概况\r\nconst openOverview = () => {\r\n  window.open('https://www.xa-cppcc.gov.cn/zxgk/zyzn/4.html', '_blank');\r\n}\r\n// 跳转文史资料\r\nconst openHistory = () => {\r\n  router.push({ path: '/CultureHistory' })\r\n}\r\n// 获取文史资料征集\r\nconst getCulturalHistoricalCollection = async () => {\r\n  const params = {\r\n    pageNo: 1,\r\n    pageSize: 2,\r\n    query: {\r\n      channelId: '1935275534502072322',\r\n      isDraft: 0\r\n    },\r\n    tableId: 'id_message_notification',\r\n    wheres: []\r\n  }\r\n  const { data, code } = await api.notificationList(params)\r\n  if (code == 200) {\r\n    const icons = [\r\n      require('@/assets/img/icon_culture_history01.png'),\r\n      require('@/assets/img/icon_culture_history02.png')\r\n    ]\r\n    culturalHistoryList.value = (data || []).map((item, idx) => ({\r\n      ...item,\r\n      icon: icons[idx]\r\n    }))\r\n  } else {\r\n    showToast('文史资料征集请求失败！')\r\n  }\r\n}\r\n// 文史资料征集进详情\r\nconst goToCulturalDetail = (_item) => {\r\n  router.push({ path: '/NoticeDetails', query: { id: _item.id } })\r\n}\r\n// 打开文史资料征集进全部\r\nconst openHistoricalCollecMore = () => {\r\n  router.push({ path: '/NoticeList', query: {} })\r\n}\r\n// 提交文史资料\r\nconst openSubmitHistorical = () => {\r\n  router.push({ path: '/CultureHistoryAdd' })\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.LayoutHome {\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow: auto;\r\n\r\n  // 顶部背景和logo\r\n  .top_bg {\r\n    position: relative;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 220px;\r\n    overflow: hidden;\r\n\r\n    .bg_img {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      width: 100%;\r\n      height: 100%;\r\n      object-fit: cover;\r\n      z-index: 1;\r\n    }\r\n\r\n    .logo {\r\n      position: relative;\r\n      z-index: 2;\r\n      align-self: flex-start;\r\n      margin: 32px 0 0 15px;\r\n    }\r\n\r\n    .top_text {\r\n      position: relative;\r\n      z-index: 2;\r\n      margin-top: auto;\r\n      margin-bottom: 43px;\r\n    }\r\n  }\r\n\r\n  // 政协资讯\r\n  .new_box {\r\n    margin-top: -32px;\r\n    background-image: url('@/assets/img/icon_new_bg.png');\r\n    background-size: 100% 100%;\r\n    height: 340px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    position: relative;\r\n    z-index: 3;\r\n\r\n    .carousel {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      margin: 15px;\r\n      height: 100%;\r\n\r\n      .swiper-container {\r\n        width: 100%;\r\n        height: 100%;\r\n\r\n        .swiper-item {\r\n          width: 100%;\r\n\r\n          .swiper-image-container {\r\n            position: relative;\r\n            width: 100%;\r\n            height: 100%;\r\n            box-sizing: border-box;\r\n\r\n            .swiper-image {\r\n              width: 100%;\r\n              height: 180px;\r\n              object-fit: cover;\r\n              border-radius: 5px;\r\n            }\r\n\r\n            .swiper-overlay {\r\n              padding: 5px;\r\n\r\n              .swiper-title {\r\n                color: #3A3A3A;\r\n                font-size: 14px;\r\n                margin: 0 0 8px 0;\r\n                display: -webkit-box;\r\n                -webkit-line-clamp: 2;\r\n                -webkit-box-orient: vertical;\r\n                overflow: hidden;\r\n                text-overflow: ellipsis;\r\n              }\r\n\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .negotiation_activities_preheating {\r\n    background-image: url('@/assets/img/icon_preheat_bg.png');\r\n    background-size: 100% 100%;\r\n    // height: 750px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    position: relative;\r\n\r\n    .negotiation_activity_preheat {\r\n      margin: 20px 26px 0;\r\n      flex-shrink: 0;\r\n\r\n      .negotiation_activity_preheat_img {\r\n        width: 100%;\r\n        height: auto;\r\n        display: block;\r\n      }\r\n    }\r\n\r\n    .negotiation_topics {\r\n      background: #EDF7FF;\r\n      border-radius: 8px;\r\n      box-shadow: 0px 0px 10px 0px rgba(107, 108, 109, 0.18);\r\n      padding: 14px 12px;\r\n      margin: 20px 26px 15px 26px;\r\n\r\n      .negotiation_topics_title {\r\n        font-size: 16px;\r\n        color: #3368C6;\r\n        margin-bottom: 12px;\r\n        letter-spacing: 1px;\r\n      }\r\n\r\n      .negotiation_topics_content {\r\n        display: grid;\r\n        grid-template-columns: 1.2fr 1fr;\r\n        grid-template-rows: 1fr 1fr;\r\n        gap: 10px;\r\n        margin-bottom: 15px;\r\n\r\n        .negotiation-topic-card {\r\n          background: #fff;\r\n          border-radius: 8px;\r\n          box-shadow: 0 2px 12px 0 rgba(51, 104, 198, 0.06);\r\n          padding: 10px;\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: flex-start;\r\n\r\n          .topic-title {\r\n            font-size: 12px;\r\n            color: #000;\r\n            line-height: 1.4;\r\n          }\r\n\r\n          .topic-image {\r\n            width: 74px;\r\n            height: 62px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            margin: auto;\r\n\r\n            img {\r\n              width: 100%;\r\n              height: 100%;\r\n              object-fit: contain;\r\n            }\r\n          }\r\n\r\n          .sub-topic-btn {\r\n            display: flex;\r\n            align-items: flex-end;\r\n            justify-content: space-between;\r\n            width: 100%;\r\n          }\r\n\r\n          .topic-btn {\r\n            margin: 0 auto;\r\n            background: #3368C6;\r\n            border-radius: 20px;\r\n            padding: 2px 10px;\r\n            font-size: 12px;\r\n            border: none;\r\n            color: #fff;\r\n          }\r\n        }\r\n\r\n        .main {\r\n          grid-row: 1 / span 2;\r\n          grid-column: 1 / 2;\r\n          min-width: 0;\r\n          width: 115px;\r\n          background: #E3EFFF;\r\n\r\n          .topic-title {\r\n            font-size: 12px;\r\n            color: #000;\r\n            line-height: 1.4;\r\n            display: -webkit-box;\r\n            -webkit-line-clamp: 4;\r\n            -webkit-box-orient: vertical;\r\n            overflow: hidden;\r\n          }\r\n\r\n          .topic-image {\r\n            width: 74px;\r\n            height: 62px;\r\n          }\r\n        }\r\n\r\n        .sub {\r\n          grid-column: 2 / 3;\r\n          width: 170px;\r\n          background: #FFFFFF;\r\n          border-radius: 8px;\r\n\r\n          .topic-title {\r\n            font-size: 12px;\r\n            color: #000;\r\n            line-height: 1.4;\r\n            display: -webkit-box;\r\n            -webkit-line-clamp: 2;\r\n            -webkit-box-orient: vertical;\r\n            overflow: hidden;\r\n          }\r\n\r\n          .topic-image {\r\n            width: 26px;\r\n            height: 33px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .negotiation-submit-btn-wrapper {\r\n        display: flex;\r\n        justify-content: center;\r\n        margin-bottom: 0;\r\n\r\n        .negotiation-submit-btn {\r\n          background: linear-gradient(270deg, #4E80D1 0%, #C2E6FF 100%);\r\n          color: #fff;\r\n          font-size: 15px;\r\n          border: none;\r\n          border-radius: 6px;\r\n          padding: 8px 0;\r\n          width: 100%;\r\n          max-width: 480px;\r\n          letter-spacing: 1px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .custom-action-cards {\r\n      display: flex;\r\n      flex-direction: row;\r\n      gap: 16px;\r\n      margin: 0 26px 15px 26px;\r\n\r\n      .action-card {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        background-image: url('@/assets/img/icon_participate_bg.png');\r\n        background-size: 100% 100%;\r\n        padding: 20px 18px 20px 12px;\r\n        position: relative;\r\n      }\r\n\r\n      .action-card-content {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 8px;\r\n        margin-right: 10px;\r\n      }\r\n\r\n      .action-title {\r\n        font-size: 14px;\r\n        color: #3368C6;\r\n        font-weight: 500;\r\n      }\r\n\r\n      .action-arrow {\r\n        width: 4px;\r\n        height: 5px;\r\n      }\r\n\r\n      .action-icon {\r\n        width: 17px;\r\n        height: 17px;\r\n      }\r\n    }\r\n\r\n    .video_graphic_live {\r\n      background: #EDF7FF;\r\n      border-radius: 8px;\r\n      box-shadow: 0px 0px 10px 0px rgba(107, 108, 109, 0.18);\r\n      padding: 10px 12px;\r\n      margin: 0px 26px 30px 26px;\r\n      height: 200px;\r\n\r\n      .video_graphic_live_title {\r\n        font-size: 16px;\r\n        color: #3368C6;\r\n        margin-bottom: 12px;\r\n        letter-spacing: 1px;\r\n      }\r\n\r\n      .video_graphic_live_content {\r\n        position: relative;\r\n        width: 100%;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        .vg-carousel-img-wrapper {\r\n          position: relative;\r\n          width: 100%;\r\n          height: 150px;\r\n          border-radius: 10px;\r\n          overflow: hidden;\r\n          box-shadow: 0 2px 12px 0 rgba(51, 104, 198, 0.08);\r\n          cursor: pointer;\r\n          transition: box-shadow 0.3s;\r\n          background: #fff;\r\n\r\n          .vg-carousel-img {\r\n            width: 100%;\r\n            height: 100%;\r\n            object-fit: cover;\r\n            display: block;\r\n          }\r\n\r\n          .vg-carousel-bottom-bar {\r\n            position: absolute;\r\n            left: 0;\r\n            bottom: 0;\r\n            width: 100%;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            background: rgba(0, 0, 0, 0.45);\r\n            padding: 0 16px 0 16px;\r\n            height: 38px;\r\n            border-bottom-left-radius: 10px;\r\n            border-bottom-right-radius: 10px;\r\n          }\r\n\r\n          .vg-carousel-title {\r\n            color: #fff;\r\n            font-size: 13px;\r\n            white-space: nowrap;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            max-width: 80%;\r\n          }\r\n\r\n          .vg-carousel-dots {\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 8px;\r\n            margin: 0 0 0 16px;\r\n\r\n            .vg-dot {\r\n              width: 4px;\r\n              height: 4px;\r\n              background: #C2E6FF;\r\n              border-radius: 2px;\r\n            }\r\n\r\n            .vg-dot.active {\r\n              width: 13px;\r\n              height: 3px;\r\n              background: #3368C6;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .committee_living_room {\r\n    margin: 4px 8px 8px;\r\n\r\n    .committee_living_room_img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n\r\n  .negotiation_topics_collect {\r\n    background-image: url('@/assets/img/negotiation_topics_collect_bg.png');\r\n    background-size: 100% 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    position: relative;\r\n    min-height: 344px;\r\n\r\n    .negotiation_topics_collect_box {\r\n      background: #EDF7FF;\r\n      border-radius: 8px;\r\n      box-shadow: 0px 0px 10px 0px rgba(107, 108, 109, 0.18);\r\n      padding: 10px 12px;\r\n      margin: 26px 26px 30px 26px;\r\n      min-height: 270px;\r\n\r\n      .topics_collect_title {\r\n        font-size: 16px;\r\n        color: #3368C6;\r\n        text-align: center;\r\n        font-weight: bold;\r\n      }\r\n\r\n      .topics_collect_content {\r\n        font-size: 15px;\r\n        color: #333;\r\n        line-height: 24px;\r\n        margin-top: 20px;\r\n        display: -webkit-box;\r\n        -webkit-line-clamp: 8;\r\n        -webkit-box-orient: vertical;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n      }\r\n\r\n      .topics_collect_organize {\r\n        font-size: 13px;\r\n        color: #999999;\r\n        margin-top: 20px;\r\n      }\r\n\r\n      .topics_collect_flex {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        font-size: 13px;\r\n        margin-top: 10px;\r\n\r\n        .topics_collect_flex_text1 {\r\n          font-size: 13px;\r\n          color: #DA1718;\r\n        }\r\n\r\n        .topics_collect_flex_text2 {\r\n          font-size: 13px;\r\n          color: #333333;\r\n        }\r\n      }\r\n\r\n      .negotiation-submit-btn-wrapper {\r\n        display: flex;\r\n        justify-content: center;\r\n        margin-top: 16px;\r\n\r\n        .negotiation-submit-btn {\r\n          background: linear-gradient(270deg, #4E80D1 0%, #C2E6FF 100%);\r\n          color: #fff;\r\n          font-size: 15px;\r\n          border: none;\r\n          border-radius: 6px;\r\n          padding: 8px 0;\r\n          width: 100%;\r\n          max-width: 480px;\r\n          letter-spacing: 1px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .overview_history_card {\r\n    display: flex;\r\n    flex-direction: row;\r\n    gap: 16px;\r\n    margin: 10px 10px;\r\n\r\n    .action_card {\r\n      // display: flex;\r\n      // flex-direction: column;\r\n      // align-items: flex-start;\r\n      // justify-content: space-between;\r\n      padding: 20px 18px 20px 12px;\r\n      position: relative;\r\n      height: 94px;\r\n      width: 50%;\r\n\r\n      .overview_history_card_content {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n        gap: 8px;\r\n        margin-right: 10px;\r\n\r\n        .action_title {\r\n          font-size: 17px;\r\n          font-weight: bold;\r\n          color: #3368C6;\r\n        }\r\n\r\n        .action_info {\r\n          background: #3368C6;\r\n          border-radius: 20px;\r\n          font-size: 12px;\r\n          color: #FFFFFF;\r\n          line-height: 18px;\r\n          padding: 1px 8px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .overview {\r\n      background-image: url('@/assets/img/icon_overview_bg.png');\r\n      background-size: 100% 100%;\r\n    }\r\n\r\n    .history {\r\n      background-image: url('@/assets/img/icon_history_bg.png');\r\n      background-size: 100% 100%;\r\n    }\r\n  }\r\n\r\n  .cultural_historical_collection {\r\n    background-image: url('@/assets/img/icon_cultural_historical_collection_bg.png');\r\n    background-size: 100% 100%;\r\n    height: 240px;\r\n    margin: 0 10px;\r\n\r\n    .cultural_historical_collection_content {\r\n\r\n      .historical_collection_content_flex {\r\n        display: grid;\r\n        grid-template-columns: repeat(2, 1fr);\r\n        gap: 15px;\r\n        padding: 20px 15px 5px 15px;\r\n\r\n        .cultural-history-card {\r\n          background: #EDF7FF;\r\n          border-radius: 12px;\r\n          box-shadow: 0 2px 8px rgba(51, 104, 198, 0.08);\r\n          padding: 10px 12px 6px 12px;\r\n          position: relative;\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: flex-start;\r\n\r\n          .card-title {\r\n            font-size: 14px;\r\n            color: #333;\r\n            font-weight: bold;\r\n            margin-bottom: 16px;\r\n            display: -webkit-box;\r\n            -webkit-line-clamp: 2;\r\n            -webkit-box-orient: vertical;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            // min-height: 40px;\r\n          }\r\n\r\n          .card-btn-icon {\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            width: 100%;\r\n\r\n            .card-btn {\r\n              background: #3368C6;\r\n              color: #fff;\r\n              border: none;\r\n              border-radius: 16px;\r\n              padding: 4px 10px;\r\n              font-size: 13px;\r\n              align-self: flex-start;\r\n            }\r\n\r\n            .card-icon {\r\n              width: 26px;\r\n              height: 26px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .historical_collection_wrapper {\r\n        display: flex;\r\n        justify-content: center;\r\n        margin: 15px 30px;\r\n\r\n        .historical_collection_wrapper_btn {\r\n          background: linear-gradient(270deg, #4E80D1 0%, #C2E6FF 100%);\r\n          color: #fff;\r\n          font-size: 15px;\r\n          border: none;\r\n          border-radius: 6px;\r\n          padding: 8px 0;\r\n          width: 100%;\r\n          max-width: 480px;\r\n          letter-spacing: 1px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .header_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 20px 25px 0;\r\n    z-index: 4;\r\n\r\n    .header_title {\r\n      font-size: 18px;\r\n      font-weight: bold;\r\n      color: #000;\r\n      letter-spacing: 1px;\r\n      display: inline-block;\r\n      position: relative;\r\n    }\r\n\r\n    .header_title::after {\r\n      content: '';\r\n      position: absolute;\r\n      left: 50%;\r\n      transform: translateX(-50%);\r\n      width: 20px;\r\n      height: 3px;\r\n      background: #3368C6;\r\n      border-radius: 2px;\r\n      margin-top: 4px;\r\n      margin-bottom: 2px;\r\n      bottom: -9px;\r\n    }\r\n\r\n    .header_all {\r\n      font-size: 14px;\r\n      color: #999999;\r\n    }\r\n  }\r\n\r\n  .loading {\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    font-size: 14px;\r\n    color: #8d8d8d;\r\n    padding: 42px 15px 20px;\r\n\r\n    .loading_spinner {\r\n      width: 40px;\r\n      height: 40px;\r\n      border: 4px solid #e0e0e0;\r\n      border-top: 4px solid #3368C6;\r\n      border-radius: 50%;\r\n      animation: spin 1s linear infinite;\r\n      display: inline-block;\r\n    }\r\n  }\r\n\r\n  @keyframes spin {\r\n    to {\r\n      transform: rotate(360deg);\r\n    }\r\n  }\r\n}\r\n\r\n// 全局loading遮罩样式\r\n.global-loading-mask {\r\n  position: fixed;\r\n  z-index: 9999;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(255, 255, 255, 0.85);\r\n}\r\n\r\n.global-loading-spinner {\r\n  width: 48px;\r\n  height: 48px;\r\n  border: 5px solid #e0e0e0;\r\n  border-top: 5px solid #3368C6;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.global-loading-text {\r\n  font-size: 18px;\r\n  color: #3368C6;\r\n}\r\n\r\n.van-swipe__indicators {\r\n  bottom: 10px !important;\r\n}\r\n\r\n.van-swipe__indicator {\r\n  width: 18px;\r\n  height: 6px;\r\n  border-radius: 3px;\r\n  background: #D8E0F3 !important;\r\n}\r\n\r\n.van-swipe__indicator--active {\r\n  background: #3368C6 !important;\r\n}\r\n</style>\r\n"], "mappings": ";OAUWA,UAAkC;OAEhCC,UAAoC;OAGpCC,UAAoC;OA+FXC,UAAiC;OAEpCC,UAAmC;OAOnCC,UAAkC;OAoC1DC,UAAiD;;EA1JrDC,KAAK,EAAC;AAAY;;;EAGnBA,KAAK,EAAC;;;EAeHA,KAAK,EAAC;AAAS;;EACbA,KAAK,EAAC;AAAY;;;EAKhBA,KAAK,EAAC;;;;EAKNA,KAAK,EAAC;;;EAKAA,KAAK,EAAC;AAAwB;;;EAE5BA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAc;;EAEtBA,KAAK,EAAC;AAAe;;;;EAU7BA,KAAK,EAAC;;;EAMVA,KAAK,EAAC;AAAmC;;;EAMvCA,KAAK,EAAC;;;;EAKNA,KAAK,EAAC;AAAoB;;;EAGtBA,KAAK,EAAC;;;;EAKNA,KAAK,EAAC;;;EAEFA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAa;;;;EAQnBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAe;;EAEnBA,KAAK,EAAC,mBAAmB;EAACC,KAAiB,EAAjB;IAAA;EAAA;;;;;EAQhCD,KAAK,EAAC;;;;EA0BVA,KAAK,EAAC;;;;EAGFA,KAAK,EAAC;;;;EAKNA,KAAK,EAAC;;;;EAKFA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAAmB;;EACzBA,KAAK,EAAC;AAAkB;;;;EAS9BA,KAAK,EAAC;;;EAOZA,KAAK,EAAC;AAAuB;;;EAK7BA,KAAK,EAAC;;;EAKJA,KAAK,EAAC;AAAgC;;;EAElCA,KAAK,EAAC;;;EAKNA,KAAK,EAAC;AAAsB;;EAC5BA,KAAK,EAAC;AAAwB;;EAG9BA,KAAK,EAAC;AAAyB;;EAC/BA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAA2B;;;;;EAG5BA,KAAK,EAAC;AAAM;;;;;EAMjBA,KAAK,EAAC;AAA2B;;;EAOnCA,KAAK,EAAC;;;;EAsBZA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAY;;EAIlBA,KAAK,EAAC;AAAwC;;EAC5CA,KAAK,EAAC;AAAoC;;;EAGtCA,KAAK,EAAC;AAAY;;EAClBA,KAAK,EAAC;AAAe;;;EAY7BA,KAAK,EAAC;AAAoB;;;;uBA5OjCE,mBAAA,CAgPM,OAhPNC,UAgPM,GA/OJC,mBAAA,iBAAoB,EACTC,MAAA,CAAAC,WAAW,I,cAAtBJ,mBAAA,CAIM,OAJNK,UAIM,EAAAC,MAAA,SAAAA,MAAA,QAFJC,mBAAA,CAA0C;IAArCT,KAAK,EAAC;EAAwB,2BACnCS,mBAAA,CAA6C;IAAxCT,KAAK,EAAC;EAAqB,GAAC,QAAM,mB,yCAEzCI,mBAAA,eAAkB,E,8VAUlBA,mBAAA,UAAa,EACbK,mBAAA,CAmCM,OAnCNC,UAmCM,GAlCJD,mBAAA,CAGM,OAHNE,UAGM,G,4BAFJF,mBAAA,CAAsC;IAAhCT,KAAK,EAAC;EAAc,GAAC,MAAI,qBAC/BS,mBAAA,CAA8D;IAAxDT,KAAK,EAAC,YAAY;IAAEY,OAAK,EAAAJ,MAAA,QAAAA,MAAA,gBAAAK,MAAA;MAAA,OAAER,MAAA,CAAAS,WAAW;IAAA;KAAI,MAAO,E,GAEzCT,MAAA,CAAAU,OAAO,I,cACrBb,mBAAA,CAEM,OAFNc,UAEM,EAAAR,MAAA,SAAAA,MAAA,QADJC,mBAAA,CAAqC;IAA/BT,KAAK,EAAC;EAAiB,0B,MAGZK,MAAA,CAAAY,YAAY,CAACC,MAAM,Q,cACtChB,mBAAA,CAiBM,OAjBNiB,UAiBM,GAhBJC,YAAA,CAeYC,oBAAA;IAfDrB,KAAK,EAAC,kBAAkB;IAAEsB,QAAQ,EAAE,IAAI;IAAE,iBAAe,EAAC,MAAM;IAAE,iBAAe,EAAE,IAAI;IAC/FC,IAAI,EAAE;;sBACS;MAAA,OAAqC,E,kBAArDrB,mBAAA,CAYiBsB,SAAA,QAAAC,WAAA,CAZuBpB,MAAA,CAAAY,YAAY,YAA5BS,IAAI,EAAEC,KAAK;6BAAnCC,YAAA,CAYiBC,yBAAA;UAZsCC,GAAG,EAAEH,KAAK;UAAE3B,KAAK,EAAC,aAAa;UACnFY,OAAK,WAALA,OAAKA,CAAAC,MAAA;YAAA,OAAER,MAAA,CAAA0B,eAAe,CAACL,IAAI;UAAA;;4BAC5B;YAAA,OASM,CATNjB,mBAAA,CASM,OATNuB,UASM,GARJvB,mBAAA,CAA6F;cAAvFwB,GAAG,EAAE5B,MAAA,CAAA6B,MAAM,CAACC,OAAO,eAAeT,IAAI,CAACU,OAAO;cAAGC,GAAG,EAAEX,IAAI,CAACY,KAAK;cAAEtC,KAAK,EAAC;iDAC9ES,mBAAA,CAEM,OAFN8B,UAEM,GADJ9B,mBAAA,CAAoD,OAApD+B,WAAoD,EAAAC,gBAAA,CAAvBf,IAAI,CAACgB,SAAS,iB,GAE7CjC,mBAAA,CAGM,OAHNkC,WAGM,I,kBAFJzC,mBAAA,CACmFsB,SAAA,QAAAC,WAAA,CADvDpB,MAAA,CAAAY,YAAY,YAA1BS,IAAI,EAAEkB,GAAG;mCAAvB1C,mBAAA,CACmF;gBADxC4B,GAAG,EAAEc,GAAG;gBAAE5C,KAAK,EAAA6C,eAAA,EAAC,KAAK;kBAAAC,MAAA,EAC5CF,GAAG,KAAKvC,MAAA,CAAA0C;gBAAa;gBAAKnC,OAAK,WAALA,OAAKA,CAAAC,MAAA;kBAAA,OAAEmC,IAAA,CAAAC,gBAAgB,CAACL,GAAG;gBAAA;;;;;;;;;yBAQnF1C,mBAAA,CAEM,OAFNgD,WAEM,EAAA1C,MAAA,SAAAA,MAAA,QADJC,mBAAA,CAAiB,cAAX,MAAI,mB,OAIhBL,mBAAA,YAAe,EACfK,mBAAA,CAgGM,OAhGN0C,WAgGM,GA/FJ/C,mBAAA,cAAiB,E,4BACjBK,mBAAA,CAEM;IAFDT,KAAK,EAAC;EAAY,IACrBS,mBAAA,CAAwC;IAAlCT,KAAK,EAAC;EAAc,GAAC,QAAM,E,qBAEnCI,mBAAA,cAAiB,EAC+BC,MAAA,CAAA+C,SAAS,W,cAAzDlD,mBAAA,CAGM,OAHNmD,WAGM,GAFJ5C,mBAAA,CAC8B;IADxBwB,GAAG,EAAE5B,MAAA,CAAA6B,MAAM,CAACC,OAAO;IAAqCE,GAAG,EAAC,EAAE;IAACrC,KAAK,EAAC,kCAAkC;IAC1GY,OAAK,EAAAJ,MAAA,QAAAA,MAAA,gBAAAK,MAAA;MAAA,OAAER,MAAA,CAAAiD,YAAY;IAAA;+EAExBlD,mBAAA,YAAe,EACfK,mBAAA,CAqCM,OArCN8C,WAqCM,G,4BApCJ9C,mBAAA,CAAgD;IAA3CT,KAAK,EAAC;EAA0B,GAAC,MAAI,qBAC1BK,MAAA,CAAAmD,wBAAwB,I,cACtCtD,mBAAA,CAEM,OAFNuD,WAEM,EAAAjD,MAAA,SAAAA,MAAA,QADJC,mBAAA,CAAqC;IAA/BT,KAAK,EAAC;EAAiB,0B,MAGZK,MAAA,CAAAqD,iBAAiB,CAACxC,MAAM,Q,cAC3ChB,mBAAA,CAmBM,OAnBNyD,WAmBM,GAlBJlD,mBAAA,CAOM;IAPDT,KAAK,EAAC,6BAA6B;IAAEY,OAAK,EAAAJ,MAAA,QAAAA,MAAA,gBAAAK,MAAA;MAAA,OAAER,MAAA,CAAAiD,YAAY,CAACjD,MAAA,CAAAqD,iBAAiB;IAAA;MAC7EjD,mBAAA,CAA+D,OAA/DmD,WAA+D,EAAAnB,gBAAA,CAAnCpC,MAAA,CAAAqD,iBAAiB,IAAIpB,KAAK,kBACtD7B,mBAAA,CAGM,OAHNoD,WAGM,GAFOxD,MAAA,CAAAqD,iBAAiB,IAAII,QAAQ,I,cAAxC5D,mBAAA,CACiF;;IAA9E+B,GAAG,EAAE5B,MAAA,CAAA6B,MAAM,CAACC,OAAO,eAAe9B,MAAA,CAAAqD,iBAAiB,IAAII,QAAQ;IAAEzB,GAAG,EAAC;2GAE1E5B,mBAAA,CAAsC;IAA9BT,KAAK,EAAC;EAAW,GAAC,KAAG,oB,sBAE/BE,mBAAA,CASMsB,SAAA,QAAAC,WAAA,CATiDpB,MAAA,CAAAqD,iBAAiB,CAACK,KAAK,eAA/BrC,IAAI;yBAAnDxB,mBAAA,CASM;MATDF,KAAK,EAAC,4BAA4B;MAA6C8B,GAAG,EAAEJ,IAAI,CAACsC,EAAE;MAC7FpD,OAAK,WAALA,OAAKA,CAAAC,MAAA;QAAA,OAAER,MAAA,CAAAiD,YAAY,CAAC5B,IAAI;MAAA;QACzBjB,mBAAA,CAA+C,OAA/CwD,WAA+C,EAAAxB,gBAAA,CAAnBf,IAAI,CAACY,KAAK,kBACtC7B,mBAAA,CAKM,OALNyD,WAKM,G,4BAJJzD,mBAAA,CAAwD;MAAhDT,KAAK,EAAC,WAAW;MAACC,KAAiB,EAAjB;QAAA;MAAA;OAAkB,KAAG,qBAC/CQ,mBAAA,CAEM,OAFN0D,WAEM,GADOzC,IAAI,CAACoC,QAAQ,I,cAAxB5D,mBAAA,CAAyF;;MAA9D+B,GAAG,EAAE5B,MAAA,CAAA6B,MAAM,CAACC,OAAO,eAAeT,IAAI,CAACoC,QAAQ;MAAEzB,GAAG,EAAC;;qDAOxFnC,mBAAA,CAEM,OAFNkE,WAEM,EAAA5D,MAAA,SAAAA,MAAA,QADJC,mBAAA,CAAiB,cAAX,MAAI,mB,KAGdA,mBAAA,CAEM;IAFDT,KAAK,EAAC,gCAAgC;IAAEY,OAAK,EAAAJ,MAAA,QAAAA,MAAA,gBAAAK,MAAA;MAAA,OAAER,MAAA,CAAAiD,YAAY;IAAA;kCAC9D7C,mBAAA,CAAsD;IAA9CT,KAAK,EAAC;EAAwB,GAAC,QAAM,mB,MAGjDI,mBAAA,mBAAsB,EACtBK,mBAAA,CAeM;IAfDT,KAAK,EAAC;EAAqB,IAC9BS,mBAAA,CAMM;IANDT,KAAK,EAAC,qBAAqB;IAAEY,OAAK,EAAEP,MAAA,CAAAgE;kCACvC5D,mBAAA,CAGM;IAHDT,KAAK,EAAC;EAAqB,IAC9BS,mBAAA,CAAwC;IAAlCT,KAAK,EAAC;EAAc,GAAC,QAAM,GACjCS,mBAAA,CAAsE;IAAjET,KAAK,EAAC,cAAc;IAACiC,GAAiC,EAAjCrC,UAAiC;IAACyC,GAAG,EAAC;yBAElE5B,mBAAA,CAA0E;IAArET,KAAK,EAAC,aAAa;IAACiC,GAAmC,EAAnCpC,UAAmC;IAACwC,GAAG,EAAC;gCAEnE5B,mBAAA,CAMM;IANDT,KAAK,EAAC,oBAAoB;IAAEY,OAAK,EAAEP,MAAA,CAAAiE;kCACtC7D,mBAAA,CAGM;IAHDT,KAAK,EAAC;EAAqB,IAC9BS,mBAAA,CAAwC;IAAlCT,KAAK,EAAC;EAAc,GAAC,QAAM,GACjCS,mBAAA,CAAsE;IAAjET,KAAK,EAAC,cAAc;IAACiC,GAAiC,EAPjCrC,UAAiC;IAOCyC,GAAG,EAAC;yBAElE5B,mBAAA,CAAyE;IAApET,KAAK,EAAC,aAAa;IAACiC,GAAkC,EAAlCnC,UAAkC;IAACuC,GAAG,EAAC;kCAGpEjC,mBAAA,aAAgB,EACsB,KAAK,I,cAA3CF,mBAAA,CA4BM,OA5BNqE,WA4BM,G,4BA3BJ9D,mBAAA,CAAmD;IAA9CT,KAAK,EAAC;EAA0B,GAAC,SAAO,qBAC7BK,MAAA,CAAAmE,uBAAuB,I,cACrCtE,mBAAA,CAEM,OAFNuE,WAEM,EAAAjE,MAAA,SAAAA,MAAA,QADJC,mBAAA,CAAqC;IAA/BT,KAAK,EAAC;EAAiB,0B,MAGZK,MAAA,CAAAqE,gBAAgB,CAACxD,MAAM,Q,cAC1ChB,mBAAA,CAaM,OAbNyE,WAaM,GAZJlE,mBAAA,CAWM;IAXDT,KAAK,EAAC,yBAAyB;IAAE4E,YAAU,EAAEvE,MAAA,CAAAwE,cAAc;IAAGC,WAAS,EAAEzE,MAAA,CAAA0E,aAAa;IACxFC,UAAQ,EAAE3E,MAAA,CAAA4E,YAAY;IAAGrE,OAAK,EAAAJ,MAAA,QAAAA,MAAA,gBAAAK,MAAA;MAAA,OAAER,MAAA,CAAA6E,iBAAiB,CAAC7E,MAAA,CAAAqE,gBAAgB,CAACrE,MAAA,CAAA8E,OAAO;IAAA;MAC3E1E,mBAAA,CAC2C;IADtCT,KAAK,EAAC,iBAAiB;IAAEiC,GAAG,EAAE5B,MAAA,CAAAqE,gBAAgB,CAACrE,MAAA,CAAA8E,OAAO,EAAEC,GAAG;IAC7D/C,GAAG,EAAEhC,MAAA,CAAAqE,gBAAgB,CAACrE,MAAA,CAAA8E,OAAO,EAAE7C;wCAClC7B,mBAAA,CAMM,OANN4E,WAMM,GALJ5E,mBAAA,CAA0E,OAA1E6E,WAA0E,EAAA7C,gBAAA,CAAxCpC,MAAA,CAAAqE,gBAAgB,CAACrE,MAAA,CAAA8E,OAAO,EAAE7C,KAAK,kBACjE7B,mBAAA,CAGM,OAHN8E,WAGM,I,kBAFJrF,mBAAA,CACgFsB,SAAA,QAAAC,WAAA,CADpDpB,MAAA,CAAAqE,gBAAgB,YAA9BhD,IAAI,EAAEkB,GAAG;yBAAvB1C,mBAAA,CACgF;MADjC4B,GAAG,EAAEc,GAAG;MAAE5C,KAAK,EAAA6C,eAAA,EAAC,QAAQ;QAAAC,MAAA,EACnDF,GAAG,KAAKvC,MAAA,CAAA8E;MAAO;MAAKvE,OAAK,EAAA4E,cAAA,WAAA3E,MAAA;QAAA,OAAOR,MAAA,CAAAoF,cAAc,CAAC7C,GAAG;MAAA;;oFAO9E1C,mBAAA,CAEM,OAFNwF,WAEM,EAAAlF,MAAA,SAAAA,MAAA,QADJC,mBAAA,CAAiB,cAAX,MAAI,mB,8CAKlBL,mBAAA,WAAc,EACdK,mBAAA,CAGM,OAHNkF,WAGM,GAFJlF,mBAAA,CACqC;IADhCwB,GAAiD,EAAjDlC,UAAiD;IAACsC,GAAG,EAAC,EAAE;IAACrC,KAAK,EAAC,2BAA2B;IAC5FY,OAAK,EAAAJ,MAAA,QAAAA,MAAA,gBAAAK,MAAA;MAAA,OAAER,MAAA,CAAAuF,uBAAuB;IAAA;QAEnCxF,mBAAA,YAAe,EAEPC,MAAA,CAAAwF,4BAA4B,IAAIxF,MAAA,CAAAwF,4BAA4B,CAAC3E,MAAM,Q,cAD3EhB,mBAAA,CAuCM,OAvCN4F,WAuCM,G,4BArCJrF,mBAAA,CAEM;IAFDT,KAAK,EAAC;EAAY,IACrBS,mBAAA,CAAwC;IAAlCT,KAAK,EAAC;EAAc,GAAC,QAAM,E,qBAEnCS,mBAAA,CAiCM,OAjCNsF,WAiCM,GAhCY1F,MAAA,CAAA2F,+BAA+B,I,cAC7C9F,mBAAA,CAEM,OAFN+F,WAEM,EAAAzF,MAAA,SAAAA,MAAA,QADJC,mBAAA,CAAqC;IAA/BT,KAAK,EAAC;EAAiB,0B,MAGZK,MAAA,CAAAwF,4BAA4B,CAAC3E,MAAM,Q,cAAxDhB,mBAAA,CAqBWsB,SAAA;IAAAM,GAAA;EAAA,IApBTrB,mBAAA,CAAmF,OAAnFyF,WAAmF,EAAAzD,gBAAA,CAA9CpC,MAAA,CAAAwF,4BAA4B,IAAIvD,KAAK,kBAC1E7B,mBAAA,CAEM,OAFN0F,WAEM,EAAA1D,gBAAA,CADDpC,MAAA,CAAAwF,4BAA4B,IAAIO,OAAO,kBAE5C3F,mBAAA,CAAqG,OAArG4F,WAAqG,EAAhE,OAAK,GAAA5D,gBAAA,CAAGpC,MAAA,CAAAwF,4BAA4B,IAAIS,eAAe,kBAC5F7F,mBAAA,CAWM,OAXN8F,WAWM,GAVJ9F,mBAAA,CAQM,OARN+F,WAQM,GAPQnG,MAAA,CAAAwF,4BAA4B,IAAIY,UAAU,gB,cAAtDvG,mBAAA,CAGO,QAAAwG,WAAA,G,kCAFFrG,MAAA,CAAAwF,4BAA4B,IAAIY,UAAU,IAAG,GAChD,iBAAAhG,mBAAA,CAAqE,QAArEkG,WAAqE,EAAAlE,gBAAA,CAA/CpC,MAAA,CAAAwF,4BAA4B,IAAIe,IAAI,IAAG,GAAC,gB,oBAEhE1G,mBAAA,CAEO,QAAA2G,WAAA,EAAApE,gBAAA,CADFpC,MAAA,CAAAwF,4BAA4B,IAAIY,UAAU,kB,GAGjDhG,mBAAA,CAAoG,OAApGqG,WAAoG,EAA7D,IAAE,GAAArE,gBAAA,CAAGpC,MAAA,CAAAwF,4BAA4B,IAAIkB,YAAY,IAAG,KAAG,gB,GAEhGtG,mBAAA,CAEM;IAFDT,KAAK,EAAC,gCAAgC;IAAEY,OAAK,EAAAJ,MAAA,QAAAA,MAAA,gBAAAK,MAAA;MAAA,OAAER,MAAA,CAAA2G,eAAe,CAAC3G,MAAA,CAAAwF,4BAA4B;IAAA;kCAC9FpF,mBAAA,CAAoD;IAA5CT,KAAK,EAAC;EAAwB,GAAC,MAAI,mB,iDAI7CE,mBAAA,CAEM,OAFN+G,WAEM,EAAAzG,MAAA,SAAAA,MAAA,QADJC,mBAAA,CAAiB,cAAX,MAAI,mB,8CAKlBL,mBAAA,mBAAsB,EACtBK,mBAAA,CAaM;IAbDT,KAAK,EAAC;EAAuB,IAChCS,mBAAA,CAKM;IALDT,KAAK,EAAC,sBAAsB;IAAEY,OAAK,EAAEP,MAAA,CAAA6G;kCACxCzG,mBAAA,CAGM;IAHDT,KAAK,EAAC;EAA+B,IACxCS,mBAAA,CAAoC;IAA/BT,KAAK,EAAC;EAAc,GAAC,MAAI,GAC9BS,mBAAA,CAAmC;IAA9BT,KAAK,EAAC;EAAa,GAAC,MAAI,E,uBAGjCS,mBAAA,CAKM;IALDT,KAAK,EAAC,qBAAqB;IAAEY,OAAK,EAAEP,MAAA,CAAA8G;kCACvC1G,mBAAA,CAGM;IAHDT,KAAK,EAAC;EAA+B,IACxCS,mBAAA,CAAoC;IAA/BT,KAAK,EAAC;EAAc,GAAC,MAAI,GAC9BS,mBAAA,CAAmC;IAA9BT,KAAK,EAAC;EAAa,GAAC,MAAI,E,yBAInCI,mBAAA,YAAe,EACmCC,MAAA,CAAA+G,mBAAmB,IAAI/G,MAAA,CAAA+G,mBAAmB,CAAClG,MAAM,Q,cAAnGhB,mBAAA,CAoBM,OApBNmH,WAoBM,GAnBJ5G,mBAAA,CAGM,OAHN6G,WAGM,G,4BAFJ7G,mBAAA,CAAwC;IAAlCT,KAAK,EAAC;EAAc,GAAC,QAAM,qBACjCS,mBAAA,CAA2E;IAArET,KAAK,EAAC,YAAY;IAAEY,OAAK,EAAAJ,MAAA,QAAAA,MAAA,gBAAAK,MAAA;MAAA,OAAER,MAAA,CAAAkH,wBAAwB;IAAA;KAAI,MAAO,E,GAEtE9G,mBAAA,CAcM,OAdN+G,WAcM,GAbJ/G,mBAAA,CASM,OATNgH,WASM,I,kBARJvH,mBAAA,CAOMsB,SAAA,QAAAC,WAAA,CAP4CpB,MAAA,CAAA+G,mBAAmB,YAA3B1F,IAAI;yBAA9CxB,mBAAA,CAOM;MAPDF,KAAK,EAAC,uBAAuB;MAAsC8B,GAAG,EAAEJ,IAAI,CAACsC,EAAE;MACjFpD,OAAK,WAALA,OAAKA,CAAAC,MAAA;QAAA,OAAER,MAAA,CAAAqH,kBAAkB,CAAChG,IAAI;MAAA;QAC/BjB,mBAAA,CAA8C,OAA9CkH,WAA8C,EAAAlF,gBAAA,CAAnBf,IAAI,CAACkG,KAAK,kBACrCnH,mBAAA,CAGM,OAHNoH,WAGM,G,4BAFJpH,mBAAA,CAAqC;MAA7BT,KAAK,EAAC;IAAU,GAAC,KAAG,qBAC5BS,mBAAA,CAAqD;MAAhDT,KAAK,EAAC,WAAW;MAAEiC,GAAG,EAAEP,IAAI,CAACoG,IAAI;MAAEzF,GAAG,EAAC;;oCAIlD5B,mBAAA,CAEM;IAFDT,KAAK,EAAC,+BAA+B;IAAEY,OAAK,EAAAJ,MAAA,QAAAA,MAAA,gBAAAK,MAAA;MAAA,OAAER,MAAA,CAAA0H,oBAAoB;IAAA;kCACrEtH,mBAAA,CAAmE;IAA3DT,KAAK,EAAC;EAAmC,GAAC,UAAQ,mB,6CAIhEI,mBAAA,SAAY,EACZK,mBAAA,CAGM,OAHNuH,WAGM,GAFJvH,mBAAA,CACmC;IAD9BwB,GAAiD,EAnFjDlC,UAAiD;IAmFCsC,GAAG,EAAC,EAAE;IAACrC,KAAK,EAAC,wBAAwB;IACzFY,OAAK,EAAAJ,MAAA,QAAAA,MAAA,gBAAAK,MAAA;MAAA,OAAEmC,IAAA,CAAAiF,qBAAqB;IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}