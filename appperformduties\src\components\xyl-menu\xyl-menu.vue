<template>
  <el-scrollbar class="xyl-menu-scrollbar">
    <el-menu class="xyl-menu" :collapse="isCollapse" :default-active="menuId" @select="handleSelect">
      <!-- <xyl-menu-item :menuData="menuData"></xyl-menu-item> -->
      <template v-for="item in menuData">
        <el-sub-menu :index="item.id" :key="item.id" popper-class="xyl-menu-popper" v-if="item.children.length">
          <template #title>
            <el-icon>
              <location />
            </el-icon>
            <span class="xyl-menu-text">{{ item.title }}</span>
          </template>
          <xyl-menu-item :menuData="item.children"></xyl-menu-item>
        </el-sub-menu>
        <el-menu-item :index="item.id" :key="item.id" v-if="!item.children.length">
          <el-icon>
            <location />
          </el-icon>
          <template #title>
            <span class="xyl-menu-text">{{ item.title }}</span>
          </template>
        </el-menu-item>
      </template>
    </el-menu>
  </el-scrollbar>
</template>
<script>
export default { name: 'XylMenu' }
</script>
<script setup>
import { computed } from 'vue'
import XylMenuItem from './xyl-menu-item.vue'
const props = defineProps({
  modelValue: [String, Number],
  menuData: { type: Array, default: () => [] },
  collapse: { type: Boolean, default: false }
})
const emit = defineEmits(['update:modelValue', 'select'])

const menuId = computed({
  get () {
    return props.modelValue
  },
  set (val) {
    emit('update:modelValue', val)
  }
})
const menuData = computed(() => props.menuData)
const isCollapse = computed(() => props.collapse)

const handleSelect = (key) => {
  menuId.value = key
  selectData(menuData.value, key)
}

const selectData = (data, id) => {
  data.forEach(item => {
    if (item.children.length === 0) {
      if (item.id === id) { emit('select', item) }
    } else {
      selectData(item.children, id)
    }
  })
}
</script>
<style lang="scss">
.xyl-menu-scrollbar {
  width: 100%;
  height: 100%;
  background: #46505a;

  .xyl-menu:not(.zy-el-menu--collapse) {
    width: 220px;
    border: none;
  }

  .zy-el-menu {
    --zy-el-menu-active-color: #fff;
    --zy-el-menu-bg-color: #46505a;
    --zy-el-menu-hover-text-color: #fff;
    --zy-el-menu-hover-bg-color: #313c47;
    --zy-el-menu-text-color: #bbb;
    --zy-el-menu-border-color: #46505a;
  }

  .zy-el-menu-item {
    min-width: 200px;
    height: auto;
    min-height: var(--zy-el-menu-sub-item-height);
    line-height: 1;

    &:hover {
      color: #fff;
    }

    &.is-active {
      background: var(--zy-el-color-primary);

      .xyl-menu-text {
        color: #fff;
      }
    }
  }

  .zy-el-sub-menu__title {
    min-width: 200px;
    height: auto;
    min-height: var(--zy-el-menu-sub-item-height);
    line-height: 1;
    padding-right: 40px;

    &:hover {
      color: #fff;
    }
  }

  .xyl-menu-text {
    display: inline-block;
    width: 100%;
    font-size: var(--zy-text-font-size);
    line-height: var(--zy-line-height);
    padding: var(--zy-distance-four) 0;
    text-overflow: clip;
    white-space: normal;
  }

  .zy-el-menu--collapse {
    .zy-el-menu-item {
      min-width: auto !important;
      height: var(--zy-el-menu-sub-item-height);
      min-height: auto;
      line-height: var(--zy-el-menu-sub-item-height);
    }

    .zy-el-sub-menu__title {
      min-width: auto !important;
      height: var(--zy-el-menu-sub-item-height);
      min-height: auto;
      line-height: var(--zy-el-menu-sub-item-height);
      padding-right: var(--zy-el-menu-base-level-padding);
    }

    .xyl-menu-text {
      text-overflow: initial;
      white-space: nowrap;
    }
  }
}
</style>
