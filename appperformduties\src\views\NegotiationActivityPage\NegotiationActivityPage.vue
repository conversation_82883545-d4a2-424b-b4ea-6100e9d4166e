<template>
  <div class="NegotiationActivityPage">
    <!-- 顶部背景 -->
    <div>
      <img src="@/assets/img/icon_negotiation_activity.png" alt="" style="width: 100%;height: 100%;">
    </div>
    <!-- 往期、下期 -->
    <div class="card-container">
      <div class="card" @click="openNegotiation(1)">
        <div class="card_left">
          <div class="card-title">往期协商</div>
          <div class="card-footer">
            <span class="card-link">点击查看</span>
            <span class="card-arrow">&gt;</span>
          </div>
        </div>
        <img class="card-icon" src="@/assets/img/icon_previous_period.png" alt="往期协商">
      </div>
      <div class="card" @click="openNegotiation(2)">
        <div class="card_left">
          <div class="card-title">下期协商</div>
          <div class="card-footer">
            <span class="card-link">点击查看</span>
            <span class="card-arrow">&gt;</span>
          </div>
        </div>
        <img class="card-icon" src="@/assets/img/icon_next_issue.png" alt="下期协商">
      </div>
    </div>
    <!-- 本期 -->
    <div class="current_period_box" v-if="negotiationTopics.id">
      <div class="current_period_title_box">
        <img class="current_period_title_line" src="@/assets/img/icon_current_period_left_line.png" alt="left" />
        <span class="current_period_title">本期协商</span>
        <img class="current_period_title_line" src="@/assets/img/icon_current_period_right_line.png" alt="right" />
      </div>
      <div class="current_period_content_box">
        <div class="current_period_content_title">
          {{ negotiationTopics.title }}
        </div>
        <div class="current_period_content_text" v-html="negotiationTopics.content"></div>
      </div>
    </div>
    <!-- 参与各方 -->
    <div class="participants-section">
      <div class="header_title">
        <span class="header_line"></span>
        <span>参与各方</span>
      </div>
      <div class="participants-cards">
        <div class="participant-card" @click="openCompanyList('join')">
          <span class="card-text">协商部门</span>
          <img class="card-icon" src="@/assets/img/icon_department.png" alt="协商部门" />
        </div>
        <div class="participant-card" @click="openCompanyList('hold')">
          <span class="card-text">牵头单位</span>
          <img class="card-icon" src="@/assets/img/icon_lead_unit.png" alt="牵头单位" />
        </div>
      </div>
    </div>
    <!-- 视频+图文直播 -->
    <div class="video_graphic_live" v-if="false">
      <div class="header_title">
        <span class="header_line"></span>
        <span>视频+图文直播</span>
      </div>
      <div class="live-swiper-box">
        <van-swipe class="live-swiper" :autoplay="300000" @change="onSwipeChange">
          <van-swipe-item v-for="(item) in liveList" :key="item.id">
            <img :src="item.img" class="live-img" />
            <div class="live-title-bar">
              <div class="live-title">{{ item.title }}</div>
            </div>
          </van-swipe-item>
        </van-swipe>
      </div>
    </div>
    <!-- 参加问卷调查 -->
    <div class="participate_questionnaire">
      <div class="header_title">
        <span class="header_line"></span>
        <span>参加问卷调查</span>
      </div>
      <div class="participate_questionnaire_content" @click="openQuestionnaire()">
        <img src="@/assets/img/icon_questionnaire.png" alt="" class="participate_questionnaire_img">
        <div class="enter">点击进入</div>
      </div>
    </div>
    <!-- 网络议政 -->
    <div class="network_discuss" v-if="networkDiscuss.id">
      <div class="header_title">
        <span class="header_line"></span>
        <span>网络议政</span>
      </div>
      <div class="network_discuss_content">
        <div class="network-discuss-card">
          <div class="network-discuss-header">
            <span class="tag tag-blue">{{ networkDiscuss.collectType?.name }}</span>
            <span class="tag tag-red">征集{{ networkDiscuss.collectStatus?.name }}</span>
          </div>
          <div class="network-discuss-body" v-html="networkDiscuss.content"></div>
          <div class="network-discuss-comment-title">全部意见</div>
          <div class="ndd-comment-list">
            <template v-if="commentList && commentList.length > 0">
              <div class="ndd-comment-item" v-for="(item, index) in commentList" :key="index">
                <img src="@/assets/img/headImg.png" alt="" class="ndd-comment-item-head-Img">
                <div style="width: 100%;">
                  <div class="ndd-comment-item-top">
                    <div class="ndd-comment-item-user-tag">
                      <span class="ndd-comment-item-user">{{ item.commentUserName }}</span>
                    </div>
                    <span class="ndd-comment-status" v-if="item.checkedStatus != 1">{{ item.checkedStatus != '2' ? '审核中'
                      :
                      '未通过'
                      }}</span>
                    <div class="ndd-comment-list-like" v-if="item.checkedStatus == 1" @click="handleCommentLikes(item)">
                      <span>{{ item.praisesCount }}</span>
                      <img class="ndd-comment-list-like-img"
                        :src="item.hasClickPraises ? require('@/assets/img/fabulous_o.png') : require('@/assets/img/fabulous.png')"
                        alt="">
                    </div>
                  </div>
                  <div class="ndd-comment-content">{{ item.commentContent }}</div>
                  <div class="ndd-comment-img" v-if="item.fileInfos && item.fileInfos.length">
                    <img v-for="(img, imgIdx) in item.fileInfos" :key="img.id" :src="getCommentImgUrl(img)"
                      :alt="img.originalFileName" class="ndd-comment-img-item"
                      @click="previewCommentImages(item.fileInfos, imgIdx)" />
                  </div>
                  <div class="ndd-comment-footer">
                    <span class="ndd-comment-time">{{ formatDate(item.createDate, 'YYYY-MM-DD HH:mm') }}</span>
                    <span class="ndd-comment-delete" @click="handle(item)">{{ item.createBy == user.id ? '删除' : '回复'
                      }}</span>
                  </div>
                  <!-- 嵌套回复 -->
                  <div v-if="item.children && item.children.length" class="ndd-comment-reply-list">
                    <div class="ndd-comment-reply-item" v-for="reply in item.children" :key="reply.id">
                      <img src="@/assets/img/headImg.png" alt="" class="ndd-comment-reply-head-img">
                      <div>
                        <div>
                          <span class="ndd-comment-reply-user">{{ reply.commentUserName }}</span>
                          <span class="ndd-comment-reply-to">回复</span>
                          <span class="ndd-comment-reply-target">{{ reply.toCommenter }}</span>
                        </div>
                        <div class="ndd-comment-reply-content">{{ reply.commentContent }}</div>
                        <span class="ndd-comment-reply-time">{{ formatDate(reply.createDate, 'YYYY-MM-DD HH:mm')
                          }}</span>
                        <span class="ndd-comment-delete" @click="handle(reply)">{{ reply.createBy == user.id ? '删除' :
                          '回复'
                          }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <template v-else>
              <div class="ndd-comments-more">暂无数据</div>
            </template>
          </div>
          <div>
            <input class="ndd-comment-input" :placeholder="isCollectionEnded() ? '征集已结束' : '发表评论'"
              v-model="commentInput" readonly @click="handleCommentClick" />
          </div>
        </div>
      </div>
    </div>
  </div>
  <van-popup v-model:show="showCompanyPopup" position="bottom" round>
    <div style="padding: 20px; min-width: 260px;">
      <div style="font-weight: bold; font-size: 18px; margin-bottom: 10px; text-align:center;">{{ companyTitle }}</div>
      <ul style="max-height: 300px; overflow-y: auto; padding: 0 0 10px 0;">
        <li v-for="item in companyList" :key="item.companyId"
          style="margin-bottom: 8px; font-size:16px; list-style:none; border-bottom:1px solid #eee; padding:6px 0;">
          {{ item.companyName }}
        </li>
        <li v-if="!companyList.length" style="text-align:center; color:#999;font-size: 14px;margin:30px;">暂无数据</li>
      </ul>
      <van-button block type="primary" @click="closeCompanyList">关闭</van-button>
    </div>
  </van-popup>
  <!-- 评论弹窗 -->
  <van-popup v-model:show="showCommentPopup" position="bottom"
    :style="{ height: commentImages.length > 0 ? '280px' : '230px', borderRadius: '16px 16px 0 0' }">
    <div class="comment-popup-content">
      <van-field v-model="commentInput" rows="4" autosize type="textarea" maxlength="300"
        :placeholder="replyToUser ? `回复${replyToUser}` : '请输入评论'" show-word-limit />
      <!-- 已上传图片预览 -->
      <div class="comment-upload-preview" v-if="commentImages.length">
        <div class="comment-upload-preview-item" v-for="(img, idx) in commentImages" :key="img.id"
          @click.stop="previewCommentImages(commentImages, idx)">
          <img :src="getCommentImgUrl(img)" class="comment-upload-preview-img" />
          <span class="comment-upload-preview-remove" @click.stop="removeCommentImage(idx)">×</span>
        </div>
      </div>
      <div class="comment-popup-bottom">
        <div style="flex: 1; display: flex; align-items: center;">
          <input ref="fileInput" type="file" accept="image/*" style="display: none" @change="handleImageUpload"
            multiple />
          <img src="@/assets/img/icon_upload_img.png" alt="上传图片" class="comment-upload-icon"
            @click="triggerFileInput" />
        </div>
        <div style="flex: 1;"></div>
        <div class="comment-popup-actions">
          <span style="color:#888;font-size:14px;">{{ commentInput.length }}/300</span>
          <van-button type="primary" size="small" @click="sendComment" :disabled="!commentInput.trim()"
            style="margin-left: 12px;">发送</van-button>
        </div>
      </div>
    </div>
  </van-popup>
  <!-- 引入通用用户信息弹窗组件 -->
  <UserInfoPopup v-model:show="showInfoPopup" @close="handlePopupClose" @success="handlePopupSuccess" />
</template>
<script>
export default { name: 'NegotiationActivityPage' }
</script>
<script setup>
import api from '@/api'
import { showToast, showConfirmDialog } from 'vant'
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import config from '@/config'
import { formatDate } from '@/assets/js/utils.js'
import UserInfoPopup from '@/components/UserInfoPopup.vue'
import { isInThirdPartyApp } from '@/utils/environment.js'
const router = useRouter()
const route = useRoute()
const title = ref('协商活动列表')
const negotiationTopics = ref({})
const liveList = ref([
  {
    id: 1,
    img: require('@/assets/img/bg_survey_list_zx.png'),
    title: '市政协召开全市“深化六个改革”重点工...',
  },
  {
    id: 2,
    img: require('@/assets/img/icon_top_bg.png'),
    title: '第二条直播标题示例',
  }
])
const activeIndex = ref(0)
const networkDiscuss = ref({})
const commentList = ref([])
const user = ref(JSON.parse(sessionStorage.getItem('user')) || '')
const showCompanyPopup = ref(false)
const companyList = ref([])
const companyTitle = ref('')
const showCommentPopup = ref(false)
const commentInput = ref('')
const replyToUser = ref('')
const replyParentId = ref(null)
const fileInput = ref(null)
const commentImages = ref([])
// 信息填写弹窗相关变量
const showInfoPopup = ref(false)
onMounted(() => {
  if (title.value) {
    document.title = title.value
  }
  getInfo()
})
// 处理弹窗关闭
const handlePopupClose = () => {
  showInfoPopup.value = false
}

// 处理弹窗成功提交
const handlePopupSuccess = () => {
  showCommentPopup.value = true
}
// 轮播图事件
const onSwipeChange = (index) => {
  activeIndex.value = index
}
// 打开往期、下期协商
const openNegotiation = (_type) => {
  router.push({ path: '/NegotiationActivityList', query: { type: _type } })
}
// 打开问卷调查
const openQuestionnaire = () => {
  router.push({ path: '/QuestionnaireList' })
}
// 获取对应的协商详情
const getInfo = async () => {
  const res = await api.consultActivityInfo({ detailId: route.query.id })
  negotiationTopics.value = res.data
  getNetworkDiscuss()
}
// 点击协商部门、牵头单位
const openCompanyList = (type) => {
  if (type === 'join') {
    companyList.value = negotiationTopics.value.joinCompanys || []
    companyTitle.value = '协商部门'
  } else if (type === 'hold') {
    companyList.value = negotiationTopics.value.holdCompanys || []
    companyTitle.value = '牵头单位'
  }
  showCompanyPopup.value = true
}
// 关闭弹窗
const closeCompanyList = () => {
  showCompanyPopup.value = false
}
// 获取该协商活动的网络议政
const getNetworkDiscuss = async () => {
  const res = await api.opinioncollectList({
    collectStatus: '0',
    pageNo: 1,
    pageSize: 99,
    query: { consultActivityId: route.query.id },
    tableId: 'id_opinion_collect'
  })
  if (res.code == 200) {
    if (res.data && res.data.length > 0) {
      await getOpinioncollectInfo(res.data[0].id)
    }
  } else {
    showToast('网络议政接口出错！')
  }
}
// 获取网络议政详情
const getOpinioncollectInfo = async (id) => {
  const res = await api.opinioncollectInfo({ detailId: id })
  if (res.code == 200) {
    networkDiscuss.value = res.data
    getCommentList()
  } else {
    showToast('网络议政详情接口出错！')
    networkDiscuss.value = {}
  }
}
// 获取网络议政的评论列表
const getCommentList = async () => {
  const res = await api.commentList({
    businessCode: 'opinioncollect',
    businessId: networkDiscuss.value.id,
    pageNo: 1,
    pageSize: 10
  })
  if (res.code == 200) {
    commentList.value = res.data
  } else {
    showToast('评论接口出错！')
  }
}
const getCommentImgUrl = (img) => {
  return config.API_URL + '/image/' + img.newFileName
}
// 处理网络议政评论的删除和回复
const handle = (_item) => {
  if (_item.createBy == user.value.id) {
    handleDels(_item)
  } else {
    handleReply(_item)
  }
}
// 网络议政评论删除
const handleDels = (_item) => {
  showConfirmDialog({
    title: '提示',
    confirmButtonColor: '#4488EB',
    message: '确定删除所选的评论吗？'
  }).then(async () => {
    const res = await api.commentDels({ ids: [_item.id] })
    console.log('删除评论', res)
    if (res.code == 200) {
      showToast('删除成功')
      getCommentList()
    } else {
      showToast(res.data || res.message)
    }
  }).catch(() => {
  })
}
// 网络议政评论回复
const handleReply = (_item) => {
  // 检查征集是否已结束
  if (isCollectionEnded()) {
    showToast('征集已结束，无法回复评论')
    return
  }
  replyToUser.value = _item.commentUserName
  replyParentId.value = _item.id
  showCommentPopup.value = true
  commentInput.value = ''
}
// 判断征集是否已结束
const isCollectionEnded = () => {
  if (!networkDiscuss.value.endDate) {
    return false
  }
  const endDate = new Date(networkDiscuss.value.endDate)
  const currentDate = new Date()
  return currentDate > endDate
}
// 网络议政评论的点赞、取消点赞
const handleCommentLikes = (_item) => {
  if (_item.hasClickPraises) {
    _item.hasClickPraises = false
    _item.praisesCount--
    getLikeDel('comment', _item.id)
  } else {
    _item.hasClickPraises = true
    _item.praisesCount++
    getLikeAdd('comment', _item.id)
  }
}
// 网络议政取消点赞请求
const getLikeDel = async (_type, _id) => {
  await api.getLikeDel({
    businessCode: _type || "opinioncollect",
    businessId: _id || route.query.id
  })
}
// 网络议政点赞请求
const getLikeAdd = async (_type, _id) => {
  await api.getLikeAdd({
    form: {
      businessCode: _type || "opinioncollect",
      businessId: _id || route.query.id
    }
  })
}
// 处理评论点击事件
const handleCommentClick = () => {
  // 检查征集是否已结束
  if (isCollectionEnded()) {
    showToast('征集已结束，无法发表评论')
    return
  }
  // 环境判断
  if (!isInThirdPartyApp()) {
    const publicUser = sessionStorage.getItem('public_user')
    if (publicUser) {
      showCommentPopup.value = true
    } else {
      // 不在第三方app环境中，显示信息填写弹窗
      showInfoPopup.value = true
    }
  } else {
    // 在第三方app环境中，直接显示评论弹窗
    showCommentPopup.value = true
  }
}
const triggerFileInput = () => {
  fileInput.value && fileInput.value.click()
}
const handleImageUpload = async (event) => {
  const files = event.target.files
  if (!files.length) return
  for (let i = 0; i < files.length; i++) {
    const file = files[i]
    const formData = new FormData()
    formData.append('file', file)
    const res = await api.globalUpload(formData, () => { })
    if (res && res.data) {
      commentImages.value.push(res.data)
    }
  }
  event.target.value = '' // 允许重复上传同一图片
}
const removeCommentImage = (idx) => {
  commentImages.value.splice(idx, 1)
}
// 发送评论/回复
const sendComment = async () => {
  const attachmentIds = commentImages.value.map(img => img.id).join(',')
  const res = await api.commentAdd({
    form: {
      attachmentIds,
      businessCode: 'opinioncollect',
      businessId: networkDiscuss.value.id,
      commentContent: commentInput.value,
      terminalName: 'APP',
      parentId: replyParentId.value || null
    }
  })
  if (res.code == 200) {
    showToast('发送成功')
    getCommentList()
  } else {
    showToast(res.data || res.message)
  }
  showCommentPopup.value = false
  commentInput.value = ''
  replyToUser.value = ''
  replyParentId.value = null
}
</script>
<style lang="scss">
.NegotiationActivityPage {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: #F4F5F9;

  .card-container {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    padding: 0px 16px 0 16px;
    margin-top: -30px;

    .card {
      flex: 1;
      background: #fff;
      border-radius: 6px;
      box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.04);
      padding: 10px 12px;
      display: flex;
      justify-content: space-between;

      .card_left {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .card-title {
          font-size: 16px;
          color: #23262A;
        }

        .card-footer {
          display: flex;
          align-items: center;
          color: #979DA3;
          font-size: 15px;
          margin-top: 10px;

          .card-link {
            margin-right: 4px;
          }
        }
      }

      .card-icon {
        width: 30px;
        height: 35px;
        margin-left: 8px;
      }
    }
  }

  .current_period_box {
    background-image: url('@/assets/img/icon_current_period_bg.png');
    background-size: 100% 100%;
    margin: 5px 8px 0 8px;
    padding: 20px 18px 18px 18px;
    min-height: 380px;

    .current_period_title_box {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16px;
      width: 100%;

      .current_period_title_line {
        width: 87px;
        height: 6px;
        object-fit: contain;
      }

      .current_period_title {
        color: #3B7CFF;
        font-size: 18px;
        font-weight: bold;
        margin: 0 12px;
        letter-spacing: 1px;
      }
    }

    .current_period_content_box {
      border-radius: 6px;
      padding: 0px 14px 14px 14px;
      width: 100%;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      .current_period_content_title {
        color: #23262A;
        font-size: 17px;
        font-weight: bold;
        margin-bottom: 10px;
        text-align: center;
        width: 100%;
      }

      .current_period_content_text {
        color: #474B4F;
        font-size: 16px;
        line-height: 1.8;
        text-indent: 2em;
      }
    }
  }

  .participants-section {
    margin: 10px 16px;

    .participants-cards {
      display: flex;
      gap: 18px;

      .participant-card {
        background: linear-gradient(90deg, #DDF1FF 0%, #C2DFFE 100%);
        box-shadow: 0px 2 8px 0px rgba(24, 64, 118, 0.05);
        border-radius: 4px 4px 4px 4px;
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 15px 16px;

        .card-text {
          color: #3B7CFF;
          font-size: 16px;
          font-weight: 500;
        }

        .card-icon {
          width: 25px;
          height: 25px;
          object-fit: contain;
        }
      }
    }
  }

  .video_graphic_live {
    margin: 16px 16px;

    .live-swiper-box {
      position: relative;
      overflow: hidden;
      margin-bottom: 20px;
      background: #FFFFFF;
      box-shadow: 0px 2 8px 0px rgba(24, 64, 118, 0.05);
      border-radius: 4px;

      .live-swiper {
        height: 150px;
        border-radius: 6px;
        position: relative;
        margin: 15px;

        .live-img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          display: block;
        }

        .live-title-bar {
          position: absolute;
          left: 0;
          right: 0;
          bottom: 0;
          height: 44px;
          background: rgba(0, 0, 0, 0.148);
          display: flex;
          align-items: center;
          z-index: 2;

          .live-title {
            color: #fff;
            font-size: 14px;
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding-left: 16px;
            width: 80%;
          }
        }
      }
    }
  }

  .participate_questionnaire {
    margin: 16px 16px;

    .participate_questionnaire_content {
      position: relative;

      .participate_questionnaire_img {
        width: 100%;
        height: 100%;
      }

      .enter {
        position: absolute;
        top: 50px;
        left: 47px;
        background: #245EF8;
        border-radius: 9px;
        font-size: 12px;
        color: #FFFFFF;
        padding: 2px 5px;
      }
    }
  }

  .network_discuss {
    margin: 16px 16px;

    .network_discuss_content {
      background: #FFFFFF;
      box-shadow: 0px 2 8px 0px rgba(24, 64, 118, 0.05);
      border-radius: 4px;

      .network-discuss-card {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.04);
        padding: 18px 16px 12px 16px;
        margin-bottom: 12px;
        font-size: 15px;

        .network-discuss-header {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 13px;
            font-weight: 500;
            margin-right: 8px;

            &.tag-blue {
              background: #eaf4ff;
              color: #3b7cff;
            }

            &.tag-red {
              background: #fff0f0;
              color: #ff4d4f;
            }
          }
        }

        .network-discuss-body {
          color: #23262a;
          font-size: 15px;
          line-height: 1.7;
          margin-bottom: 14px;
        }

        .network-discuss-comment-title {
          font-size: 15px;
          color: #0146C7;
          font-weight: bold;
          margin-top: 16px;
        }

        .ndd-comment-list {
          display: flex;
          flex-direction: column;
          margin-bottom: 16px;

          .ndd-comment-item {
            display: flex;
            align-items: flex-start;
            width: auto;
            padding: 15px 0px;
            border-bottom: 1px solid rgb(238, 238, 238);

            .ndd-comment-item-head-Img {
              width: 30px;
              height: 30px;
              border-radius: 50%;
              margin-right: 8px;
              margin-top: 2px;
            }

            .ndd-comment-item-top {
              display: flex;
              align-items: center;
              justify-content: space-between;

              .ndd-comment-item-user-tag {
                display: flex;
                align-items: center;

                .ndd-comment-item-user {
                  font-weight: 600;
                  color: #333333;
                  font-size: 16px;
                  margin-right: 15px;
                }
              }

              .ndd-comment-status {
                font-size: 14px;
                color: rgb(230, 162, 60);
              }

              .ndd-comment-list-like {
                display: flex;
                align-items: center;

                span {
                  margin-right: 5px;
                  color: rgb(102, 102, 102);
                  font-size: 14px;
                }

                .ndd-comment-list-like-img {
                  width: 19px;
                  height: 19px;
                }
              }
            }

            .ndd-comment-content {
              color: #333333;
              margin-top: 5px;
              word-break: break-all;
              font-size: 15px;
            }

            .ndd-comment-img {
              margin-top: 15px;

              .ndd-comment-img-item {
                width: 60px;
                height: 60px;
                margin-right: 10px;
                object-fit: cover;
                border-radius: 4px;
              }
            }

            .ndd-comment-footer {
              display: flex;
              align-items: center;
              margin-top: 10px;

              .ndd-comment-time {
                font-size: 14px;
                color: rgb(153, 153, 153);
              }

              .ndd-comment-delete {
                font-size: 14px;
                margin-left: 20px;
                color: rgb(51, 51, 51);
              }
            }
          }

          .ndd-comments-more {
            text-align: center;
            color: #bbb;
            font-size: 13px;
            margin-top: 8px;
          }
        }

        .ndd-comment-input {
          width: 100%;
          border: none;
          outline: none;
          background: #f4f5f9;
          border-radius: 16px;
          padding: 8px 12px;
          margin-right: 8px;
          font-size: 14px;
        }
      }
    }
  }



  // 覆盖原生指示器样式
  .van-swipe__indicators {
    position: absolute;
    right: 16px;
    left: auto;
    bottom: 18px;
    top: auto;
    z-index: 3;
    display: flex;
    align-items: center;
    background: none;

    .van-swipe__indicator {
      background: rgba(255, 255, 255, 0.6);

      &.van-swipe__indicator--active {
        background: #fff;
      }
    }
  }

  .header_title {
    display: flex;
    align-items: center;
    font-size: 17px;
    font-weight: bold;
    color: #23262A;
    margin-bottom: 12px;

    .header_line {
      width: 4px;
      height: 18px;
      background: #3B7CFF;
      border-radius: 2px;
      margin-right: 8px;
      display: inline-block;
    }
  }
}

.comment-popup-content {
  padding: 16px 16px 0 16px;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;

  .comment-popup-bottom {
    display: flex;
    align-items: center;
    margin-top: 12px;
    justify-content: space-between;

    .comment-upload-icon {
      width: 24px;
      height: 24px;
    }

    .comment-popup-actions {
      display: flex;
      align-items: center;
    }
  }

  .comment-upload-preview {
    display: flex;
    margin: 8px 0 0 0;

    .comment-upload-preview-item {
      position: relative;
      margin-right: 8px;

      .comment-upload-preview-img {
        width: 48px;
        height: 48px;
        object-fit: cover;
        border-radius: 4px;
      }

      .comment-upload-preview-remove {
        position: absolute;
        top: -6px;
        right: -6px;
        background: #fff;
        color: #f00;
        border-radius: 50%;
        width: 18px;
        height: 18px;
        text-align: center;
        line-height: 18px;
        font-size: 14px;
        cursor: pointer;
        border: 1px solid #eee;
      }
    }
  }
}
</style>
