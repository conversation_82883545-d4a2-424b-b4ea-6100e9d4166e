{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, createTextVNode as _createTextVNode, vModelText as _vModelText, withDirectives as _withDirectives, createStaticVNode as _createStaticVNode } from \"vue\";\nimport _imports_0 from '../../assets/img/icon_museum.png';\nvar _hoisted_1 = {\n  class: \"archive-visit-booking\"\n};\nvar _hoisted_2 = {\n  class: \"section\"\n};\nvar _hoisted_3 = {\n  class: \"date-grid flex_box\"\n};\nvar _hoisted_4 = [\"onClick\"];\nvar _hoisted_5 = {\n  class: \"day\"\n};\nvar _hoisted_6 = {\n  class: \"date\"\n};\nvar _hoisted_7 = {\n  class: \"status\"\n};\nvar _hoisted_8 = {\n  class: \"section\"\n};\nvar _hoisted_9 = {\n  class: \"time-slots\"\n};\nvar _hoisted_10 = [\"onClick\"];\nvar _hoisted_11 = {\n  class: \"time\"\n};\nvar _hoisted_12 = {\n  class: \"capacity\"\n};\nvar _hoisted_13 = {\n  class: \"section\"\n};\nvar _hoisted_14 = {\n  class: \"form-item\"\n};\nvar _hoisted_15 = {\n  class: \"counter\"\n};\nvar _hoisted_16 = {\n  class: \"count\"\n};\nvar _hoisted_17 = {\n  class: \"form-item\"\n};\nvar _hoisted_18 = {\n  class: \"form-item\"\n};\nvar _hoisted_19 = {\n  class: \"form-item\"\n};\nvar _hoisted_20 = {\n  class: \"form-item\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 选择入馆日期 \"), _createElementVNode(\"div\", _hoisted_2, [_cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n    class: \"section-title flex_box flex_justify_between\"\n  }, [_createElementVNode(\"div\", {\n    class: \"flex_box flex_align_center\"\n  }, [_createElementVNode(\"img\", {\n    src: _imports_0,\n    alt: \"\",\n    class: \"icon_museum\"\n  }), _createElementVNode(\"div\", null, \"选择入馆日期\")]), _createElementVNode(\"div\", {\n    class: \"year\"\n  }, \"2025年\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.dateList, function (date) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: _normalizeClass([\"date-item\", {\n        active: date.active,\n        disabled: date.disabled\n      }]),\n      key: date.day,\n      onClick: function onClick($event) {\n        return $setup.selectDate(date);\n      }\n    }, [_createElementVNode(\"div\", _hoisted_5, _toDisplayString(date.day), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_6, _toDisplayString(date.date), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_7, _toDisplayString(date.status), 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_4);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 选择入馆时段 \"), _createElementVNode(\"div\", _hoisted_8, [_cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n    class: \"section-title\"\n  }, [_createElementVNode(\"span\", {\n    class: \"star\"\n  }, \"★\"), _createTextVNode(\" 选择入馆时段 \")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_9, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.timeSlots, function (slot) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: _normalizeClass([\"time-slot\", {\n        active: slot.active\n      }]),\n      key: slot.id,\n      onClick: function onClick($event) {\n        return $setup.selectTimeSlot(slot);\n      }\n    }, [_createElementVNode(\"div\", _hoisted_11, _toDisplayString(slot.time), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_12, _toDisplayString(slot.status) + \" (\" + _toDisplayString(slot.count) + \"人)\", 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_10);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 添加参观人员 \"), _createElementVNode(\"div\", _hoisted_13, [_cache[11] || (_cache[11] = _createElementVNode(\"div\", {\n    class: \"section-title\"\n  }, [_createElementVNode(\"span\", {\n    class: \"icon\"\n  }, \"👥\"), _createTextVNode(\" 添加参观人员 \")], -1 /* CACHED */)), _createCommentVNode(\" 参观人数 \"), _createElementVNode(\"div\", _hoisted_14, [_cache[6] || (_cache[6] = _createElementVNode(\"label\", {\n    class: \"label\"\n  }, \"*参观人数\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"button\", {\n    class: \"counter-btn\",\n    onClick: $setup.decreaseCount\n  }, \"-\"), _createElementVNode(\"span\", _hoisted_16, _toDisplayString($setup.visitorCount), 1 /* TEXT */), _createElementVNode(\"button\", {\n    class: \"counter-btn\",\n    onClick: $setup.increaseCount\n  }, \"+\")])]), _createCommentVNode(\" 联系人 \"), _createElementVNode(\"div\", _hoisted_17, [_cache[7] || (_cache[7] = _createElementVNode(\"label\", {\n    class: \"label\"\n  }, \"*联系人\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"input\",\n    placeholder: \"请输入姓名\",\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.contactName = $event;\n    })\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $setup.contactName]])]), _createCommentVNode(\" 联系方式 \"), _createElementVNode(\"div\", _hoisted_18, [_cache[8] || (_cache[8] = _createElementVNode(\"label\", {\n    class: \"label\"\n  }, \"*联系方式\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"input\",\n    placeholder: \"请输入手机号\",\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.contactPhone = $event;\n    })\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $setup.contactPhone]])]), _createCommentVNode(\" 团体名称 \"), _createElementVNode(\"div\", _hoisted_19, [_cache[9] || (_cache[9] = _createElementVNode(\"label\", {\n    class: \"label\"\n  }, \"*团体名称\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"input\",\n    placeholder: \"请输入单位名称\",\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n      return $setup.groupName = $event;\n    })\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $setup.groupName]])]), _createCommentVNode(\" 单位地址 \"), _createElementVNode(\"div\", _hoisted_20, [_cache[10] || (_cache[10] = _createElementVNode(\"label\", {\n    class: \"label\"\n  }, \"*单位地址\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"input\",\n    placeholder: \"请选择单位地址\",\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n      return $setup.groupAddress = $event;\n    })\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $setup.groupAddress]])])]), _createCommentVNode(\" 底部按钮 \"), _cache[12] || (_cache[12] = _createStaticVNode(\"<div class=\\\"bottom-actions\\\" data-v-b40b4332><div class=\\\"action-icons\\\" data-v-b40b4332><div class=\\\"action-icon\\\" data-v-b40b4332><span class=\\\"icon\\\" data-v-b40b4332>👤</span><span class=\\\"text\\\" data-v-b40b4332>人员名单</span></div><div class=\\\"action-icon\\\" data-v-b40b4332><span class=\\\"icon\\\" data-v-b40b4332>📋</span><span class=\\\"text\\\" data-v-b40b4332>填写说明</span></div><div class=\\\"action-icon\\\" data-v-b40b4332><span class=\\\"icon\\\" data-v-b40b4332>📎</span><span class=\\\"text\\\" data-v-b40b4332>附件</span></div></div><div class=\\\"submit-buttons\\\" data-v-b40b4332><button class=\\\"btn btn-secondary\\\" data-v-b40b4332>提交预约</button><button class=\\\"btn btn-primary\\\" data-v-b40b4332>我的预约</button></div></div>\", 1))]);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "src", "alt", "_hoisted_3", "_Fragment", "_renderList", "$setup", "dateList", "date", "_normalizeClass", "active", "disabled", "key", "day", "onClick", "$event", "selectDate", "_hoisted_5", "_toDisplayString", "_hoisted_6", "_hoisted_7", "status", "_hoisted_8", "_hoisted_9", "timeSlots", "slot", "id", "selectTimeSlot", "_hoisted_11", "time", "_hoisted_12", "count", "_hoisted_13", "_hoisted_14", "_hoisted_15", "decreaseCount", "_hoisted_16", "visitorCount", "increaseCount", "_hoisted_17", "type", "placeholder", "contactName", "_hoisted_18", "contactPhone", "_hoisted_19", "groupName", "_hoisted_20", "groupAddress"], "sources": ["D:\\zy\\xm\\h5\\i西安\\appperformduties\\src\\views\\ArchiveVisitBooking\\ArchiveVisitBooking.vue"], "sourcesContent": ["<template>\r\n  <div class=\"archive-visit-booking\">\r\n    <!-- 选择入馆日期 -->\r\n    <div class=\"section\">\r\n      <div class=\"section-title flex_box flex_justify_between\">\r\n        <div class=\"flex_box flex_align_center\">\r\n          <img src=\"../../assets/img/icon_museum.png \" alt=\"\" class=\"icon_museum\">\r\n          <div>选择入馆日期</div>\r\n        </div>\r\n        <div class=\"year\">2025年</div>\r\n      </div>\r\n      <div class=\"date-grid flex_box\">\r\n        <div class=\"date-item\" v-for=\"date in dateList\" :key=\"date.day\"\r\n          :class=\"{ active: date.active, disabled: date.disabled }\" @click=\"selectDate(date)\">\r\n          <div class=\"day\">{{ date.day }}</div>\r\n          <div class=\"date\">{{ date.date }}</div>\r\n          <div class=\"status\">{{ date.status }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 选择入馆时段 -->\r\n    <div class=\"section\">\r\n      <div class=\"section-title\">\r\n        <span class=\"star\">★</span>\r\n        选择入馆时段\r\n      </div>\r\n      <div class=\"time-slots\">\r\n        <div class=\"time-slot\" v-for=\"slot in timeSlots\" :key=\"slot.id\" :class=\"{ active: slot.active }\"\r\n          @click=\"selectTimeSlot(slot)\">\r\n          <div class=\"time\">{{ slot.time }}</div>\r\n          <div class=\"capacity\">{{ slot.status }} ({{ slot.count }}人)</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 添加参观人员 -->\r\n    <div class=\"section\">\r\n      <div class=\"section-title\">\r\n        <span class=\"icon\">👥</span>\r\n        添加参观人员\r\n      </div>\r\n\r\n      <!-- 参观人数 -->\r\n      <div class=\"form-item\">\r\n        <label class=\"label\">*参观人数</label>\r\n        <div class=\"counter\">\r\n          <button class=\"counter-btn\" @click=\"decreaseCount\">-</button>\r\n          <span class=\"count\">{{ visitorCount }}</span>\r\n          <button class=\"counter-btn\" @click=\"increaseCount\">+</button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 联系人 -->\r\n      <div class=\"form-item\">\r\n        <label class=\"label\">*联系人</label>\r\n        <input type=\"text\" class=\"input\" placeholder=\"请输入姓名\" v-model=\"contactName\">\r\n      </div>\r\n\r\n      <!-- 联系方式 -->\r\n      <div class=\"form-item\">\r\n        <label class=\"label\">*联系方式</label>\r\n        <input type=\"text\" class=\"input\" placeholder=\"请输入手机号\" v-model=\"contactPhone\">\r\n      </div>\r\n\r\n      <!-- 团体名称 -->\r\n      <div class=\"form-item\">\r\n        <label class=\"label\">*团体名称</label>\r\n        <input type=\"text\" class=\"input\" placeholder=\"请输入单位名称\" v-model=\"groupName\">\r\n      </div>\r\n\r\n      <!-- 单位地址 -->\r\n      <div class=\"form-item\">\r\n        <label class=\"label\">*单位地址</label>\r\n        <input type=\"text\" class=\"input\" placeholder=\"请选择单位地址\" v-model=\"groupAddress\">\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 底部按钮 -->\r\n    <div class=\"bottom-actions\">\r\n      <div class=\"action-icons\">\r\n        <div class=\"action-icon\">\r\n          <span class=\"icon\">👤</span>\r\n          <span class=\"text\">人员名单</span>\r\n        </div>\r\n        <div class=\"action-icon\">\r\n          <span class=\"icon\">📋</span>\r\n          <span class=\"text\">填写说明</span>\r\n        </div>\r\n        <div class=\"action-icon\">\r\n          <span class=\"icon\">📎</span>\r\n          <span class=\"text\">附件</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"submit-buttons\">\r\n        <button class=\"btn btn-secondary\">提交预约</button>\r\n        <button class=\"btn btn-primary\">我的预约</button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default { name: 'ArchiveVisitBooking' }\r\n</script>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue'\r\n\r\n// 响应式数据\r\nconst visitorCount = ref(1)\r\nconst contactName = ref('')\r\nconst contactPhone = ref('')\r\nconst groupName = ref('')\r\nconst groupAddress = ref('')\r\n\r\n// 日期数据\r\nconst dateList = ref([\r\n  { day: '二', date: '9-2', status: '可预约', active: false, disabled: false },\r\n  { day: '三', date: '9-3', status: '可预约', active: false, disabled: false },\r\n  { day: '四', date: '9-4', status: '可预约', active: false, disabled: false },\r\n  { day: '五', date: '9-5', status: '可预约', active: true, disabled: false },\r\n  { day: '六', date: '9-6', status: '可预约', active: false, disabled: false },\r\n  { day: '日', date: '9-7', status: '可预约', active: false, disabled: false },\r\n  { day: '一', date: '9-8', status: '可预约', active: false, disabled: true }\r\n])\r\n\r\n// 时段数据\r\nconst timeSlots = ref([\r\n  { id: 1, time: '09:00-11:00', status: '可预约', count: 48, active: false },\r\n  { id: 2, time: '13:30-16:30', status: '可预约', count: 160, active: true }\r\n])\r\n\r\n// 方法\r\nconst selectDate = (date) => {\r\n  if (date.disabled) return\r\n  dateList.value.forEach(item => item.active = false)\r\n  date.active = true\r\n}\r\n\r\nconst selectTimeSlot = (slot) => {\r\n  timeSlots.value.forEach(item => item.active = false)\r\n  slot.active = true\r\n}\r\n\r\nconst increaseCount = () => {\r\n  visitorCount.value++\r\n}\r\n\r\nconst decreaseCount = () => {\r\n  if (visitorCount.value > 1) {\r\n    visitorCount.value--\r\n  }\r\n}\r\n\r\nonMounted(() => {\r\n  // 初始化逻辑\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.archive-visit-booking {\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow: auto;\r\n  background: #f9f9f9;\r\n  padding: 10px 10px;\r\n\r\n  .section {\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    margin-bottom: 12px;\r\n    padding: 10px 6px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n    .section-title {\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n      color: #333;\r\n      margin-bottom: 12px;\r\n\r\n      .icon_museum {\r\n        width: 16px;\r\n        height: 15px;\r\n        margin-right: 8px;\r\n      }\r\n\r\n      .year {\r\n        font-size: 14px;\r\n        color: #666;\r\n        font-weight: normal;\r\n      }\r\n    }\r\n\r\n    // 日期选择样式\r\n    .date-grid {\r\n      gap: 6px;\r\n      overflow-x: auto;\r\n\r\n      .date-item {\r\n        flex: 1;\r\n        max-width: 70px;\r\n        background: #f8f8f8;\r\n        border-radius: 6px;\r\n        padding: 8px 4px;\r\n        text-align: center;\r\n        cursor: pointer;\r\n        transition: all 0.3s ease;\r\n        border: 1px solid transparent;\r\n        position: relative;\r\n\r\n        .day {\r\n          font-size: 12px;\r\n          color: #666;\r\n          margin-bottom: 3px;\r\n          line-height: 1;\r\n          font-weight: normal;\r\n        }\r\n\r\n        .date {\r\n          font-size: 13px;\r\n          font-weight: 500;\r\n          color: #333;\r\n          margin-bottom: 3px;\r\n          line-height: 1;\r\n        }\r\n\r\n        .status {\r\n          font-size: 10px;\r\n          color: #999;\r\n          line-height: 1;\r\n          font-weight: normal;\r\n        }\r\n\r\n        &.active {\r\n          background: #fff3e0;\r\n          border-color: #ff6b35;\r\n\r\n          .day,\r\n          .date {\r\n            color: #ff6b35;\r\n          }\r\n\r\n          .status {\r\n            color: #ff6b35;\r\n          }\r\n        }\r\n\r\n        &.disabled {\r\n          opacity: 0.4;\r\n          cursor: not-allowed;\r\n          background: #f5f5f5;\r\n\r\n          .day,\r\n          .date,\r\n          .status {\r\n            color: #ccc;\r\n          }\r\n        }\r\n\r\n        &:hover:not(.disabled):not(.active) {\r\n          background: #f0f0f0;\r\n          border-color: #e0e0e0;\r\n        }\r\n      }\r\n    }\r\n\r\n    // 时段选择样式\r\n    .time-slots {\r\n      display: grid;\r\n      grid-template-columns: repeat(2, 1fr);\r\n      gap: 12px;\r\n\r\n      .time-slot {\r\n        background: #f8f8f8;\r\n        border-radius: 8px;\r\n        padding: 16px;\r\n        cursor: pointer;\r\n        transition: all 0.3s ease;\r\n        border: 2px solid transparent;\r\n\r\n        .time {\r\n          font-size: 16px;\r\n          font-weight: 600;\r\n          color: #333;\r\n          margin-bottom: 4px;\r\n        }\r\n\r\n        .capacity {\r\n          font-size: 14px;\r\n          color: #666;\r\n        }\r\n\r\n        &.active {\r\n          background: #fff3e0;\r\n          border-color: #ff6b35;\r\n\r\n          .time {\r\n            color: #ff6b35;\r\n          }\r\n\r\n          .capacity {\r\n            color: #ff6b35;\r\n          }\r\n        }\r\n\r\n        &:hover {\r\n          background: #f0f0f0;\r\n        }\r\n      }\r\n    }\r\n\r\n    // 表单样式\r\n    .form-item {\r\n      margin-bottom: 16px;\r\n\r\n      .label {\r\n        display: block;\r\n        font-size: 14px;\r\n        color: #333;\r\n        margin-bottom: 8px;\r\n        font-weight: 500;\r\n\r\n        &::before {\r\n          content: '*';\r\n          color: #ff6b35;\r\n          margin-right: 4px;\r\n        }\r\n      }\r\n\r\n      .input {\r\n        width: 100%;\r\n        padding: 12px 16px;\r\n        border: 1px solid #ddd;\r\n        border-radius: 8px;\r\n        font-size: 16px;\r\n        background: white;\r\n        transition: border-color 0.3s ease;\r\n\r\n        &:focus {\r\n          outline: none;\r\n          border-color: #ff6b35;\r\n        }\r\n\r\n        &::placeholder {\r\n          color: #999;\r\n        }\r\n      }\r\n\r\n      .counter {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 16px;\r\n\r\n        .counter-btn {\r\n          width: 32px;\r\n          height: 32px;\r\n          border: 1px solid #ddd;\r\n          border-radius: 6px;\r\n          background: white;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          cursor: pointer;\r\n          font-size: 18px;\r\n          color: #666;\r\n          transition: all 0.3s ease;\r\n\r\n          &:hover {\r\n            border-color: #ff6b35;\r\n            color: #ff6b35;\r\n          }\r\n\r\n          &:active {\r\n            background: #f0f0f0;\r\n          }\r\n        }\r\n\r\n        .count {\r\n          font-size: 18px;\r\n          font-weight: 600;\r\n          color: #333;\r\n          min-width: 24px;\r\n          text-align: center;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 底部操作区域\r\n  .bottom-actions {\r\n    background: white;\r\n    border-radius: 8px;\r\n    padding: 16px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n    .action-icons {\r\n      display: flex;\r\n      justify-content: space-around;\r\n      margin-bottom: 20px;\r\n      padding-bottom: 16px;\r\n      border-bottom: 1px solid #f0f0f0;\r\n\r\n      .action-icon {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        cursor: pointer;\r\n        transition: all 0.3s ease;\r\n\r\n        .icon {\r\n          font-size: 24px;\r\n          margin-bottom: 4px;\r\n        }\r\n\r\n        .text {\r\n          font-size: 12px;\r\n          color: #666;\r\n        }\r\n\r\n        &:hover {\r\n          .text {\r\n            color: #ff6b35;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .submit-buttons {\r\n      display: grid;\r\n      grid-template-columns: 1fr 1fr;\r\n      gap: 12px;\r\n\r\n      .btn {\r\n        padding: 14px 24px;\r\n        border-radius: 8px;\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        border: none;\r\n        cursor: pointer;\r\n        transition: all 0.3s ease;\r\n\r\n        &.btn-secondary {\r\n          background: #f8f8f8;\r\n          color: #666;\r\n\r\n          &:hover {\r\n            background: #e8e8e8;\r\n          }\r\n        }\r\n\r\n        &.btn-primary {\r\n          background: #ff6b35;\r\n          color: white;\r\n\r\n          &:hover {\r\n            background: #e55a2b;\r\n          }\r\n        }\r\n\r\n        &:active {\r\n          transform: translateY(1px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 响应式设计\r\n// @media (max-width: 768px) {\r\n//   .archive-visit-booking {\r\n//     padding: 12px;\r\n\r\n//     .section {\r\n//       padding: 12px;\r\n\r\n//       .date-grid {\r\n//         grid-template-columns: repeat(3, 1fr);\r\n//       }\r\n\r\n//       .time-slots {\r\n//         grid-template-columns: 1fr;\r\n//       }\r\n//     }\r\n//   }\r\n// }</style>"], "mappings": ";OAMeA,UAAuC;;EAL/CC,KAAK,EAAC;AAAuB;;EAE3BA,KAAK,EAAC;AAAS;;EAQbA,KAAK,EAAC;AAAoB;;;EAGtBA,KAAK,EAAC;AAAK;;EACXA,KAAK,EAAC;AAAM;;EACZA,KAAK,EAAC;AAAQ;;EAMpBA,KAAK,EAAC;AAAS;;EAKbA,KAAK,EAAC;AAAY;;;EAGdA,KAAK,EAAC;AAAM;;EACZA,KAAK,EAAC;AAAU;;EAMtBA,KAAK,EAAC;AAAS;;EAObA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAS;;EAEZA,KAAK,EAAC;AAAO;;EAMlBA,KAAK,EAAC;AAAW;;EAMjBA,KAAK,EAAC;AAAW;;EAMjBA,KAAK,EAAC;AAAW;;EAMjBA,KAAK,EAAC;AAAW;;uBAvE1BC,mBAAA,CAkGM,OAlGNC,UAkGM,GAjGJC,mBAAA,YAAe,EACfC,mBAAA,CAgBM,OAhBNC,UAgBM,G,0BAfJD,mBAAA,CAMM;IANDJ,KAAK,EAAC;EAA6C,IACtDI,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAA4B,IACrCI,mBAAA,CAAwE;IAAnEE,GAAuC,EAAvCP,UAAuC;IAACQ,GAAG,EAAC,EAAE;IAACP,KAAK,EAAC;MAC1DI,mBAAA,CAAiB,aAAZ,QAAM,E,GAEbA,mBAAA,CAA6B;IAAxBJ,KAAK,EAAC;EAAM,GAAC,OAAK,E,qBAEzBI,mBAAA,CAOM,OAPNI,UAOM,I,kBANJP,mBAAA,CAKMQ,SAAA,QAAAC,WAAA,CALgCC,MAAA,CAAAC,QAAQ,YAAhBC,IAAI;yBAAlCZ,mBAAA,CAKM;MALDD,KAAK,EAAAc,eAAA,EAAC,WAAW;QAAAC,MAAA,EACFF,IAAI,CAACE,MAAM;QAAAC,QAAA,EAAYH,IAAI,CAACG;MAAQ;MADPC,GAAG,EAAEJ,IAAI,CAACK,GAAG;MACDC,OAAK,WAALA,OAAKA,CAAAC,MAAA;QAAA,OAAET,MAAA,CAAAU,UAAU,CAACR,IAAI;MAAA;QACjFT,mBAAA,CAAqC,OAArCkB,UAAqC,EAAAC,gBAAA,CAAjBV,IAAI,CAACK,GAAG,kBAC5Bd,mBAAA,CAAuC,OAAvCoB,UAAuC,EAAAD,gBAAA,CAAlBV,IAAI,CAACA,IAAI,kBAC9BT,mBAAA,CAA2C,OAA3CqB,UAA2C,EAAAF,gBAAA,CAApBV,IAAI,CAACa,MAAM,iB;sCAKxCvB,mBAAA,YAAe,EACfC,mBAAA,CAYM,OAZNuB,UAYM,G,0BAXJvB,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAe,IACxBI,mBAAA,CAA2B;IAArBJ,KAAK,EAAC;EAAM,GAAC,GAAC,G,iBAAO,UAE7B,E,qBACAI,mBAAA,CAMM,OANNwB,UAMM,I,kBALJ3B,mBAAA,CAIMQ,SAAA,QAAAC,WAAA,CAJgCC,MAAA,CAAAkB,SAAS,YAAjBC,IAAI;yBAAlC7B,mBAAA,CAIM;MAJDD,KAAK,EAAAc,eAAA,EAAC,WAAW;QAAAC,MAAA,EAA4De,IAAI,CAACf;MAAM;MAA3CE,GAAG,EAAEa,IAAI,CAACC,EAAE;MAC3DZ,OAAK,WAALA,OAAKA,CAAAC,MAAA;QAAA,OAAET,MAAA,CAAAqB,cAAc,CAACF,IAAI;MAAA;QAC3B1B,mBAAA,CAAuC,OAAvC6B,WAAuC,EAAAV,gBAAA,CAAlBO,IAAI,CAACI,IAAI,kBAC9B9B,mBAAA,CAAiE,OAAjE+B,WAAiE,EAAAZ,gBAAA,CAAxCO,IAAI,CAACJ,MAAM,IAAG,IAAE,GAAAH,gBAAA,CAAGO,IAAI,CAACM,KAAK,IAAG,IAAE,gB;sCAKjEjC,mBAAA,YAAe,EACfC,mBAAA,CAuCM,OAvCNiC,WAuCM,G,4BAtCJjC,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAe,IACxBI,mBAAA,CAA4B;IAAtBJ,KAAK,EAAC;EAAM,GAAC,IAAE,G,iBAAO,UAE9B,E,qBAEAG,mBAAA,UAAa,EACbC,mBAAA,CAOM,OAPNkC,WAOM,G,0BANJlC,mBAAA,CAAkC;IAA3BJ,KAAK,EAAC;EAAO,GAAC,OAAK,qBAC1BI,mBAAA,CAIM,OAJNmC,WAIM,GAHJnC,mBAAA,CAA6D;IAArDJ,KAAK,EAAC,aAAa;IAAEmB,OAAK,EAAER,MAAA,CAAA6B;KAAe,GAAC,GACpDpC,mBAAA,CAA6C,QAA7CqC,WAA6C,EAAAlB,gBAAA,CAAtBZ,MAAA,CAAA+B,YAAY,kBACnCtC,mBAAA,CAA6D;IAArDJ,KAAK,EAAC,aAAa;IAAEmB,OAAK,EAAER,MAAA,CAAAgC;KAAe,GAAC,E,KAIxDxC,mBAAA,SAAY,EACZC,mBAAA,CAGM,OAHNwC,WAGM,G,0BAFJxC,mBAAA,CAAiC;IAA1BJ,KAAK,EAAC;EAAO,GAAC,MAAI,qB,gBACzBI,mBAAA,CAA2E;IAApEyC,IAAI,EAAC,MAAM;IAAC7C,KAAK,EAAC,OAAO;IAAC8C,WAAW,EAAC,OAAO;;aAAUnC,MAAA,CAAAoC,WAAW,GAAA3B,MAAA;IAAA;iDAAXT,MAAA,CAAAoC,WAAW,E,KAG3E5C,mBAAA,UAAa,EACbC,mBAAA,CAGM,OAHN4C,WAGM,G,0BAFJ5C,mBAAA,CAAkC;IAA3BJ,KAAK,EAAC;EAAO,GAAC,OAAK,qB,gBAC1BI,mBAAA,CAA6E;IAAtEyC,IAAI,EAAC,MAAM;IAAC7C,KAAK,EAAC,OAAO;IAAC8C,WAAW,EAAC,QAAQ;;aAAUnC,MAAA,CAAAsC,YAAY,GAAA7B,MAAA;IAAA;iDAAZT,MAAA,CAAAsC,YAAY,E,KAG7E9C,mBAAA,UAAa,EACbC,mBAAA,CAGM,OAHN8C,WAGM,G,0BAFJ9C,mBAAA,CAAkC;IAA3BJ,KAAK,EAAC;EAAO,GAAC,OAAK,qB,gBAC1BI,mBAAA,CAA2E;IAApEyC,IAAI,EAAC,MAAM;IAAC7C,KAAK,EAAC,OAAO;IAAC8C,WAAW,EAAC,SAAS;;aAAUnC,MAAA,CAAAwC,SAAS,GAAA/B,MAAA;IAAA;iDAATT,MAAA,CAAAwC,SAAS,E,KAG3EhD,mBAAA,UAAa,EACbC,mBAAA,CAGM,OAHNgD,WAGM,G,4BAFJhD,mBAAA,CAAkC;IAA3BJ,KAAK,EAAC;EAAO,GAAC,OAAK,qB,gBAC1BI,mBAAA,CAA8E;IAAvEyC,IAAI,EAAC,MAAM;IAAC7C,KAAK,EAAC,OAAO;IAAC8C,WAAW,EAAC,SAAS;;aAAUnC,MAAA,CAAA0C,YAAY,GAAAjC,MAAA;IAAA;iDAAZT,MAAA,CAAA0C,YAAY,E,OAIhFlD,mBAAA,UAAa,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}