<template>
  <div class="environment-test">
    <div class="header">
      <h2>环境检测测试页面</h2>
    </div>
    
    <div class="test-section">
      <h3>当前环境信息</h3>
      <div class="info-item">
        <label>是否在第三方App中:</label>
        <span :class="{ 'success': inThirdPartyApp, 'error': !inThirdPartyApp }">
          {{ inThirdPartyApp ? '是' : '否' }}
        </span>
      </div>
      <div class="info-item">
        <label>环境类型:</label>
        <span class="env-type">{{ environmentType }}</span>
      </div>
      <div class="info-item">
        <label>User Agent:</label>
        <span class="user-agent">{{ userAgent }}</span>
      </div>
    </div>

    <div class="test-section">
      <h3>功能支持检测</h3>
      <div class="feature-item" v-for="feature in features" :key="feature.name">
        <label>{{ feature.label }}:</label>
        <span :class="{ 'success': feature.supported, 'error': !feature.supported }">
          {{ feature.supported ? '支持' : '不支持' }}
        </span>
      </div>
    </div>

    <div class="test-section">
      <h3>环境检测详情</h3>
      <div class="detection-details">
        <div class="detail-item">
          <label>AlipayJSBridge:</label>
          <span>{{ hasAlipayJSBridge ? '存在' : '不存在' }}</span>
        </div>
        <div class="detail-item">
          <label>在iframe中:</label>
          <span>{{ inIframe ? '是' : '否' }}</span>
        </div>
        <div class="detail-item">
          <label>URL参数检测:</label>
          <span>{{ urlParamsDetection }}</span>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>模拟测试</h3>
      <div class="simulation-buttons">
        <button @click="simulateThirdPartyApp" class="btn btn-primary">
          模拟第三方App环境
        </button>
        <button @click="simulateBrowser" class="btn btn-secondary">
          模拟普通浏览器
        </button>
        <button @click="refreshDetection" class="btn btn-info">
          重新检测
        </button>
      </div>
    </div>

    <div class="test-section">
      <h3>默认用户信息</h3>
      <div class="user-info">
        <div class="info-item">
          <label>手机号:</label>
          <span>{{ defaultUser.phone }}</span>
        </div>
        <div class="info-item">
          <label>用户名:</label>
          <span>{{ defaultUser.userName }}</span>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>测试日志</h3>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { 
  isInThirdPartyApp, 
  getEnvironmentType, 
  supportsThirdPartyFeature, 
  getDefaultUser 
} from '@/utils/environment'

const inThirdPartyApp = ref(false)
const environmentType = ref('')
const userAgent = ref('')
const hasAlipayJSBridge = ref(false)
const inIframe = ref(false)
const urlParamsDetection = ref('')
const defaultUser = ref({})
const logs = ref([])

const features = ref([
  { name: 'getAuthCode', label: '第三方授权', supported: false },
  { name: 'wechatPay', label: '微信支付', supported: false },
  { name: 'dingtalkAuth', label: '钉钉授权', supported: false }
])

const addLog = (message) => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message
  })
  if (logs.value.length > 20) {
    logs.value.pop()
  }
}

const detectEnvironment = () => {
  addLog('开始环境检测...')
  
  inThirdPartyApp.value = isInThirdPartyApp()
  environmentType.value = getEnvironmentType()
  userAgent.value = navigator.userAgent
  hasAlipayJSBridge.value = !!(window.AlipayJSBridge || window.parent.AlipayJSBridge)
  inIframe.value = window.self !== window.top
  defaultUser.value = getDefaultUser()
  
  // 检测URL参数
  const urlParams = new URLSearchParams(window.location.search)
  const appParams = ['from', 'source', 'app_source', 'channel']
  const detectedParams = []
  appParams.forEach(param => {
    const value = urlParams.get(param)
    if (value) {
      detectedParams.push(`${param}=${value}`)
    }
  })
  urlParamsDetection.value = detectedParams.length > 0 ? detectedParams.join(', ') : '无'
  
  // 检测功能支持
  features.value.forEach(feature => {
    feature.supported = supportsThirdPartyFeature(feature.name)
  })
  
  addLog(`环境检测完成: ${inThirdPartyApp.value ? '第三方App' : '普通浏览器'} (${environmentType.value})`)
}

const simulateThirdPartyApp = () => {
  addLog('模拟第三方App环境...')
  // 模拟添加AlipayJSBridge
  window.AlipayJSBridge = {
    call: (method, params, callback) => {
      addLog(`模拟调用: ${method}`)
      if (callback) {
        setTimeout(() => {
          callback({ code: 0, data: { authCode: 'mock_auth_code' } })
        }, 1000)
      }
    }
  }
  detectEnvironment()
}

const simulateBrowser = () => {
  addLog('模拟普通浏览器环境...')
  // 移除模拟的AlipayJSBridge
  delete window.AlipayJSBridge
  detectEnvironment()
}

const refreshDetection = () => {
  addLog('重新检测环境...')
  detectEnvironment()
}

onMounted(() => {
  detectEnvironment()
})
</script>

<style scoped>
.environment-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  font-family: Arial, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h2 {
  color: #333;
  border-bottom: 2px solid #3368C6;
  padding-bottom: 10px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #f9f9f9;
}

.test-section h3 {
  margin-top: 0;
  color: #3368C6;
  border-bottom: 1px solid #ddd;
  padding-bottom: 10px;
}

.info-item, .detail-item, .feature-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.info-item:last-child, .detail-item:last-child, .feature-item:last-child {
  border-bottom: none;
}

.info-item label, .detail-item label, .feature-item label {
  font-weight: bold;
  color: #555;
  min-width: 120px;
}

.success {
  color: #28a745;
  font-weight: bold;
}

.error {
  color: #dc3545;
  font-weight: bold;
}

.env-type {
  background: #3368C6;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.user-agent {
  font-size: 12px;
  color: #666;
  word-break: break-all;
  max-width: 400px;
}

.simulation-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.btn-primary {
  background: #3368C6;
  color: white;
}

.btn-primary:hover {
  background: #2856a3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover {
  background: #138496;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
}

.log-item {
  display: flex;
  margin-bottom: 5px;
  font-size: 12px;
}

.log-time {
  color: #6c757d;
  margin-right: 10px;
  min-width: 80px;
}

.log-message {
  color: #333;
}

.user-info {
  background: #e3f2fd;
  padding: 15px;
  border-radius: 5px;
}
</style>
