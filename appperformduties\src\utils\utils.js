export const alipayJSReady = callback => {
  if (window.parent.AlipayJSBridge) {
    window.AlipayJSBridge = window.parent.AlipayJSBridge
  }
  if (window.AlipayJSBridge) {
    callback && callback()
  } else {
    document.addEventListener('AlipayJSBridgeReady', callback, false)
  }
}
export const getAuthCode = (options, callback) => {
  alipayJSReady(() => {
    window.AlipayJSBridge.call('getAuthCode', { ...options }, result => {
      callback && callback(result)
    })
  })
}

export const decodeCharacter = (str) => {
  if (!str) return str
  // eslint-disable-next-line no-useless-escape
  return str.replace(/&amp;/g, "&").replace(/(&nbsp;|&ensp;|&emsp;|&#xa0;|&#160;)/g, ' ').replace(/&zwnj;/g, '').replace(/&mdash;/g, '—').replace(/&ldquo;/g, '“').replace(/&rsquo;/g, '’').replace(/&lsquo;/g, '‘').replace(/&rdquo;/g, '”').replace(/&middot;/g, '·').replace(/&hellip;/g, '…').replace(/&quot;/g, '"').replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&sup3;/g, '³').replace(/&times;/g, '×').replace(/&deg;/g, '°').replace(/&permil;/g, '‰');
}

export const removeTag = (str) => {
  if (!str) return str
  // eslint-disable-next-line no-useless-escape
  return decodeCharacter(str.replace(/<!--[\w\W\r\n]*?-->/gmi, '').replace(/(<[^\s\/>]+)\b[^>]*>/gi, '$1>').replace(/<[^>]+>/g, '').replace(/\s*/g, ''));
}