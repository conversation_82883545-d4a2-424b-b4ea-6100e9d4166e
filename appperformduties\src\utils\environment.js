/**
 * 环境检测工具函数
 * 用于判断当前应用是否运行在第三方app环境中
 */

/**
 * 检测是否在第三方app环境中
 * @returns {boolean} true表示在第三方app中，false表示不在
 */
export const isInThirdPartyApp = () => {
  // 方法1: 检测是否存在 AlipayJSBridge（支付宝相关app）
  if (window.AlipayJSBridge || window.parent.AlipayJSBridge) {
    console.log('环境检测: 发现 AlipayJSBridge，判断为第三方app环境')
    return true
  }

  // 方法2: 检测 User Agent 中是否包含特定标识
  const userAgent = navigator.userAgent.toLowerCase()
  const thirdPartyIdentifiers = [
    'alipay',
    'ixian',
    'i西安',
    'dingtalk'
  ]

  for (const identifier of thirdPartyIdentifiers) {
    if (userAgent.includes(identifier)) {
      console.log(`环境检测: User Agent 包含 "${identifier}"，判断为第三方app环境`)
      return true
    }
  }

  // 方法3: 检测是否在iframe中运行
  if (window.self !== window.top) {
    console.log('环境检测: 检测到iframe环境，判断为第三方app环境')
    return true
  }

  // 方法4: 检测URL参数中的特定标识
  const urlParams = new URLSearchParams(window.location.search)
  const appParams = ['from', 'source', 'app_source', 'channel']
  const appValues = ['app', 'ixian', 'i西安', 'alipay', 'third_party']

  for (const param of appParams) {
    const value = urlParams.get(param)
    if (value && appValues.some(appValue => value.toLowerCase().includes(appValue))) {
      console.log(`环境检测: URL参数 ${param}=${value}，判断为第三方app环境`)
      return true
    }
  }

  // 方法5: 检测特定的全局变量或API
  const thirdPartyAPIs = [
    'AlipayJSBridge',
    'dd', // 钉钉
    'wx', // 微信
    'webkit' // 某些app的webview
  ]

  for (const api of thirdPartyAPIs) {
    if (window[api]) {
      console.log(`环境检测: 发现全局API "${api}"，判断为第三方app环境`)
      return true
    }
  }

  console.log('环境检测: 未发现第三方app特征，判断为普通浏览器环境')
  return false
}

/**
 * 获取当前环境类型
 * @returns {string} 环境类型描述
 */
export const getEnvironmentType = () => {
  if (window.AlipayJSBridge || window.parent.AlipayJSBridge) {
    return 'alipay'
  }

  const userAgent = navigator.userAgent.toLowerCase()

  if (userAgent.includes('ixian') || userAgent.includes('i西安')) {
    return 'ixian'
  }

  if (userAgent.includes('micromessenger')) {
    return 'wechat'
  }

  if (userAgent.includes('dingtalk')) {
    return 'dingtalk'
  }

  if (window.self !== window.top) {
    return 'iframe'
  }

  return 'browser'
}

/**
 * 检测是否支持第三方app的特定功能
 * @param {string} feature 功能名称，如 'getAuthCode'
 * @returns {boolean} 是否支持该功能
 */
export const supportsThirdPartyFeature = (feature) => {
  switch (feature) {
    case 'getAuthCode':
      return !!(window.AlipayJSBridge || window.parent.AlipayJSBridge)
    case 'wechatPay':
      return !!(window.wx && window.wx.chooseWXPay)
    case 'dingtalkAuth':
      return !!(window.dd && window.dd.biz && window.dd.biz.user)
    default:
      return false
  }
}

/**
 * 等待第三方app环境准备就绪
 * @param {number} timeout 超时时间（毫秒）
 * @returns {Promise<boolean>} 是否准备就绪
 */
export const waitForThirdPartyReady = (timeout = 5000) => {
  return new Promise((resolve) => {
    let isResolved = false

    const checkReady = () => {
      if (isResolved) return

      if (isInThirdPartyApp()) {
        isResolved = true
        resolve(true)
      }
    }

    // 立即检查一次
    checkReady()

    // 如果还没准备好，设置定时器检查
    if (!isResolved) {
      const interval = setInterval(() => {
        checkReady()
        if (isResolved) {
          clearInterval(interval)
        }
      }, 100)

      // 设置超时
      setTimeout(() => {
        if (!isResolved) {
          isResolved = true
          clearInterval(interval)
          resolve(false)
        }
      }, timeout)
    }
  })
}

/**
 * 获取默认用户信息（用于非第三方app环境）
 * @returns {object} 默认用户信息
 */
export const getDefaultUser = () => {
  return {
    phone: '15175157513',
    userName: '管理员'
  }
}
