<template>
  <div class="archive-visit-booking">
    <!-- 选择入馆日期 -->
    <div class="section">
      <div class="section-header flex_box flex_justify_between">
        <div class="flex_box flex_align_center">
          <img src="../../assets/img/icon_museum.png " alt="" class="icon_museum">
          <div class="section-title">选择入馆日期</div>
        </div>
        <div class="year">2025年</div>
      </div>
      <div class="date-grid flex_box">
        <div class="date-item" v-for="date in dateList" :key="date.day"
          :class="{ active: date.active, disabled: date.disabled }" @click="selectDate(date)">
          <div class="day">{{ date.day }}</div>
          <div class="date">{{ date.date }}</div>
          <div class="status">{{ date.status }}</div>
        </div>
      </div>
    </div>

    <!-- 选择入馆时段 -->
    <div class="section">
      <div class="section-header flex_box flex_justify_between">
        <div class="flex_box flex_align_center">
          <img src="../../assets/img/icon_museum.png " alt="" class="icon_museum">
          <div class="section-title">选择入馆时段</div>
        </div>
      </div>
      <div class="time-slots">
        <div class="time-slot" v-for="slot in timeSlots" :key="slot.id" :class="{ active: slot.active }"
          @click="selectTimeSlot(slot)">
          <div class="time">{{ slot.time }}</div>
          <div class="capacity">{{ slot.status }} ({{ slot.count }}人)</div>
          <div v-if="slot.active" class="active-indicator"></div>
        </div>
      </div>
    </div>

    <!-- 预约人员信息 -->
    <div class="section">
      <div class="section-header flex_box flex_justify_between">
        <div class="flex_box flex_align_center">
          <span class="section-line"></span>
          <div class="section-title">预约人员信息</div>
        </div>
      </div>
      <van-cell-group inset class="form-group">
        <!-- 联系人 -->
        <van-field v-model="contactName" label="联系人" placeholder="请输入姓名" required label-width="80px"
          class="form-field" />

        <!-- 联系方式 -->
        <van-field v-model="contactPhone" label="联系方式" placeholder="请输入手机号" required label-width="80px"
          class="form-field" />

        <!-- 团体名称 -->
        <van-field v-model="groupName" label="团体名称" placeholder="请输入单位名称" required label-width="80px"
          class="form-field" />

        <!-- 团体性质 -->
        <van-field v-model="groupTypeText" label="团体性质" placeholder="请选择团体性质" readonly clickable required
          label-width="80px" class="form-field" @click="showGroupTypePicker = true" />

        <!-- 附件 -->
        <van-field label="附件" required label-width="80px" class="form-field upload-field">
          <template #input>
            <div class="upload-area">
              <van-uploader v-model="attachmentFiles" :max-count="3" :after-read="onAttachmentRead"
                :after-delete="onAttachmentDelete" accept="image/*" class="elegant-uploader" multiple
                :preview-size="100">
                <template #default>
                  <div class="upload-zone">
                    <div class="camera-icon">
                      <van-icon name="photograph" size="32" />
                    </div>
                    <div class="upload-text">点击上传图片</div>
                    <div class="upload-hint">支持JPG、PNG格式，最多3张</div>
                  </div>
                </template>
              </van-uploader>
            </div>
          </template>
        </van-field>
      </van-cell-group>
    </div>

    <!-- 底部按钮 -->
    <div class="submit-buttons">
      <div class="btn">提交预约</div>
      <div class="btn">我的预约</div>
    </div>

    <!-- 团体性质选择弹窗 -->
    <van-popup v-model:show="showGroupTypePicker" position="bottom">
      <van-picker :columns="groupTypeOptions" @confirm="onGroupTypeConfirm" @cancel="showGroupTypePicker = false" />
    </van-popup>
  </div>
</template>

<script>
export default { name: 'ArchiveVisitBooking' }
</script>

<script setup>
import { ref, onMounted } from 'vue'
import { showToast } from 'vant'

// 响应式数据
const contactName = ref('')
const contactPhone = ref('')
const groupName = ref('')
const groupType = ref('')
const groupTypeText = ref('')
const showGroupTypePicker = ref(false)

// 文件上传相关
const attachmentFiles = ref([])

// 团体性质选项
const groupTypeOptions = [
  { text: '机关单位', value: '机关单位' },
  { text: '企事业单位', value: '企事业单位' },
  { text: '学校', value: '学校' },
  { text: '社会团体', value: '社会团体' },
  { text: '其他', value: '其他' }
]

// 日期数据
const dateList = ref([
  { day: '二', date: '9-2', status: '可预约', active: false, disabled: false },
  { day: '三', date: '9-3', status: '可预约', active: false, disabled: false },
  { day: '四', date: '9-4', status: '可预约', active: false, disabled: false },
  { day: '五', date: '9-5', status: '可预约', active: true, disabled: false },
  { day: '六', date: '9-6', status: '可预约', active: false, disabled: false },
  { day: '日', date: '9-7', status: '可预约', active: false, disabled: false },
  { day: '一', date: '9-8', status: '可预约', active: false, disabled: true }
])

// 时段数据
const timeSlots = ref([
  { id: 1, time: '09:00-11:00', status: '可预约', count: 49, active: false },
  { id: 2, time: '13:30-16:30', status: '可预约', count: 160, active: true },
  { id: 3, time: '13:30-16:30', status: '可预约', count: 160, active: true },
  { id: 4, time: '13:30-16:30', status: '可预约', count: 160, active: true }
])

// 方法
const selectDate = (date) => {
  if (date.disabled) return
  dateList.value.forEach(item => item.active = false)
  date.active = true
}

const selectTimeSlot = (slot) => {
  timeSlots.value.forEach(item => item.active = false)
  slot.active = true
}

// 团体性质选择确认
const onGroupTypeConfirm = ({ selectedOptions }) => {
  groupType.value = selectedOptions[0].value
  groupTypeText.value = selectedOptions[0].text
  showGroupTypePicker.value = false
}

// 附件上传处理
const onAttachmentRead = (file) => {
  console.log('附件上传:', file)

  // 验证文件类型
  if (!file.file.type.startsWith('image/')) {
    showToast('请选择图片文件')
    return
  }

  // 验证文件大小 (限制为5MB)
  const maxSize = 5 * 1024 * 1024
  if (file.file.size > maxSize) {
    showToast('图片大小不能超过5MB')
    return
  }

  // 这里可以添加文件上传到服务器的逻辑
  // 例如：uploadFile(file.file)
  showToast('图片上传成功')
}

// 附件删除处理
const onAttachmentDelete = (file, detail) => {
  console.log('删除附件:', file, detail)
  showToast('图片已删除')
}

onMounted(() => {
  // 初始化逻辑
})
</script>

<style lang="scss" scoped>
.archive-visit-booking {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: #f9f9f9;
  padding: 10px 10px;

  .section {
    background: #fff;
    border-radius: 8px;
    margin-bottom: 12px;
    padding: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .section-header {
      margin-bottom: 15px;

      .icon_museum {
        width: 16px;
        height: 15px;
        margin-right: 8px;
      }

      .section-line {
        width: 4px;
        height: 16px;
        background: #A54E3B;
        margin-right: 8px;
      }

      .section-title {
        font-size: 15px;
        font-weight: bold;
        color: #333;
      }

      .year {
        font-size: 14px;
        color: #666;
        font-weight: normal;
      }
    }

    // 日期选择样式
    .date-grid {
      gap: 6px;
      overflow-x: auto;

      .date-item {
        flex: 1;
        max-width: 70px;
        height: 70px;
        background: #f8f8f8;
        border-radius: 4px;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-evenly;

        .day {
          font-size: 12px;
          color: #666;
          margin-bottom: 3px;
          line-height: 1;
          font-weight: normal;
        }

        .date {
          font-size: 13px;
          font-weight: 500;
          color: #333;
          margin-bottom: 3px;
          line-height: 1;
        }

        .status {
          font-size: 10px;
          color: #999;
          line-height: 1;
          font-weight: normal;
        }

        &.active {
          background: #fff3e0;
          border-color: #ff6b35;

          .day,
          .date {
            color: #ff6b35;
          }

          .status {
            color: #ff6b35;
          }
        }

        &.disabled {
          opacity: 0.4;
          background: #f5f5f5;

          .day,
          .date,
          .status {
            color: #ccc;
          }
        }

        &:hover:not(.disabled):not(.active) {
          background: #f0f0f0;
          border-color: #e0e0e0;
        }
      }
    }

    // 时段选择样式
    .time-slots {
      display: flex;
      gap: 8px;

      .time-slot {
        flex: 1;
        background: #f5f5f5;
        border-radius: 6px;
        padding: 12px 8px;
        transition: all 0.3s ease;
        border: 1px solid transparent;
        position: relative;
        text-align: center;

        .time {
          font-size: 14px;
          font-weight: 500;
          color: #333;
          margin-bottom: 2px;
          line-height: 1.2;
        }

        .capacity {
          font-size: 12px;
          color: #666;
          line-height: 1.2;
        }

        .active-indicator {
          position: absolute;
          top: 4px;
          right: 4px;
          width: 6px;
          height: 6px;
          background: #ff4444;
          border-radius: 50%;
        }

        &.active {
          background: #fff3e0;
          border-color: #ff9800;

          .time {
            color: #ff6b35;
          }

          .capacity {
            color: #ff6b35;
          }
        }

        &:hover:not(.active) {
          background: #eeeeee;
        }
      }
    }

    // Vant表单组件样式
    .form-group {
      margin: 12px 4px 0 4px;
      border-radius: 8px;
      overflow: hidden;

      .van-cell {
        padding: 10px 0 10px 0;
        background: white;

        &::after {
          border-bottom: 1px solid #f0f0f0;
        }

        &:last-child::after {
          border-bottom: none;
        }
      }

      :deep(.van-field__label) {
        color: #333;
        font-size: 14px;
        font-weight: 500;
        width: 80px;
        flex-shrink: 0;

        &::before {
          content: '*';
          color: #ff4444;
          margin-right: 4px;
        }
      }

      :deep(.van-field__control) {
        font-size: 14px;
        color: #333;

        &::placeholder {
          color: #999;
        }
      }

      // 上传字段样式
      .upload-field {
        :deep(.van-field__control) {
          padding: 0;
        }

        .upload-area {
          width: 100%;

          .elegant-uploader {
            :deep(.van-uploader__wrapper) {
              display: flex;
              flex-wrap: wrap;
              gap: 10px;
              align-items: flex-start;
            }

            // 图片预览样式
            :deep(.van-uploader__preview) {
              position: relative;
              border-radius: 10px;
              overflow: hidden;
              box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
              transition: all 0.3s ease;

              &:hover {
                transform: translateY(-3px);
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
              }

              .van-uploader__preview-image {
                border-radius: 10px;
                object-fit: cover;
              }

              .van-uploader__preview-delete {
                position: absolute;
                top: 6px;
                right: 6px;
                width: 22px;
                height: 22px;
                background: #ff4757;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.2s ease;
                box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3);

                &:hover {
                  background: #ff3742;
                  transform: scale(1.1);
                  box-shadow: 0 4px 12px rgba(255, 71, 87, 0.4);
                }

                .van-icon {
                  color: white;
                  font-size: 12px;
                }
              }
            }

            // 上传区域样式
            :deep(.van-uploader__upload) {
              margin: 0;
              width: 100%;

              .upload-zone {
                width: 100%;
                height: 140px;
                border: 2px dashed #d1d5db;
                border-radius: 16px;
                background: #f9fafb;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
                position: relative;

                .camera-icon {
                  width: 60px;
                  height: 60px;
                  border-radius: 50%;
                  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin-bottom: 12px;
                  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
                  transition: all 0.3s ease;

                  .van-icon {
                    color: white;
                  }
                }

                .upload-text {
                  font-size: 12px;
                  font-weight: 500;
                  color: #374151;
                  margin-bottom: 4px;
                  transition: color 0.3s ease;
                }

                .upload-hint {
                  font-size: 12px;
                  color: #9ca3af;
                  text-align: center;
                  line-height: 1.4;
                }

                &:hover {
                  border-color: #667eea;
                  background: #f0f4ff;
                  transform: translateY(-2px);
                  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);

                  .camera-icon {
                    transform: scale(1.1);
                    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
                  }

                  .upload-text {
                    color: #667eea;
                  }
                }

                &:active {
                  transform: translateY(0);
                }
              }
            }
          }
        }
      }
    }

    .counter {
      display: flex;
      align-items: center;
      gap: 16px;

      .counter-btn {
        width: 32px;
        height: 32px;
        border: 1px solid #ddd;
        border-radius: 6px;
        background: white;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 18px;
        color: #666;
        transition: all 0.3s ease;

        &:hover {
          border-color: #ff6b35;
          color: #ff6b35;
        }

        &:active {
          background: #f0f0f0;
        }
      }

      .count {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        min-width: 24px;
        text-align: center;
      }
    }
  }
}

// 底部操作区域
.submit-buttons {
  display: flex;
  justify-content: space-around;
  align-items: center;

  .btn {
    color: #ffffff;
    background-color: #e1b97b;
    font-weight: normal;
    border-radius: 30px;
    padding: 10px 0;
    width: 46%;
    text-align: center;
    font-size: 14px;
  }
}
</style>