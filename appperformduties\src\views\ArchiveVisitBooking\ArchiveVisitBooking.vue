<template>
  <div class="archive-visit-booking">
    <!-- 选择入馆日期 -->
    <div class="section">
      <div class="section-header flex_box flex_justify_between">
        <div class="flex_box flex_align_center">
          <img src="../../assets/img/icon_museum.png " alt="" class="icon_museum">
          <div class="section-title">选择入馆日期</div>
        </div>
        <div class="year">2025年</div>
      </div>
      <div class="date-grid flex_box">
        <div class="date-item" v-for="date in dateList" :key="date.day"
          :class="{ active: date.active, disabled: date.disabled }" @click="selectDate(date)">
          <div class="day">{{ date.day }}</div>
          <div class="date">{{ date.date }}</div>
          <div class="status">{{ date.status }}</div>
        </div>
      </div>
    </div>

    <!-- 选择入馆时段 -->
    <div class="section">
      <div class="section-header flex_box flex_justify_between">
        <div class="flex_box flex_align_center">
          <img src="../../assets/img/icon_museum.png " alt="" class="icon_museum">
          <div class="section-title">选择入馆时段</div>
        </div>
      </div>
      <div class="time-slots">
        <div class="time-slot" v-for="slot in timeSlots" :key="slot.id" :class="{ active: slot.active }"
          @click="selectTimeSlot(slot)">
          <div class="time">{{ slot.time }}</div>
          <div class="capacity">{{ slot.status }} ({{ slot.count }}人)</div>
          <div v-if="slot.active" class="active-indicator"></div>
        </div>
      </div>
    </div>

    <!-- 预约人员信息 -->
    <div class="section">
      <div class="section-header flex_box flex_justify_between">
        <div class="flex_box flex_align_center">
          <span class="section-line"></span>
          <div class="section-title">预约人员信息</div>
        </div>
      </div>
      <van-cell-group inset class="form-group">
        <!-- 联系人 -->
        <van-field v-model="contactName" label="联系人" placeholder="请输入姓名" required label-width="80px"
          class="form-field" />

        <!-- 联系方式 -->
        <van-field v-model="contactPhone" label="联系方式" placeholder="请输入手机号" required label-width="80px"
          class="form-field" />

        <!-- 团体名称 -->
        <van-field v-model="groupName" label="团体名称" placeholder="请输入单位名称" required label-width="80px"
          class="form-field" />

        <!-- 团体性质 -->
        <van-field v-model="groupTypeText" label="团体性质" placeholder="请选择团体性质" readonly clickable required
          label-width="80px" class="form-field" @click="showGroupTypePicker = true" />

        <!-- 人员名单 -->
        <van-field label="人员名单" required label-width="80px" class="form-field upload-field">
          <template #input>
            <div class="upload-container">
              <div class="upload-actions">
                <van-uploader v-model="personnelList" :max-count="1" :after-read="onPersonnelListRead"
                  :accept="personnelAcceptTypes" class="custom-uploader" :show-upload="personnelList.length === 0">
                  <div class="upload-icon-btn">
                    <van-icon name="plus" size="16" />
                    <span>上传附件</span>
                  </div>
                </van-uploader>
                <div class="download-icon-btn" @click="downloadPersonnelTemplate">
                  <van-icon name="down" size="16" />
                  <span>下载模板</span>
                </div>
              </div>
            </div>
          </template>
        </van-field>

        <!-- 单位证明材料 -->
        <van-field label="单位证明" required label-width="80px" class="form-field upload-field">
          <template #input>
            <div class="upload-container">
              <van-uploader v-model="unitProofFiles" :max-count="3" :after-read="onUnitProofRead"
                :accept="unitProofAcceptTypes" class="custom-uploader" multiple>
                <div class="upload-icon-btn">
                  <van-icon name="plus" size="16" />
                  <span>上传附件</span>
                </div>
              </van-uploader>
            </div>
          </template>
        </van-field>
      </van-cell-group>
    </div>

    <!-- 底部按钮 -->
    <div class="submit-buttons">
      <div class="btn">提交预约</div>
      <div class="btn">我的预约</div>
    </div>

    <!-- 团体性质选择弹窗 -->
    <van-popup v-model:show="showGroupTypePicker" position="bottom">
      <van-picker :columns="groupTypeOptions" @confirm="onGroupTypeConfirm" @cancel="showGroupTypePicker = false" />
    </van-popup>
  </div>
</template>

<script>
export default { name: 'ArchiveVisitBooking' }
</script>

<script setup>
import { ref, onMounted } from 'vue'
import { showToast } from 'vant'

// 响应式数据
const contactName = ref('')
const contactPhone = ref('')
const groupName = ref('')
const groupType = ref('')
const groupTypeText = ref('')
const showGroupTypePicker = ref(false)

// 文件上传相关
const personnelList = ref([])
const unitProofFiles = ref([])

// 文件类型定义 - 兼容安卓和苹果
const personnelAcceptTypes = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/pdf,image/*'
const unitProofAcceptTypes = 'image/*,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document'

// 团体性质选项
const groupTypeOptions = [
  { text: '机关单位', value: '机关单位' },
  { text: '企事业单位', value: '企事业单位' },
  { text: '学校', value: '学校' },
  { text: '社会团体', value: '社会团体' },
  { text: '其他', value: '其他' }
]

// 日期数据
const dateList = ref([
  { day: '二', date: '9-2', status: '可预约', active: false, disabled: false },
  { day: '三', date: '9-3', status: '可预约', active: false, disabled: false },
  { day: '四', date: '9-4', status: '可预约', active: false, disabled: false },
  { day: '五', date: '9-5', status: '可预约', active: true, disabled: false },
  { day: '六', date: '9-6', status: '可预约', active: false, disabled: false },
  { day: '日', date: '9-7', status: '可预约', active: false, disabled: false },
  { day: '一', date: '9-8', status: '可预约', active: false, disabled: true }
])

// 时段数据
const timeSlots = ref([
  { id: 1, time: '09:00-11:00', status: '可预约', count: 49, active: false },
  { id: 2, time: '13:30-16:30', status: '可预约', count: 160, active: true },
  { id: 3, time: '13:30-16:30', status: '可预约', count: 160, active: true },
  { id: 4, time: '13:30-16:30', status: '可预约', count: 160, active: true }
])

// 方法
const selectDate = (date) => {
  if (date.disabled) return
  dateList.value.forEach(item => item.active = false)
  date.active = true
}

const selectTimeSlot = (slot) => {
  timeSlots.value.forEach(item => item.active = false)
  slot.active = true
}

// 团体性质选择确认
const onGroupTypeConfirm = ({ selectedOptions }) => {
  groupType.value = selectedOptions[0].value
  groupTypeText.value = selectedOptions[0].text
  showGroupTypePicker.value = false
}

// 人员名单文件上传
const onPersonnelListRead = (file) => {
  console.log('人员名单文件上传:', file)
  // 这里可以添加文件上传到服务器的逻辑
  // 例如：uploadFile(file.file)
}

// 单位证明材料文件上传
const onUnitProofRead = (file) => {
  console.log('单位证明材料上传:', file)
  // 这里可以添加文件上传到服务器的逻辑
  // 例如：uploadFile(file.file)
}

// 下载人员名单模板 - 兼容移动端
const downloadPersonnelTemplate = async () => {
  try {
    // 检测设备类型
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent)
    const isAndroid = /Android/.test(navigator.userAgent)

    // 模板文件URL - 这里应该是实际的服务器文件地址
    const templateUrl = '/api/download/personnel-template' // 实际API地址

    if (isIOS || isAndroid) {
      // 移动端使用window.open在新窗口打开下载
      window.open(templateUrl, '_blank')
    } else {
      // 桌面端使用传统下载方式
      const link = document.createElement('a')
      link.href = templateUrl
      link.download = '人员名单模板.xlsx'
      link.target = '_blank'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }

    // 显示提示信息
    showToast('模板下载中，请稍候...')
  } catch (error) {
    console.error('下载模板失败:', error)
    showToast('下载失败，请重试')
  }
}

onMounted(() => {
  // 初始化逻辑
})
</script>

<style lang="scss" scoped>
.archive-visit-booking {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: #f9f9f9;
  padding: 10px 10px;

  .section {
    background: #fff;
    border-radius: 8px;
    margin-bottom: 12px;
    padding: 10px 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .section-header {
      margin-bottom: 15px;

      .icon_museum {
        width: 16px;
        height: 15px;
        margin-right: 8px;
      }

      .section-line {
        width: 3px;
        height: 16px;
        background: #A54E3B;
        margin-right: 8px;
      }

      .section-title {
        font-size: 15px;
        font-weight: bold;
        color: #333;
      }

      .year {
        font-size: 14px;
        color: #666;
        font-weight: normal;
      }
    }

    // 日期选择样式
    .date-grid {
      gap: 6px;
      overflow-x: auto;

      .date-item {
        flex: 1;
        max-width: 70px;
        height: 70px;
        background: #f8f8f8;
        border-radius: 4px;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-evenly;

        .day {
          font-size: 12px;
          color: #666;
          margin-bottom: 3px;
          line-height: 1;
          font-weight: normal;
        }

        .date {
          font-size: 13px;
          font-weight: 500;
          color: #333;
          margin-bottom: 3px;
          line-height: 1;
        }

        .status {
          font-size: 10px;
          color: #999;
          line-height: 1;
          font-weight: normal;
        }

        &.active {
          background: #fff3e0;
          border-color: #ff6b35;

          .day,
          .date {
            color: #ff6b35;
          }

          .status {
            color: #ff6b35;
          }
        }

        &.disabled {
          opacity: 0.4;
          background: #f5f5f5;

          .day,
          .date,
          .status {
            color: #ccc;
          }
        }

        &:hover:not(.disabled):not(.active) {
          background: #f0f0f0;
          border-color: #e0e0e0;
        }
      }
    }

    // 时段选择样式
    .time-slots {
      display: flex;
      gap: 8px;

      .time-slot {
        flex: 1;
        background: #f5f5f5;
        border-radius: 6px;
        padding: 12px 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid transparent;
        position: relative;
        text-align: center;

        .time {
          font-size: 14px;
          font-weight: 500;
          color: #333;
          margin-bottom: 2px;
          line-height: 1.2;
        }

        .capacity {
          font-size: 12px;
          color: #666;
          line-height: 1.2;
        }

        .active-indicator {
          position: absolute;
          top: 4px;
          right: 4px;
          width: 6px;
          height: 6px;
          background: #ff4444;
          border-radius: 50%;
        }

        &.active {
          background: #fff3e0;
          border-color: #ff9800;

          .time {
            color: #ff6b35;
          }

          .capacity {
            color: #ff6b35;
          }
        }

        &:hover:not(.active) {
          background: #eeeeee;
        }
      }
    }

    // Vant表单组件样式
    .form-group {
      margin-top: 12px;
      border-radius: 8px;
      overflow: hidden;

      .van-cell {
        padding: 12px 0;
        background: white;

        &::after {
          border-bottom: 1px solid #f0f0f0;
        }

        &:last-child::after {
          border-bottom: none;
        }
      }

      :deep(.van-field__label) {
        color: #333;
        font-size: 14px;
        font-weight: 500;
        width: 80px;
        flex-shrink: 0;

        &::before {
          content: '*';
          color: #ff4444;
          margin-right: 4px;
        }
      }

      :deep(.van-field__control) {
        font-size: 14px;
        color: #333;

        &::placeholder {
          color: #999;
        }
      }

      // 上传字段样式
      .upload-field {
        :deep(.van-field__control) {
          padding: 0;
        }

        .upload-container {
          display: flex;
          align-items: center;
          width: 100%;

          .upload-actions {
            display: flex;
            align-items: center;
            gap: 12px;
            width: 100%;
          }

          .custom-uploader {
            flex: 1;

            :deep(.van-uploader__wrapper) {
              display: flex;
              align-items: center;
              gap: 8px;
            }

            :deep(.van-uploader__preview) {
              margin: 0 8px 0 0;
            }

            :deep(.van-uploader__upload) {
              margin: 0;
            }
          }

          .upload-icon-btn,
          .download-icon-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 8px 12px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 60px;

            span {
              font-size: 12px;
              color: #666;
              margin-top: 4px;
              white-space: nowrap;
            }

            &:hover {
              border-color: #ff6b35;
              background: #fff9f5;

              span {
                color: #ff6b35;
              }
            }

            &:active {
              transform: scale(0.98);
            }
          }

          .upload-icon-btn {
            border-color: #ff6b35;
            color: #ff6b35;

            span {
              color: #ff6b35;
            }
          }

          .download-icon-btn {
            flex-shrink: 0;
            color: #666;
          }
        }
      }
    }

    .counter {
      display: flex;
      align-items: center;
      gap: 16px;

      .counter-btn {
        width: 32px;
        height: 32px;
        border: 1px solid #ddd;
        border-radius: 6px;
        background: white;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 18px;
        color: #666;
        transition: all 0.3s ease;

        &:hover {
          border-color: #ff6b35;
          color: #ff6b35;
        }

        &:active {
          background: #f0f0f0;
        }
      }

      .count {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        min-width: 24px;
        text-align: center;
      }
    }
  }
}

// 底部操作区域
.submit-buttons {
  display: flex;
  justify-content: space-around;
  align-items: center;

  .btn {
    color: #ffffff;
    background-color: #e1b97b;
    font-weight: normal;
    border-radius: 30px;
    padding: 10px 0;
    width: 46%;
    text-align: center;
    font-size: 14px;
  }
}
</style>