<template>
  <div class="archive-visit-booking">
    <!-- 选择入馆日期 -->
    <div class="section">
      <div class="section-header flex_box flex_justify_between">
        <div class="flex_box flex_align_center">
          <img src="../../assets/img/icon_museum.png " alt="" class="icon_museum">
          <div class="section-title">选择入馆日期</div>
        </div>
        <div class="year">2025年</div>
      </div>
      <div class="date-grid flex_box">
        <div class="date-item" v-for="date in dateList" :key="date.day"
          :class="{ active: date.active, disabled: date.disabled }" @click="selectDate(date)">
          <div class="day">{{ date.day }}</div>
          <div class="date">{{ date.date }}</div>
          <div class="status">{{ date.status }}</div>
        </div>
      </div>
    </div>

    <!-- 选择入馆时段 -->
    <div class="section">
      <div class="section-header flex_box flex_justify_between">
        <div class="flex_box flex_align_center">
          <img src="../../assets/img/icon_museum.png " alt="" class="icon_museum">
          <div class="section-title">选择入馆时段</div>
        </div>
      </div>
      <div class="time-slots">
        <div class="time-slot" v-for="slot in timeSlots" :key="slot.id" :class="{ active: slot.active }"
          @click="selectTimeSlot(slot)">
          <div class="time">{{ slot.time }}</div>
          <div class="capacity">{{ slot.status }} ({{ slot.count }}人)</div>
          <div v-if="slot.active" class="active-indicator"></div>
        </div>
      </div>
    </div>

    <!-- 预约人员信息 -->
    <div class="section">
      <div class="section-header flex_box flex_justify_between">
        <div class="flex_box flex_align_center">
          <span class="section-line"></span>
          <div class="section-title">预约人员信息</div>
        </div>
      </div>
      <!-- 联系人 -->
      <div class="form-item-row">
        <label class="label">联系人</label>
        <input type="text" class="input" placeholder="请输入姓名" v-model="contactName">
      </div>

      <!-- 联系方式 -->
      <div class="form-item-row">
        <label class="label">联系方式</label>
        <input type="text" class="input" placeholder="请输入手机号" v-model="contactPhone">
      </div>

      <!-- 团体名称 -->
      <div class="form-item-row">
        <label class="label">团体名称</label>
        <input type="text" class="input" placeholder="请输入单位名称" v-model="groupName">
      </div>

      <!-- 单位地址 -->
      <div class="form-item-row">
        <label class="label">团体性质</label>
        <input type="text" class="input" placeholder="请选择单位地址" v-model="groupAddress">
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-actions">
      <div class="action-sections">
        <!-- 人员名单行 -->
        <div class="action-row">
          <div class="action-item">
            <span class="icon">�</span>
            <span class="text">人员名单</span>
          </div>
          <div class="action-item">
            <span class="icon">📋</span>
            <span class="text">*单位说明</span>
          </div>
        </div>
        <!-- 单位证明行 -->
        <div class="action-row">
          <div class="action-item full-width">
            <span class="icon">📎</span>
            <span class="text">*单位证明</span>
          </div>
        </div>
      </div>
      <div class="submit-buttons">
        <button class="btn btn-secondary">提交预约</button>
        <button class="btn btn-primary">我的预约</button>
      </div>
    </div>
  </div>
</template>

<script>
export default { name: 'ArchiveVisitBooking' }
</script>

<script setup>
import { ref, onMounted } from 'vue'

// 响应式数据
const contactName = ref('')
const contactPhone = ref('')
const groupName = ref('')
const groupAddress = ref('')

// 日期数据
const dateList = ref([
  { day: '二', date: '9-2', status: '可预约', active: false, disabled: false },
  { day: '三', date: '9-3', status: '可预约', active: false, disabled: false },
  { day: '四', date: '9-4', status: '可预约', active: false, disabled: false },
  { day: '五', date: '9-5', status: '可预约', active: true, disabled: false },
  { day: '六', date: '9-6', status: '可预约', active: false, disabled: false },
  { day: '日', date: '9-7', status: '可预约', active: false, disabled: false },
  { day: '一', date: '9-8', status: '可预约', active: false, disabled: true }
])

// 时段数据
const timeSlots = ref([
  { id: 1, time: '09:00-11:00', status: '可预约', count: 49, active: false },
  { id: 2, time: '13:30-16:30', status: '可预约', count: 160, active: true },
  { id: 3, time: '13:30-16:30', status: '可预约', count: 160, active: true },
  { id: 4, time: '13:30-16:30', status: '可预约', count: 160, active: true }
])

// 方法
const selectDate = (date) => {
  if (date.disabled) return
  dateList.value.forEach(item => item.active = false)
  date.active = true
}

const selectTimeSlot = (slot) => {
  timeSlots.value.forEach(item => item.active = false)
  slot.active = true
}

onMounted(() => {
  // 初始化逻辑
})
</script>

<style lang="scss" scoped>
.archive-visit-booking {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: #f9f9f9;
  padding: 10px 10px;

  .section {
    background: #fff;
    border-radius: 8px;
    margin-bottom: 12px;
    padding: 10px 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .section-header {
      margin-bottom: 12px;

      .icon_museum {
        width: 16px;
        height: 15px;
        margin-right: 8px;
      }

      .section-line {
        width: 3px;
        height: 16px;
        background: #A54E3B;
        margin-right: 8px;
      }

      .section-title {
        font-size: 15px;
        font-weight: bold;
        color: #333;
      }

      .year {
        font-size: 14px;
        color: #666;
        font-weight: normal;
      }
    }

    // 日期选择样式
    .date-grid {
      gap: 6px;
      overflow-x: auto;

      .date-item {
        flex: 1;
        max-width: 70px;
        height: 70px;
        background: #f8f8f8;
        border-radius: 4px;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-evenly;

        .day {
          font-size: 12px;
          color: #666;
          margin-bottom: 3px;
          line-height: 1;
          font-weight: normal;
        }

        .date {
          font-size: 13px;
          font-weight: 500;
          color: #333;
          margin-bottom: 3px;
          line-height: 1;
        }

        .status {
          font-size: 10px;
          color: #999;
          line-height: 1;
          font-weight: normal;
        }

        &.active {
          background: #fff3e0;
          border-color: #ff6b35;

          .day,
          .date {
            color: #ff6b35;
          }

          .status {
            color: #ff6b35;
          }
        }

        &.disabled {
          opacity: 0.4;
          background: #f5f5f5;

          .day,
          .date,
          .status {
            color: #ccc;
          }
        }

        &:hover:not(.disabled):not(.active) {
          background: #f0f0f0;
          border-color: #e0e0e0;
        }
      }
    }

    // 时段选择样式
    .time-slots {
      display: flex;
      gap: 8px;

      .time-slot {
        flex: 1;
        background: #f5f5f5;
        border-radius: 6px;
        padding: 12px 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid transparent;
        position: relative;
        text-align: center;

        .time {
          font-size: 14px;
          font-weight: 500;
          color: #333;
          margin-bottom: 2px;
          line-height: 1.2;
        }

        .capacity {
          font-size: 12px;
          color: #666;
          line-height: 1.2;
        }

        .active-indicator {
          position: absolute;
          top: 4px;
          right: 4px;
          width: 6px;
          height: 6px;
          background: #ff4444;
          border-radius: 50%;
        }

        &.active {
          background: #fff3e0;
          border-color: #ff9800;

          .time {
            color: #ff6b35;
          }

          .capacity {
            color: #ff6b35;
          }
        }

        &:hover:not(.active) {
          background: #eeeeee;
        }
      }
    }

    // 表单样式
    .form-item {
      margin-bottom: 16px;

      .label {
        display: block;
        font-size: 14px;
        color: #333;
        margin-bottom: 8px;
        font-weight: 500;

        &::before {
          content: '*';
          color: #ff6b35;
          margin-right: 4px;
        }
      }
    }

    // 表单行样式（横向布局）
    .form-item-row {
      display: flex;
      align-items: center;
      margin: 12px 8px;
      gap: 12px;

      .label {
        min-width: 80px;
        font-size: 14px;
        color: #333;
        font-weight: 500;
        margin: 0;

        // 红色星号样式
        &::before {
          content: '*';
          color: #ff4444;
          margin-right: 4px;
        }
      }

      .input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 14px;
        background: white;
        transition: border-color 0.3s ease;
        height: 36px;
        box-sizing: border-box;

        &:focus {
          outline: none;
          border-color: #ff6b35;
        }

        &::placeholder {
          color: #999;
        }
      }

      .counter {
        display: flex;
        align-items: center;
        gap: 16px;

        .counter-btn {
          width: 32px;
          height: 32px;
          border: 1px solid #ddd;
          border-radius: 6px;
          background: white;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          font-size: 18px;
          color: #666;
          transition: all 0.3s ease;

          &:hover {
            border-color: #ff6b35;
            color: #ff6b35;
          }

          &:active {
            background: #f0f0f0;
          }
        }

        .count {
          font-size: 18px;
          font-weight: 600;
          color: #333;
          min-width: 24px;
          text-align: center;
        }
      }
    }
  }

  // 底部操作区域
  .bottom-actions {
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .action-sections {
      margin-bottom: 20px;
      padding-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;

      .action-row {
        display: flex;
        gap: 12px;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .action-item {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          padding: 12px 16px;
          background: #f8f8f8;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.3s ease;
          border: 1px solid #e0e0e0;

          &.full-width {
            flex: 1;
          }

          .icon {
            font-size: 16px;
            color: #ff6b35;
          }

          .text {
            font-size: 14px;
            color: #333;
            font-weight: 500;
          }

          &:hover {
            background: #f0f0f0;
            border-color: #ff6b35;

            .text {
              color: #ff6b35;
            }
          }
        }
      }
    }

    .submit-buttons {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;

      .btn {
        padding: 14px 24px;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;

        &.btn-secondary {
          background: #f8f8f8;
          color: #666;

          &:hover {
            background: #e8e8e8;
          }
        }

        &.btn-primary {
          background: #ff6b35;
          color: white;

          &:hover {
            background: #e55a2b;
          }
        }

        &:active {
          transform: translateY(1px);
        }
      }
    }
  }
}

// 响应式设计
// @media (max-width: 768px) {
//   .archive-visit-booking {
//     padding: 12px;

//     .section {
//       padding: 12px;

//       .date-grid {
//         grid-template-columns: repeat(3, 1fr);
//       }

//       .time-slots {
//         grid-template-columns: 1fr;
//       }
//     }
//   }
// }</style>