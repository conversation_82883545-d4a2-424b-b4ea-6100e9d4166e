<template>
  <div class="LoginView">
    <div class="login-bg"></div>
    <div class="login-container">
      <div class="login-title">系统登录</div>
      <van-form @submit="onSubmit">
        <van-cell-group inset>
          <van-field v-model="username" name="username" placeholder="请输入用户名"
            :rules="[{ required: true, message: '请填写用户名' }]" />
          <van-field v-model="password" type="password" name="password" placeholder="请输入密码"
            :rules="[{ required: true, message: '请填写密码' }]" />
        </van-cell-group>
        <van-button round block type="primary" native-type="submit" class="login-btn">
          登录
        </van-button>
      </van-form>
    </div>
  </div>
</template>
<script>
export default { name: 'LoginView' }
</script>
<script setup>
import { useRouter } from 'vue-router'
import { ref, onMounted } from 'vue'
import api from '@/api'
import store from '@/store'
import utils from '@/assets/js/utils'
import { showToast } from 'vant'
const router = useRouter()
const username = ref('')
const password = ref('')
onMounted(() => {
  if (sessionStorage.getItem('token')) {
    router.push({ path: '/', query: {} })
  }
})
const onSubmit = (values) => {
  console.log('submit', values)
  login()
}
const login = async () => {
  // 管理员 dc_admin
  try {
    const { data } = await api.login({
      grant_type: 'password',
      username: username.value,
      password: utils.encrypt(password.value, new Date().getTime(), '1')
    });
    console.log('成功:', data)
    sessionStorage.setItem('token', data.token)
    sessionStorage.setItem('refresh_token', `bearer ${data.refreshToken.value}`)
    sessionStorage.setItem('expires', data.expires_in)
    sessionStorage.setItem('expiration', data.refreshToken.expiration)
    sessionStorage.setItem('verify', data.passwordElementMatchReg ? 1 : 0)
    store.dispatch('loginUser')
    setTimeout(() => {
      router.push({ path: '/LayoutHome', query: {} })
    }, 500);
  } catch (error) {
    console.log('失败:', error)
    showToast(error.message || '登录失败')
  }
}
</script>
<style lang="scss">
.LoginView {
  width: 100vw;
  height: 100vh;
  min-height: 100vh;
  position: relative;
  background: #f5f6fa;
  display: flex;
  align-items: center;
  justify-content: center;

  .login-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #6a82fb 0%, #fc5c7d 100%);
    z-index: 1;
    opacity: 0.2;
  }

  .login-container {
    position: relative;
    z-index: 2;
    width: 350px;
    padding: 40px 30px 32px 30px;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .login-logo {
    width: 64px;
    height: 64px;
    margin-bottom: 16px;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    background: #f5f6fa;
    object-fit: cover;
  }

  .login-title {
    font-size: 22px;
    font-weight: 600;
    color: #333;
    margin-bottom: 28px;
    letter-spacing: 2px;
  }

  .van-cell-group {
    width: 100%;
    margin-bottom: 18px;
  }

  .login-btn {
    margin-top: 8px;
    font-size: 16px;
    height: 44px;
    background: linear-gradient(90deg, #6a82fb 0%, #fc5c7d 100%);
    border: none;
  }
}
</style>
