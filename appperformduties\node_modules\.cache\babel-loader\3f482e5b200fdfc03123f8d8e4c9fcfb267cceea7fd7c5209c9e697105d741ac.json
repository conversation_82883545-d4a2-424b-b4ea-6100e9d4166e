{"ast": null, "code": "import { createRouter, createWebHashHistory } from 'vue-router';\nvar LoginView = function LoginView() {\n  return import('@/views/LoginView/LoginView.vue');\n};\nvar LayoutHome = function LayoutHome() {\n  return import('@/views/LayoutHome/LayoutHome.vue');\n};\nvar NewList = function NewList() {\n  return import('@/views/NewList/NewList.vue');\n}; // 新闻资讯列表\nvar NewDetails = function NewDetails() {\n  return import('@/views/NewList/NewDetails.vue');\n}; // 新闻资讯详情\nvar NegotiationActivityPage = function NegotiationActivityPage() {\n  return import('@/views/NegotiationActivityPage/NegotiationActivityPage.vue');\n}; // 协商活动\nvar NetworkDiscussList = function NetworkDiscussList() {\n  return import('@/views/NetworkDiscussList/NetworkDiscussList.vue');\n}; // 网络议政列表\nvar NetworkDiscussDetails = function NetworkDiscussDetails() {\n  return import('@/views/NetworkDiscussList/NetworkDiscussDetails.vue');\n}; // 网络议政详情\nvar NegotiationTopicsDetails = function NegotiationTopicsDetails() {\n  return import('@/views/NetworkDiscussList/NegotiationTopicsDetails.vue');\n}; // 协商议题征集详情\nvar QuestionnaireList = function QuestionnaireList() {\n  return import('@/views/QuestionnaireList/QuestionnaireList.vue');\n}; // 问卷调查列表\nvar QuestionnaireDetails = function QuestionnaireDetails() {\n  return import('@/views/QuestionnaireList/QuestionnaireDetails.vue');\n}; // 问卷调查详情\nvar CommitteeLivingRoomList = function CommitteeLivingRoomList() {\n  return import('@/views/CommitteeLivingRoom/CommitteeLivingRoomList.vue');\n}; // 委员会客厅列表\nvar CommitteeLivingRoomDetails = function CommitteeLivingRoomDetails() {\n  return import('@/views/CommitteeLivingRoom/CommitteeLivingRoomDetails.vue');\n}; // 委员会客厅详情\nvar CommitteeLivingRoomMessage = function CommitteeLivingRoomMessage() {\n  return import('@/views/CommitteeLivingRoom/CommitteeLivingRoomMessage.vue');\n}; // 委员会客厅留言\nvar CultureHistory = function CultureHistory() {\n  return import('@/views/CultureHistory/CultureHistory.vue');\n}; // 史料征集\nvar CultureHistoryDetails = function CultureHistoryDetails() {\n  return import('@/views/CultureHistory/CultureHistoryDetails.vue');\n}; // 史料征集详情\nvar CultureHistoryAdd = function CultureHistoryAdd() {\n  return import('@/views/CultureHistory/CultureHistoryAdd.vue');\n}; // 史料征集新增\nvar NoticeList = function NoticeList() {\n  return import('@/views/NoticeList/NoticeList.vue');\n}; // 通知公告列表\nvar NoticeDetails = function NoticeDetails() {\n  return import('@/views/NoticeList/NoticeDetails.vue');\n}; // 通知公告详情\nvar NegotiationActivityList = function NegotiationActivityList() {\n  return import('@/views/NegotiationActivityPage/NegotiationActivityList.vue');\n}; // 通知公告详情\nvar ArchiveVisitBooking = function ArchiveVisitBooking() {\n  return import('@/views/ArchiveVisitBooking/ArchiveVisitBooking.vue');\n}; // 文史馆\n\nvar EnvironmentTest = function EnvironmentTest() {\n  return import('@/views/EnvironmentTest.vue');\n}; // 环境检测测试页面\n\n// const casualPhoto = () => import('@/views/casualPhoto/casualPhoto')\n// const addCasualPhoto = () => import('@/views/casualPhoto/addCasualPhoto')\n// const casualPhotoList = () => import('@/views/casualPhoto/casualPhotoList')\n// const casualPhotoDetails = () => import('@/views/casualPhoto/casualPhotoDetails')\n// const addAnswer = () => import('@/views/casualPhoto/addAnswer')\n// const addAssigned = () => import('@/views/casualPhoto/addAssigned')\n// const smartPage = () => import('@/views/casualPhoto/smartPage')\nvar routes = [{\n  path: '/LoginView',\n  name: 'LoginView',\n  component: LoginView\n}, {\n  path: '/LayoutHome',\n  name: 'LayoutHome',\n  component: LayoutHome\n}, {\n  path: '/NewList',\n  name: 'NewList',\n  component: NewList\n}, {\n  path: '/NewDetails',\n  name: 'NewDetails',\n  component: NewDetails\n}, {\n  path: '/NegotiationActivityPage',\n  name: 'NegotiationActivityPage',\n  component: NegotiationActivityPage\n}, {\n  path: '/NetworkDiscussList',\n  name: 'NetworkDiscussList',\n  component: NetworkDiscussList\n}, {\n  path: '/NetworkDiscussDetails',\n  name: 'NetworkDiscussDetails',\n  component: NetworkDiscussDetails\n}, {\n  path: '/NegotiationTopicsDetails',\n  name: 'NegotiationTopicsDetails',\n  component: NegotiationTopicsDetails\n}, {\n  path: '/QuestionnaireList',\n  name: 'QuestionnaireList',\n  component: QuestionnaireList\n}, {\n  path: '/QuestionnaireDetails',\n  name: 'QuestionnaireDetails',\n  component: QuestionnaireDetails\n}, {\n  path: '/CommitteeLivingRoomList',\n  name: 'CommitteeLivingRoomList',\n  component: CommitteeLivingRoomList\n}, {\n  path: '/CommitteeLivingRoomDetails',\n  name: 'CommitteeLivingRoomDetails',\n  component: CommitteeLivingRoomDetails\n}, {\n  path: '/CommitteeLivingRoomMessage',\n  name: 'CommitteeLivingRoomMessage',\n  component: CommitteeLivingRoomMessage\n}, {\n  path: '/CultureHistory',\n  name: 'CultureHistory',\n  component: CultureHistory\n}, {\n  path: '/CultureHistoryDetails',\n  name: 'CultureHistoryDetails',\n  component: CultureHistoryDetails\n}, {\n  path: '/CultureHistoryAdd',\n  name: 'CultureHistoryAdd',\n  component: CultureHistoryAdd\n}, {\n  path: '/NoticeList',\n  name: 'NoticeList',\n  component: NoticeList\n}, {\n  path: '/NoticeDetails',\n  name: 'NoticeDetails',\n  component: NoticeDetails\n}, {\n  path: '/NegotiationActivityList',\n  name: 'NegotiationActivityList',\n  component: NegotiationActivityList\n}, {\n  path: '/ArchiveVisitBooking',\n  name: 'ArchiveVisitBooking',\n  component: ArchiveVisitBooking\n}, {\n  path: '/EnvironmentTest',\n  name: 'EnvironmentTest',\n  component: EnvironmentTest\n}\n\n// { path: '/', name: 'casualPhoto', component: casualPhoto },\n// { path: '/addCasualPhoto', name: 'addCasualPhoto', component: addCasualPhoto },\n// { path: '/casualPhotoList', name: 'casualPhotoList', component: casualPhotoList },\n// { path: '/casualPhotoDetails', name: 'casualPhotoDetails', component: casualPhotoDetails },\n// { path: '/addAnswer', name: 'addAnswer', component: addAnswer },\n// { path: '/addAssigned', name: 'addAssigned', component: addAssigned },\n// { path: '/smartPage', name: 'smartPage', component: smartPage },\n];\nvar history = createWebHashHistory();\nvar router = createRouter({\n  history,\n  routes\n});\nexport { history };\nexport default router;", "map": {"version": 3, "names": ["createRouter", "createWebHashHistory", "<PERSON><PERSON><PERSON>ie<PERSON>", "LayoutHome", "NewList", "NewDetails", "NegotiationActivityPage", "NetworkDiscussList", "NetworkDiscussDetails", "NegotiationTopicsDetails", "QuestionnaireList", "QuestionnaireDetails", "CommitteeLivingRoomList", "CommitteeLivingRoomDetails", "CommitteeLivingRoomMessage", "CultureHistory", "CultureHistoryDetails", "CultureHistoryAdd", "NoticeList", "NoticeDetails", "NegotiationActivityList", "ArchiveVisitBooking", "EnvironmentTest", "routes", "path", "name", "component", "history", "router"], "sources": ["D:/zy/xm/h5/i西安/appperformduties/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHashHistory } from 'vue-router'\r\n\r\nconst LoginView = () => import('@/views/LoginView/LoginView.vue')\r\nconst LayoutHome = () => import('@/views/LayoutHome/LayoutHome.vue')\r\nconst NewList = () => import('@/views/NewList/NewList.vue') // 新闻资讯列表\r\nconst NewDetails = () => import('@/views/NewList/NewDetails.vue') // 新闻资讯详情\r\nconst NegotiationActivityPage = () => import('@/views/NegotiationActivityPage/NegotiationActivityPage.vue') // 协商活动\r\nconst NetworkDiscussList = () => import('@/views/NetworkDiscussList/NetworkDiscussList.vue') // 网络议政列表\r\nconst NetworkDiscussDetails = () => import('@/views/NetworkDiscussList/NetworkDiscussDetails.vue') // 网络议政详情\r\nconst NegotiationTopicsDetails = () => import('@/views/NetworkDiscussList/NegotiationTopicsDetails.vue') // 协商议题征集详情\r\nconst QuestionnaireList = () => import('@/views/QuestionnaireList/QuestionnaireList.vue') // 问卷调查列表\r\nconst QuestionnaireDetails = () => import('@/views/QuestionnaireList/QuestionnaireDetails.vue') // 问卷调查详情\r\nconst CommitteeLivingRoomList = () => import('@/views/CommitteeLivingRoom/CommitteeLivingRoomList.vue') // 委员会客厅列表\r\nconst CommitteeLivingRoomDetails = () => import('@/views/CommitteeLivingRoom/CommitteeLivingRoomDetails.vue') // 委员会客厅详情\r\nconst CommitteeLivingRoomMessage = () => import('@/views/CommitteeLivingRoom/CommitteeLivingRoomMessage.vue') // 委员会客厅留言\r\nconst CultureHistory = () => import('@/views/CultureHistory/CultureHistory.vue') // 史料征集\r\nconst CultureHistoryDetails = () => import('@/views/CultureHistory/CultureHistoryDetails.vue') // 史料征集详情\r\nconst CultureHistoryAdd = () => import('@/views/CultureHistory/CultureHistoryAdd.vue') // 史料征集新增\r\nconst NoticeList = () => import('@/views/NoticeList/NoticeList.vue') // 通知公告列表\r\nconst NoticeDetails = () => import('@/views/NoticeList/NoticeDetails.vue') // 通知公告详情\r\nconst NegotiationActivityList = () => import('@/views/NegotiationActivityPage/NegotiationActivityList.vue') // 通知公告详情\r\nconst ArchiveVisitBooking = () => import('@/views/ArchiveVisitBooking/ArchiveVisitBooking.vue') // 文史馆\r\n\r\nconst EnvironmentTest = () => import('@/views/EnvironmentTest.vue') // 环境检测测试页面\r\n\r\n// const casualPhoto = () => import('@/views/casualPhoto/casualPhoto')\r\n// const addCasualPhoto = () => import('@/views/casualPhoto/addCasualPhoto')\r\n// const casualPhotoList = () => import('@/views/casualPhoto/casualPhotoList')\r\n// const casualPhotoDetails = () => import('@/views/casualPhoto/casualPhotoDetails')\r\n// const addAnswer = () => import('@/views/casualPhoto/addAnswer')\r\n// const addAssigned = () => import('@/views/casualPhoto/addAssigned')\r\n// const smartPage = () => import('@/views/casualPhoto/smartPage')\r\nconst routes = [\r\n  { path: '/LoginView', name: 'LoginView', component: LoginView },\r\n  { path: '/LayoutHome', name: 'LayoutHome', component: LayoutHome },\r\n  { path: '/NewList', name: 'NewList', component: NewList },\r\n  { path: '/NewDetails', name: 'NewDetails', component: NewDetails },\r\n  { path: '/NegotiationActivityPage', name: 'NegotiationActivityPage', component: NegotiationActivityPage },\r\n  { path: '/NetworkDiscussList', name: 'NetworkDiscussList', component: NetworkDiscussList },\r\n  { path: '/NetworkDiscussDetails', name: 'NetworkDiscussDetails', component: NetworkDiscussDetails },\r\n  { path: '/NegotiationTopicsDetails', name: 'NegotiationTopicsDetails', component: NegotiationTopicsDetails },\r\n  { path: '/QuestionnaireList', name: 'QuestionnaireList', component: QuestionnaireList },\r\n  { path: '/QuestionnaireDetails', name: 'QuestionnaireDetails', component: QuestionnaireDetails },\r\n  { path: '/CommitteeLivingRoomList', name: 'CommitteeLivingRoomList', component: CommitteeLivingRoomList },\r\n  { path: '/CommitteeLivingRoomDetails', name: 'CommitteeLivingRoomDetails', component: CommitteeLivingRoomDetails },\r\n  { path: '/CommitteeLivingRoomMessage', name: 'CommitteeLivingRoomMessage', component: CommitteeLivingRoomMessage },\r\n  { path: '/CultureHistory', name: 'CultureHistory', component: CultureHistory },\r\n  { path: '/CultureHistoryDetails', name: 'CultureHistoryDetails', component: CultureHistoryDetails },\r\n  { path: '/CultureHistoryAdd', name: 'CultureHistoryAdd', component: CultureHistoryAdd },\r\n  { path: '/NoticeList', name: 'NoticeList', component: NoticeList },\r\n  { path: '/NoticeDetails', name: 'NoticeDetails', component: NoticeDetails },\r\n  { path: '/NegotiationActivityList', name: 'NegotiationActivityList', component: NegotiationActivityList },\r\n  { path: '/ArchiveVisitBooking', name: 'ArchiveVisitBooking', component: ArchiveVisitBooking },\r\n  { path: '/EnvironmentTest', name: 'EnvironmentTest', component: EnvironmentTest },\r\n\r\n  // { path: '/', name: 'casualPhoto', component: casualPhoto },\r\n  // { path: '/addCasualPhoto', name: 'addCasualPhoto', component: addCasualPhoto },\r\n  // { path: '/casualPhotoList', name: 'casualPhotoList', component: casualPhotoList },\r\n  // { path: '/casualPhotoDetails', name: 'casualPhotoDetails', component: casualPhotoDetails },\r\n  // { path: '/addAnswer', name: 'addAnswer', component: addAnswer },\r\n  // { path: '/addAssigned', name: 'addAssigned', component: addAssigned },\r\n  // { path: '/smartPage', name: 'smartPage', component: smartPage },\r\n]\r\nconst history = createWebHashHistory()\r\nconst router = createRouter({ history, routes })\r\n\r\nexport { history }\r\nexport default router\r\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,oBAAoB,QAAQ,YAAY;AAE/D,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAA;EAAA,OAAS,MAAM,CAAC,iCAAiC,CAAC;AAAA;AACjE,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAA;EAAA,OAAS,MAAM,CAAC,mCAAmC,CAAC;AAAA;AACpE,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAA;EAAA,OAAS,MAAM,CAAC,6BAA6B,CAAC;AAAA,GAAC;AAC5D,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAA;EAAA,OAAS,MAAM,CAAC,gCAAgC,CAAC;AAAA,GAAC;AAClE,IAAMC,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAA;EAAA,OAAS,MAAM,CAAC,6DAA6D,CAAC;AAAA,GAAC;AAC5G,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA;EAAA,OAAS,MAAM,CAAC,mDAAmD,CAAC;AAAA,GAAC;AAC7F,IAAMC,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAA;EAAA,OAAS,MAAM,CAAC,sDAAsD,CAAC;AAAA,GAAC;AACnG,IAAMC,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAA;EAAA,OAAS,MAAM,CAAC,yDAAyD,CAAC;AAAA,GAAC;AACzG,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA;EAAA,OAAS,MAAM,CAAC,iDAAiD,CAAC;AAAA,GAAC;AAC1F,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA;EAAA,OAAS,MAAM,CAAC,oDAAoD,CAAC;AAAA,GAAC;AAChG,IAAMC,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAA;EAAA,OAAS,MAAM,CAAC,yDAAyD,CAAC;AAAA,GAAC;AACxG,IAAMC,0BAA0B,GAAG,SAA7BA,0BAA0BA,CAAA;EAAA,OAAS,MAAM,CAAC,4DAA4D,CAAC;AAAA,GAAC;AAC9G,IAAMC,0BAA0B,GAAG,SAA7BA,0BAA0BA,CAAA;EAAA,OAAS,MAAM,CAAC,4DAA4D,CAAC;AAAA,GAAC;AAC9G,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAA;EAAA,OAAS,MAAM,CAAC,2CAA2C,CAAC;AAAA,GAAC;AACjF,IAAMC,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAA;EAAA,OAAS,MAAM,CAAC,kDAAkD,CAAC;AAAA,GAAC;AAC/F,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA;EAAA,OAAS,MAAM,CAAC,8CAA8C,CAAC;AAAA,GAAC;AACvF,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAA;EAAA,OAAS,MAAM,CAAC,mCAAmC,CAAC;AAAA,GAAC;AACrE,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAA;EAAA,OAAS,MAAM,CAAC,sCAAsC,CAAC;AAAA,GAAC;AAC3E,IAAMC,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAA;EAAA,OAAS,MAAM,CAAC,6DAA6D,CAAC;AAAA,GAAC;AAC5G,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA;EAAA,OAAS,MAAM,CAAC,qDAAqD,CAAC;AAAA,GAAC;;AAEhG,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAA;EAAA,OAAS,MAAM,CAAC,6BAA6B,CAAC;AAAA,GAAC;;AAEpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMC,MAAM,GAAG,CACb;EAAEC,IAAI,EAAE,YAAY;EAAEC,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAExB;AAAU,CAAC,EAC/D;EAAEsB,IAAI,EAAE,aAAa;EAAEC,IAAI,EAAE,YAAY;EAAEC,SAAS,EAAEvB;AAAW,CAAC,EAClE;EAAEqB,IAAI,EAAE,UAAU;EAAEC,IAAI,EAAE,SAAS;EAAEC,SAAS,EAAEtB;AAAQ,CAAC,EACzD;EAAEoB,IAAI,EAAE,aAAa;EAAEC,IAAI,EAAE,YAAY;EAAEC,SAAS,EAAErB;AAAW,CAAC,EAClE;EAAEmB,IAAI,EAAE,0BAA0B;EAAEC,IAAI,EAAE,yBAAyB;EAAEC,SAAS,EAAEpB;AAAwB,CAAC,EACzG;EAAEkB,IAAI,EAAE,qBAAqB;EAAEC,IAAI,EAAE,oBAAoB;EAAEC,SAAS,EAAEnB;AAAmB,CAAC,EAC1F;EAAEiB,IAAI,EAAE,wBAAwB;EAAEC,IAAI,EAAE,uBAAuB;EAAEC,SAAS,EAAElB;AAAsB,CAAC,EACnG;EAAEgB,IAAI,EAAE,2BAA2B;EAAEC,IAAI,EAAE,0BAA0B;EAAEC,SAAS,EAAEjB;AAAyB,CAAC,EAC5G;EAAEe,IAAI,EAAE,oBAAoB;EAAEC,IAAI,EAAE,mBAAmB;EAAEC,SAAS,EAAEhB;AAAkB,CAAC,EACvF;EAAEc,IAAI,EAAE,uBAAuB;EAAEC,IAAI,EAAE,sBAAsB;EAAEC,SAAS,EAAEf;AAAqB,CAAC,EAChG;EAAEa,IAAI,EAAE,0BAA0B;EAAEC,IAAI,EAAE,yBAAyB;EAAEC,SAAS,EAAEd;AAAwB,CAAC,EACzG;EAAEY,IAAI,EAAE,6BAA6B;EAAEC,IAAI,EAAE,4BAA4B;EAAEC,SAAS,EAAEb;AAA2B,CAAC,EAClH;EAAEW,IAAI,EAAE,6BAA6B;EAAEC,IAAI,EAAE,4BAA4B;EAAEC,SAAS,EAAEZ;AAA2B,CAAC,EAClH;EAAEU,IAAI,EAAE,iBAAiB;EAAEC,IAAI,EAAE,gBAAgB;EAAEC,SAAS,EAAEX;AAAe,CAAC,EAC9E;EAAES,IAAI,EAAE,wBAAwB;EAAEC,IAAI,EAAE,uBAAuB;EAAEC,SAAS,EAAEV;AAAsB,CAAC,EACnG;EAAEQ,IAAI,EAAE,oBAAoB;EAAEC,IAAI,EAAE,mBAAmB;EAAEC,SAAS,EAAET;AAAkB,CAAC,EACvF;EAAEO,IAAI,EAAE,aAAa;EAAEC,IAAI,EAAE,YAAY;EAAEC,SAAS,EAAER;AAAW,CAAC,EAClE;EAAEM,IAAI,EAAE,gBAAgB;EAAEC,IAAI,EAAE,eAAe;EAAEC,SAAS,EAAEP;AAAc,CAAC,EAC3E;EAAEK,IAAI,EAAE,0BAA0B;EAAEC,IAAI,EAAE,yBAAyB;EAAEC,SAAS,EAAEN;AAAwB,CAAC,EACzG;EAAEI,IAAI,EAAE,sBAAsB;EAAEC,IAAI,EAAE,qBAAqB;EAAEC,SAAS,EAAEL;AAAoB,CAAC,EAC7F;EAAEG,IAAI,EAAE,kBAAkB;EAAEC,IAAI,EAAE,iBAAiB;EAAEC,SAAS,EAAEJ;AAAgB;;AAEhF;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,CACD;AACD,IAAMK,OAAO,GAAG1B,oBAAoB,CAAC,CAAC;AACtC,IAAM2B,MAAM,GAAG5B,YAAY,CAAC;EAAE2B,OAAO;EAAEJ;AAAO,CAAC,CAAC;AAEhD,SAASI,OAAO;AAChB,eAAeC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}