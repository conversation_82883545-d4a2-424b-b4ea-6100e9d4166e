<template>
  <van-popup :show="show" @close="handleClose" position="bottom"
    :style="{ height: '400px', borderRadius: '16px 16px 0 0' }">
    <div class="info-popup-content">
      <div class="info-popup-title">{{ props.title || '请填写您的信息' }}</div>
      <van-field v-model="userName" label="姓名" placeholder="请输入您的姓名" :rules="[{ required: true, message: '请输入姓名' }]" />
      <van-field v-model="mobile" label="手机号" placeholder="请输入您的手机号" type="number" maxlength="11"
        :rules="[{ required: true, message: '请输入手机号' }, { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }]" />
      <van-field v-model="verifyCode" label="验证码" placeholder="请输入验证码" type="number" maxlength="6"
        :rules="[{ required: true, message: '请输入验证码' }, { pattern: /^\d{6}$/, message: '请输入6位验证码' }]">
        <template #button>
          <van-button :disabled="codeDisabled" size="mini" type="primary" @click="getVerifyCode">{{ codeText
            }}</van-button>
        </template>
      </van-field>
      <div class="info-popup-actions">
        <van-button type="primary" @click="submitUserInfo" style="width: 100%">{{ props.confirmText || '确认'
          }}</van-button>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue'
import { showToast } from 'vant'
import api from '@/api'

// 定义props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '请填写您的信息'
  },
  confirmText: {
    type: String,
    default: '确认'
  }
})

// 定义emits
const emit = defineEmits(['close', 'success'])

// 关闭弹窗
const handleClose = () => {
  emit('close')
}

// 响应式变量
const userName = ref('')
const mobile = ref('')
const verifyCode = ref('')
const verifyCodeId = ref('')
const codeDisabled = ref(false)
const codeText = ref('获取验证码')

// 获取验证码
const getVerifyCode = () => {
  if (!/^1[3-9]\d{9}$/.test(mobile.value)) {
    showToast('请输入正确的手机号')
    return
  }

  // 模拟获取验证码倒计时
  let countdown = 60
  codeDisabled.value = true
  codeText.value = `${countdown}s后重新获取`
  const timer = setInterval(() => {
    countdown--
    codeText.value = `${countdown}s后重新获取`
    if (countdown <= 0) {
      clearInterval(timer)
      codeDisabled.value = false
      codeText.value = '获取验证码'
    }
  }, 1000)

  // 调用获取验证码的API
  api.sendVerifyCode({ sendType: 'no_login', mobile: mobile.value })
    .then(res => {
      if (res.code === 200) {
        verifyCodeId.value = res.data
        showToast('验证码发送成功')
      } else {
        showToast(res.message || '验证码发送失败')
        // 发送失败时重置按钮状态
        clearInterval(timer)
        codeDisabled.value = false
        codeText.value = '获取验证码'
      }
    })
    .catch(() => {
      showToast('网络错误，请稍后重试')
      // 发送失败时重置按钮状态
      clearInterval(timer)
      codeDisabled.value = false
      codeText.value = '获取验证码'
    })
}

// 提交用户信息
const submitUserInfo = async () => {
  if (!userName.value.trim()) {
    showToast('请输入姓名')
    return
  }
  if (!/^1[3-9]\d{9}$/.test(mobile.value)) {
    showToast('请输入正确的手机号')
    return
  }
  if (!verifyCode.value.trim() || verifyCode.value.length !== 6) {
    showToast('请输入正确的验证码')
    return
  }

  try {
    // 调用登录接口
    const { data } = await api.login({
      grant_type: 'anonymoussso',
      userName: userName.value.trim(),
      mobile: mobile.value.trim(),
      verifyCodeId: verifyCodeId.value,
      verifyCode: verifyCode.value.trim()
    }, {
      headers: {
        authorization: 'basic enlzb2Z0Onp5c29mdCo2MDc5'
      }
    })

    // 保存登录信息
    sessionStorage.setItem('token', data.token)
    sessionStorage.setItem('refresh_token', `bearer ${data.refreshToken.value}`)
    sessionStorage.setItem('expires', data.expires_in)
    sessionStorage.setItem('expiration', data.refreshToken.expiration)
    sessionStorage.setItem('verify', data.passwordElementMatchReg ? 1 : 0)

    // 获取用户信息
    const { data: user } = await api.loginUser()
    sessionStorage.setItem('public_user', JSON.stringify(user))

    // 登录成功，通知父组件
    showToast('验证成功')
    emit('success', user)
    emit('close')
  } catch (error) {
    showToast(error.message || '验证失败，请重试')
  }
}
</script>

<style lang="scss">
.info-popup-content {
  padding: 24px 16px;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  background-color: #fff;

  .info-popup-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
    text-align: center;
  }

  .info-popup-actions {
    margin-top: 20px;
  }

  .van-field__button .van-button {
    height: 28px;
    padding: 0 8px;
    font-size: 12px;
    border-radius: 16px;
  }

  /* 优化输入框样式 */
  .van-field {
    margin-bottom: 16px;
    background-color: #f7f8fa;
    border-radius: 8px;
    overflow: hidden;
    align-items: center;
  }

  /* 优化按钮样式 */
  .info-popup-actions .van-button {
    height: 44px;
    line-height: 44px;
    font-size: 16px;
    border-radius: 22px;
    background-color: #4488EB;
  }

  .van-field__button .van-button {
    height: 28px;
    padding: 0 8px;
    font-size: 12px;
    border-radius: 16px;
  }

  /* 优化标签颜色 */
  .van-field__label {
    color: #666;
    font-size: 14px;
    width: 60px !important;
  }

  /* 优化输入文字颜色 */
  .van-field__control {
    color: #333;
    font-size: 14px;
  }

  /* 优化占位符颜色 */
  .van-field__control::placeholder {
    color: #999;
  }
}
</style>