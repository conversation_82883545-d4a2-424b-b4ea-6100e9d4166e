<template>
  <div class="casualPhotoDetails">
    <div class="casualPhotoDetailsContent"
         v-if="info.id">
      <van-pull-refresh v-model="refreshing"
                        @refresh="onRefresh">
        <div class="title">{{ info.name }}</div>
        <div class="flex_box flex_align_center">
          <img :src="info.userImg"
               alt="用户头像"
               class="userImg" />
          <div class="userName">{{ info.userName }}</div>
          <div v-if="info.tag"
               class="tag">{{ info.tag }}</div>
          <div class="flex_placeholder"></div>
          <div v-if="info.questionTypeName"
               class="type">{{ info.questionTypeName }}</div>
        </div>
        <div class="flex_box">
          <div class="time">{{ formatDate(info.createDate) }}</div>
          <div class="flex_box ">
            <div class="flex_box commentBox"
                 @click.stop="handlePraises(info)">
              <van-icon :name="info.hasClickPraises ? 'good-job' : 'good-job-o'"
                        :class="{ 'active': info.hasClickPraises }" />
              <div class="count"
                   :class="{ 'active': info.hasClickPraises }">{{ info.praisesCount || 0 }}</div>
            </div>
            <div class="flex_box commentBox">
              <van-icon name="comment-o" />
              <div class="count">{{ info.commentCount || 0 }}</div>
            </div>
          </div>
        </div>
        <div class="content">{{ info.content }}</div>
        <div class="flex_box imgBox">
          <van-image v-for="(item, index) in info.imgList"
                     :src="item.url"
                     :key="item"
                     class="img"
                     fit="cover"
                     @click="openImagePreview(index)"></van-image>
        </div>
        <div class="flex_box">
          <div class="stationName"><van-icon name="location-o" />{{ info.address }}</div>
          <div class="line">|</div>
          <div class="stationName"><van-icon name="home-o" />{{ info.stationName }}</div>
        </div>
      </van-pull-refresh>
    </div>
    <div class="processBox">
      <processItem v-if="info.convenientlyProcessingRecordListVos && info.convenientlyProcessingRecordListVos.length"
                   :itemData="info.convenientlyProcessingRecordListVos" />
    </div>
    <commentList class="commentList"
                 ref="commentListRef"
                 :businessCode="'convenientlyPat'"
                 :id="route.query.id" />
    <div style="height:120px;"></div>
    <footer class="bottomBox">
      <div v-if="roleId === '1' || roleId === '2' || roleId === '3' || roleId === '4'"
           class="flex_box flex_justify_content">
        <div class="muneBoxItem"
             v-for="(item, index) in btnList"
             :key="index"
             @click="itemClick(item)">
          <img class="muneBoxItemIcon"
               :src="item.icon"
               alt="">
          <div class="muneBoxItemText">{{ item.name }}</div>
        </div>
      </div>
      <van-button v-if="showCheck"
                  class="btn"
                  type="primary"
                  size="large"
                  @click="actionshow = true">审核</van-button>
      <!-- <inputBox ref="inputBox"
                :inputData="inputData"
                @addCommentEvent="addCommentEvent"
                :type="relateType"
                :id="id" /> -->
    </footer>
    <van-popup v-model:show="commentShow"
               position="bottom">
      <commentInput :businessCode="'convenientlyPat'"
                    :id="route.query.id"
                    :placeholder="'请输入评论内容'"
                    @callback="callback"></commentInput>
    </van-popup>
    <van-action-sheet v-model:show="actionshow"
                      :actions="actions"
                      @select="onSelect" />
    <van-action-sheet v-model:show="returnshow"
                      :actions="returnActions"
                      @select="onReturnSelect" />
  </div>
</template>
<script>
export default { name: 'casualPhotoDetails' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import { showToast, showConfirmDialog, showImagePreview } from 'vant'
import { useRoute, useRouter } from 'vue-router'
import { formatDate } from '@/assets/js/utils.js'
import processItem from './components/processItem.vue'
import commentList from './components/commentList.vue'
import commentInput from './components/commentInput.vue'
const route = useRoute()
const router = useRouter()
const title = ref('随手拍详情')
const user = ref(JSON.parse(sessionStorage.getItem('user')) || '')
const stationUserInfo = ref(JSON.parse(sessionStorage.getItem('stationUserInfo')) || '')
const roleId = ref('1')
const btnList = ref([])
const showCheck = ref(false)
const commentShow = ref(false)
const actionshow = ref(false)
const returnshow = ref(false)
const refreshing = ref(false)
const commentListRef = ref(null)
const actions = ref([
  { name: '通过', key: 'pass' },
  { name: '退回', key: 'return' }
])
const returnActions = ref([])
const info = ref({
  // title: '违规施工没有任何安全措施，会出重大影响',
  // content: '位于东方红路青山路路口道路严重破损位于东方红路青山路路口道路严重破损',
  // time: '2022-12-12 12:12:12',
  // answerTime: '2025-05-08 12:12:12',
  // status: '已回复',
  // userName: '张三',
  // type: '基层治理',
  // tag: '长沙市代表团人大代表',
  // userAvatar: 'https://img0.baidu.com/it/u=2796591081,4265685041&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=747',
  // stationName: '东方红街道代表联系站',
  // address: '东方红路青山路路口',
  // commentCount: 1,
  // likeCount: 1,
  // isLike: false,
  // imgList: [{
  //   url: 'https://pics5.baidu.com/feed/a044ad345982b2b78ab10bad204736e377099ba7.jpeg@f_auto?token=72e1cf5205d91e7eb227052abdda3405'
  // }, {
  //   url: 'https://www.jdnews.com.cn/xwjsb/images/2023-07/14/260a1cfc-a81d-4a48-a478-14b80c38f397.jpg'
  // }, {
  //   url: 'https://img0.baidu.com/it/u=3548398977,4213109153&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1067'
  // }, {
  //   url: 'http://img2.baidu.com/it/u=2971848481,3999724549&fm=253&app=138&f=JPEG?w=427&h=759'
  // }, {
  //   url: 'http://img0.baidu.com/it/u=3178311242,3030645591&fm=253&app=138&f=JPEG?w=427&h=759'
  // }],
  // replydata: {
  //   stationName: '东方红街道代表联系站',
  //   reply: '代表您好，您反映的问题已办结，感谢您的支持！',
  //   imgList: [{
  //     url: 'https://pics5.baidu.com/feed/a044ad345982b2b78ab10bad204736e377099ba7.jpeg@f_auto?token=72e1cf5205d91e7eb227052abdda3405'
  //   }, {
  //     url: 'https://www.jdnews.com.cn/xwjsb/images/2023-07/14/260a1cfc-a81d-4a48-a478-14b80c38f397.jpg'
  //   }, {
  //     url: 'https://img0.baidu.com/it/u=3548398977,4213109153&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1067'
  //   }, {
  //     url: 'http://img2.baidu.com/it/u=2971848481,3999724549&fm=253&app=138&f=JPEG?w=427&h=759'
  //   }, {
  //     url: 'http://img0.baidu.com/it/u=3178311242,3030645591&fm=253&app=138&f=JPEG?w=427&h=759'
  //   }]
  // }
})
// const processData = ref([
//   {
//     status: '待办理',
//     stationName: '东方红街道代表联系站',
//     time: '2022-12-12 12:12:12',
//     user: '张三',
//     phone: '13888888888',
//     content: '已提交'
//   },
//   {
//     status: '上报至',
//     stationName: '岳麓区代表工委',
//     time: '2022-12-12 12:12:12',
//     user: '张三',
//     phone: '13888888888',
//     content: '已提交'
//   },
//   {
//     status: '上报至',
//     stationName: '长沙市代表工委',
//     time: '2022-12-12 12:12:12',
//     user: '张三',
//     phone: '13888888888',
//     content: '已提交'
//   }
// ])
onMounted(() => {
  if (title.value) {
    document.title = title.value
  }
  route.query.roleId && (roleId.value = route.query.roleId || '1')
  sessionStorage.getItem('roleId') && (roleId.value = sessionStorage.getItem('roleId') || '1')
  // if (roleId.value === '1') {
  //   btnList.value = [
  //     { icon: require('../../assets/img/Comment-one.png'), name: '评论', id: 'commentBtn' },
  //     // { icon: require('../../assets/img/Share.png'), name: '交办', id: 'jb' }
  //   ]
  // } else if (roleId.value === '2') {
  //   btnList.value = [
  //     { icon: require('../../assets/img/Comment-one.png'), name: '评论', id: 'commentBtn' },
  //     { icon: require('../../assets/img/Accept-email.png'), name: '答复', id: 'answer' },
  //     // { icon: require('../../assets/img/Mail-review.png'), name: '公开', id: 'public' },
  //     { icon: require('../../assets/img/Share.png'), name: '交办', id: 'jb' },
  //     { icon: require('../../assets/img/Group_19.png'), name: '转线索库', id: 'xsk' }
  //   ]
  // } else if (roleId.value === '3' || roleId.value === '4') {
  //   btnList.value = [
  //     { icon: require('../../assets/img/Comment-one.png'), name: '评论', id: 'commentBtn' },
  //     { icon: require('../../assets/img/Share.png'), name: '交办', id: 'jb' }
  //   ]
  // }
  // 基层工作人员，如果有关键字存在，则显示审核按钮
  if (showCheck.value) {
    showCheck.value = true
  }
  getDictionaryData()
  setTimeout(() => {
    onRefresh()
  }, 100)
})
const getDictionaryData = async () => {
  const { data } = await api.dictionaryData({ dictCodes: ['clue_type'] })
  var datas = data.clue_type || []
  // datas.forEach((item, index) => {

  // });
  returnActions.value = [...datas]
}
const getDetails = async () => {
  const { data } = await api.zyConvenientlyPatInfo({ detailId: route.query.id })
  info.value = data
  // btnList.value = [
  //   { icon: require('../../assets/img/Comment-one.png'), name: '评论', id: 'commentBtn' },
  //  { icon: require('../../assets/img/Accept-email.png'), name: '答复', id: 'answer' },
  //   { icon: require('../../assets/img/Mail-review.png'), name: '公开', id: 'public' },
  // { icon: require('../../assets/img/Share.png'), name: '交办', id: 'jb' },
  //   { icon: require('../../assets/img/Group_19.png'), name: '转线索库', id: 'xsk' }
  // ]
  btnList.value = [{ icon: require('../../assets/img/Comment-one.png'), name: '评论', id: 'commentBtn' }]
  console.log(stationUserInfo.value.ifStationWorker);

  if (data.processState === '20' && stationUserInfo.value.ifStationWorker) {
    btnList.value.push({ icon: require('../../assets/img/Accept-email.png'), name: '答复', id: 'answer' })
    btnList.value.push({ icon: require('../../assets/img/Share.png'), name: '交办', id: 'jb' })
  }
  if (data.processState === '30' && stationUserInfo.value.ifStationAdmin) {
    btnList.value.push({ icon: require('../../assets/img/Share.png'), name: '交办', id: 'jb' })
  }
  if (data.processState === '33' && stationUserInfo.value.ifStationAdmin) {
    //交办，交12345
    btnList.value.push({ icon: require('../../assets/img/Share.png'), name: '交办', id: 'jb' })
  }
  if (data.processState === '36' && stationUserInfo.value.ifStationAdmin && data.assignArea === stationUserInfo.value.adminAreaId) {
    //交12345
    btnList.value.push({ icon: require('../../assets/img/Share.png'), name: '交办', id: 'jb' })
  }
  if (data.processState === '50' && stationUserInfo.value.ifStationAdmin) {
    if (data.ifClue !== '1') {
      btnList.value.push({ icon: require('../../assets/img/Mail-review.png'), name: '公开', id: 'public' })
    }
    if (data.ifPublic !== '1') {
      btnList.value.push({ icon: require('../../assets/img/Group_19.png'), name: '转线索库', id: 'xsk' })
    }

    // if (stationUserInfo.value.ifStationAdmin) {

    // } else {

    // }
  }
}
const handlePraises = async (itemData) => {
  const params = {
    form: {
      relationId: itemData.id,
      userId: user.value.id,
    }
  }
  itemData.hasClickPraises = !itemData.hasClickPraises
  if (itemData.hasClickPraises) {
    itemData.praisesCount++
    await api.zyConvenientlyBrowseAdd(params)
  } else {
    itemData.praisesCount--
    await api.zyConvenientlyBrowseDels(params)
  }
}
const openImagePreview = (index) => {
  const imgList = info.value.imgList.map(item => item.url)
  showImagePreview({
    images: imgList,
    startPosition: index
  })
}
const onSelect = (item) => {
  console.log(item)
  actionshow.value = false
}
const onReturnSelect = (item) => {
  console.log(item)
  returnshow.value = false
  showConfirmDialog({
    title: '温馨提示',
    confirmButtonColor: '#4488EB',
    message:
      '确定要将这条信息转线索库吗？'
  })
    .then(() => {
      showToast('转线索库成功！')
      api.clueZyConvenientlyPat({
        detailId: route.query.id,
        clueType: item.id
      })
      onRefresh()
    })
    .catch(() => {
      // on cancel
    })
}
const onRefresh = () => {
  refreshing.value = false
  getDetails()
}
// const addCommentEvent = (value) => {
//   console.log(value)
//   data.commentList.onRefresh()
// }
// const openInputBoxEvent = (value) => {
//   console.log(value)
//   data.inputBox.changeType(2, value)
// }
// const freshState = (value) => {
//   console.log(value)
//   data.inputBox.getStats()
// }
const callback = () => {
  commentShow.value = false
  commentListRef.value.getList()
  getDetails()
}
const itemClick = (item) => {
  switch (item.id) {
    case 'commentBtn':
      commentShow.value = true
      break
    case 'answer':
      router.push({ path: 'addAnswer', query: { id: route.query.id, title: item.name } })
      break
    case 'jb':
      router.push({ path: 'addAssigned', query: { id: route.query.id, title: item.name } })
      break
    case 'xsk':
      returnshow.value = true
      // showConfirmDialog({
      //   title: '温馨提示',
      //   confirmButtonColor: '#4488EB',
      //   message:
      //     '确定要将这条信息转线索库吗？'
      // })
      //   .then(() => {
      //     showToast('转线索库成功！')
      //     api.clueZyConvenientlyPat({
      //       detailId: route.query.id,
      //       clueType: ''
      //     })
      //   })
      //   .catch(() => {
      //     // on cancel
      //   })
      break
    case 'public':
      showConfirmDialog({
        title: '温馨提示',
        confirmButtonColor: '#4488EB',
        message: '确定要公开这条信息吗？'
      })
        .then(async () => {
          const { code } = await api.publicZyConvenientlyPat({ detailId: route.query.id })
          if (code === 200) {
            showToast('公开成功！')
            onRefresh()
          }
        })
        .catch(() => {
          // on cancel
        })
      break
  }
}
</script>
<style lang="scss">
.casualPhotoDetails {
  height: 100%;
  background: #F4F5F9;
  overflow: auto;

  .casualPhotoDetailsContent {
    background: #fff;
    padding: 13px 19px;
    overflow: auto;

    .title {
      font-weight: 600;
      font-size: 18px;
      color: #333333;
      line-height: 22px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .userImg {
      width: 32px;
      height: 32px;
      border-radius: 21px 21px 21px 21px;
      margin-right: 11px;
    }

    .userName {
      font-weight: 400;
      font-size: 13px;
      color: #666666;
      line-height: 22px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .tag {
      margin-left: 5px;
      background: rgba(68, 136, 235, 0.2);
      border-radius: 2px 2px 2px 2px;
      border: 1px solid #4488EB;
      font-weight: 400;
      font-size: 12px;
      color: #4488EB;
      padding: 1px 5px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .type {
      height: 18px;
      padding: 1px 5px;
      background: #4488EB;
      border-radius: 4px 4px 4px 4px;
      color: #FFFFFF;
      font-weight: 400;
      font-size: 12px;
      color: #FFFFFF;
    }

    .time {
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #999999;
      line-height: 22px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      width: 100%;
      flex: 1;
    }

    .commentBox {
      margin-left: 15px;

      .count {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #999999;
        line-height: 12px;
        text-align: left;
        margin-left: 5px;
      }

      .active {
        color: #4488EB;
      }
    }

    .content {
      margin-top: 24px;
      font-weight: 400;
      font-size: 16px;
      color: #333333;
      line-height: 24px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .imgBox {
      flex-wrap: wrap;
    }

    .img {
      width: 73px;
      height: 73px;
      border-radius: 0px 0px 0px 0px;
      margin-bottom: 8px;
      margin-right: 4px;
    }
  }

  .processBox {
    margin-top: 10px;
  }

  .commentList {
    margin-top: 10px;
  }

  .stationName {
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    line-height: 19px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .line {
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    line-height: 19px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin: 0 10px;
  }

  .bottomBox {
    width: 100%;
    background: #FFFFFF;
    border-radius: 0px 0px 0px 0px;
    position: fixed;
    bottom: 0;
    left: 0;

    .btn {
      width: calc(100% - 24px);
      margin: 12px;
    }

    .muneBoxItem {
      padding: 10px 20px;
      text-align: center;

      .muneBoxItemIcon {
        width: 24px;
        height: 24px;
        text-align: center;
      }

      .muneBoxItemText {
        font-weight: 400;
        font-size: 14px;
        color: #000000;
        line-height: 14px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }

  .popupBox {
    padding: 20px 20px;
  }
}
</style>
