<template>
  <div class="commentInput">
    <div class="inputBox">
      <van-field v-model="message"
                 rows="4"
                 autosize
                 type="textarea"
                 :placeholder="props.placeholder" />
    </div>
    <div class="bottomBox flex_box">
      <div class="flex_placeholder"></div>
      <div class='subBtn'
           @click="submit">提交</div>
    </div>
  </div>
</template>
<script>
export default { name: 'commentInput' }
</script>
<script setup>
import api from '@/api'
import { ref } from 'vue'
// import { showImagePreview } from 'vant'
// import { formatDate } from '@/assets/js/utils.js'
const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  pid: {
    type: String,
    default: ''
  },
  businessCode: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入评论内容'
  }
})
console.log(props);

const emit = defineEmits(['callback', 'reload'])
const user = ref(JSON.parse(sessionStorage.getItem('user')) || '')
const message = ref('')
const submit = async () => {
  var param = {
    form: {
      terminalName: 'APP',
      businessCode: props.businessCode,
      businessId: props.id,
      commentContent: message.value,
      attachmentIds: '',
      checkedStatus: '1',
      publishAccountId: user.value.id,
      commentUserName: user.value.userName,
      headImg: user.value.headImg,
      commentUserMobile: user.value.mobile,
    }
  }
  if (props.pid) {
    param.form.parentId = props.pid
  }
  const { data } = await api.zyConvenientlyCommentAdd(param)
  message.value = ''
  emit('callback', data)
  emit('reload', data)
}

</script>
<style lang="scss">
.commentInput {
  background-color: #fff;

  .inputBox {
    width: 100%;
    padding-bottom: 48px;
  }

  .bottomBox {
    height: 48px;
    margin-top: 10px;
    padding: 10px;

    .subBtn {
      width: 56px;
      height: 28px;
      line-height: 28px;
      text-align: center;
      background: #4488EB;
      border-radius: 28px;
      font-size: 16px;
      color: #FFFFFF;
    }
  }

}
</style>
