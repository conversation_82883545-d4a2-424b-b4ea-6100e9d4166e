<template>
  <div class="NewList">
    <van-sticky>
      <van-search v-model="keyword" shape="round" placeholder="请输入关键词" @search="onSearch"></van-search>
      <van-tabs v-if="switchs.data.length && route.query.utype" v-model:active="switchs.value" :color="'#4488EB '"
        @click-tab="onClickTab">
        <van-tab :title="item.name" :name="item.id" v-for="item in switchs.data" :key="item.id"></van-tab>
      </van-tabs>
    </van-sticky>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list v-model:loading="loading" :finished="finished" :finished-text="dataList.length == 0 ? '' : '没有更多了'"
        offset="52" @load="onLoad">
        <div class="swiper_box" v-if="!route.query.moduleId">
          <van-swipe class="swiper-container" :autoplay="3000" indicator-color="#fff" :show-indicators="false"
            :loop="true" v-model:active="swiperActive" @change="val => swiperActive = val">
            <van-swipe-item v-for="(item, index) in swiperList" :key="index" class="swiper-item"
              @click="openCarouselDetails(item)">
              <div class="swiper-card">
                <div class="swiper-image-container">
                  <img :src="config.API_URL + '/image/' + item.infoPic" :alt="item.title" class="swiper-image">
                </div>
                <div class="swiper-text-box">
                  <div class="swiper-text-block">
                    <img class="swiper-icon" src="https://cszysoft.com/appShare/xazxnew/image/icon_img_hot_zx.png"
                      alt="">
                    <div class="swiper-title">{{ item.infoTitle }}</div>
                  </div>
                </div>
                <div class="swiper-indicator">
                  <span v-for="(item, idx) in swiperList" :key="idx"
                    :class="['dot', { active: idx === swiperActive }]" />
                </div>
              </div>
            </van-swipe-item>
          </van-swipe>
        </div>
        <div v-for="item in dataList" :key="item.id" class="list_box" @click="openDetails(item)">
          <div class="list_item">
            <img :src="config.API_URL + '/image/' + item.infoPic" alt="" v-if="item.infoPic"
              style="object-fit: cover;width: 112px;height: 84px;">
            <div style="margin-left: 10px;">
              <div class="list_item_title">{{ item.infoTitle }}</div>
              <div class="list_item_time">{{ formatDate(item.pubTime, 'YYYY-MM-DD') }}</div>
              <div class="list_item_source">{{ item.infoSource }}</div>
              <!-- <div>
                <span class="list_item_time">{{ formatDate(item.pubTime, 'YYYY-MM-DD') }}</span>
                <span class="list_item_source">{{ item.infoSource }}</span>
              </div> -->
            </div>
          </div>
        </div>
        <van-empty v-if="dataList.length == 0 && !loading" description="暂无数据" />
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
export default { name: 'NewList' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import config from '@/config'
import { formatDate } from '@/assets/js/utils.js'
import { useRouter, useRoute } from 'vue-router'
const route = useRoute()
const router = useRouter()

const swiperActive = ref(0)
const title = ref(route.query.title || '资讯列表')
const keyword = ref('')//搜索关键词
const switchs = ref({ value: '', data: [] })
const loading = ref(false)//加载中
const finished = ref(false)//是否加载完成
const refreshing = ref(false)//下拉刷新
const pageNo = ref(1)//当前页码
const pageSize = ref(10)//每页条数
const total = ref(0)//总条数
const dataList = ref([])
const swiperList = ref([])
onMounted(() => {
  if (title.value) {
    document.title = title.value
  }
  switchs.value.value = localStorage.getItem('newTabId')
  if (route.query.utype) {
    getNewColumnList()
  } else {
    setTimeout(() => {
      onRefresh()
    }, 100)
  }
})
const onSearch = () => {
  onRefresh()
}
const onRefresh = () => {
  pageNo.value = 1
  dataList.value = []
  finished.value = false
  loading.value = true
  getList(localStorage.getItem('newTabId'))
}
const onLoad = () => {
  if (dataList.value.length < total.value) {
    pageNo.value++
    getList(localStorage.getItem('newTabId'))
  } else {
    finished.value = true
  }
}
// 获取资讯栏目
const getNewColumnList = async () => {
  const res = await api.newsColumnList({
    pageNo: 1,
    pageSize: 999,
    query: { moduleId: route.query.moduleId, parentId: route.query.parentId || '0' }
  })
  switchs.value.data = res.data
  getNewTopCarousel()
  onRefresh()
}
// 获取置顶轮播资讯
const getNewTopCarousel = async (columnId) => {
  const { data } = await api.newsContentTopList({
    pageNo: 1,
    pageSize: 5,
    query: { moduleId: route.query.moduleId || 1, columnId: columnId || switchs.value.data[0].id || '' }
  })
  swiperList.value = data
}
// 获取资讯
const getList = async (columnId) => {
  const params = {
    objectParam: {},
    pageNo: pageNo.value,
    pageSize: pageSize.value,
    keyword: keyword.value,
    query: {
      columnId: route.query.id || columnId || switchs.value.data[0].id || '1887325961586761729',
      moduleId: route.query.moduleId || '1',
      passFlag: 1
    },
    tableId: route.query.tableId || 'zy_news_content_1',
    wheres: []
  }
  if (route.query.moduleId == '4') {
    params.wheres = []
  } else {
    params.wheres = [{ columnId: 'zy_news_content_1_show_code_name', queryType: 'EQ', value: '图文' }]
  }
  const { data, code, total: totals } = await api.newsContentList(params)
  if (code === 200) {
    dataList.value = dataList.value.concat(data || [])
    total.value = totals
    loading.value = false
    refreshing.value = false
    if (dataList.value.length >= total.value) {
      finished.value = true
    }
  } else {
    loading.value = false
    finished.value = true
    refreshing.value = false
  }
}
// 跳转资讯详情
const openDetails = (_item) => {
  if (route.query.moduleId == '4') {
    router.push({ path: '/CultureHistoryDetails', query: { id: _item.id } })
  } else {
    router.push({ path: '/NewDetails', query: { id: _item.id } })
  }
}

// 跳转轮播图详情
const openCarouselDetails = (_item) => {
  if (route.query.moduleId == '4') {
    router.push({ path: '/CultureHistoryDetails', query: { id: _item.id } })
  } else {
    router.push({ path: '/NewDetails', query: { id: _item.id } })
  }
}
// 切换tab
const onClickTab = (val) => {
  localStorage.setItem('newTabId', val.name)
  getTabChange(val)
}
const getTabChange = (val) => {
  pageNo.value = 1
  dataList.value = []
  finished.value = false
  loading.value = true
  swiperList.value = []
  getNewTopCarousel(val.name)
  getList(val.name)
}
</script>
<style lang="scss">
.NewList {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: #fff;

  .swiper_box {
    height: 270px;
    overflow: hidden;
    margin-top: 10px;

    .swiper-container {
      width: 100%;
      height: 100%;

      .swiper-item {
        width: 100%;

        .swiper-card {
          width: 100%;
          height: 100%;
          overflow: hidden;

          .swiper-image-container {
            position: relative;
            width: 100%;
            height: 200px;

            .swiper-image {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .swiper-text-box {
            margin-top: -20px;
            margin-bottom: 20px;
            padding: 0 16px;
            width: 100%;
            height: auto;

            .swiper-text-block {
              box-shadow: 0px 2px 25px 1px rgba(116, 136, 163, 0.1);
              background: #FFF;
              border-top-left-radius: 4px;
              border-top-right-radius: 4px;
              border-bottom-right-radius: 4px;
              border-bottom-left-radius: 4px;
              padding: 8px 10px;
              display: flex;
              align-items: center;
              position: relative;

              .swiper-icon {
                height: 34px;
                width: auto;
                margin-right: 7px;
              }

              .swiper-title {
                font-weight: 600;
                color: #333;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                display: -webkit-box;
                overflow: hidden;
                word-wrap: break-word;
                text-overflow: ellipsis;
                white-space: normal !important;
                font-size: 16px;
                line-height: 22px;
              }
            }
          }
        }
      }
    }
  }

  .list_box {
    padding: 20px 12px;
    background-color: #fff;
    border-top: 1px solid #F4F4F4;

    .list_item {
      display: flex;
      align-items: flex-start;

      .list_item_title {
        font-size: 16px;
        font-weight: 600;
        color: #000;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .list_item_time {
        color: #999;
        font-size: 12px;
        margin-top: 8px;
      }

      .list_item_source {
        color: #999;
        font-size: 12px;
        margin-top: 4px;
      }
    }
  }

  .van-tabs--line .van-tabs__wrap {
    height: 30px !important;
  }

  // 自定义指示器样式
  :deep(.van-swipe__indicators) {
    bottom: 15px;

    .van-swipe__indicator {
      background: rgba(255, 255, 255, 0.6);

      &.van-swipe__indicator--active {
        background: #fff;
      }
    }
  }

  .swiper-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 2px;
    margin-bottom: 4px;

    .dot {
      width: 16px;
      height: 3px;
      border-radius: 2px;
      background: #e0e0e0;
      margin: 0 3px;
      transition: background 0.2s;

      &.active {
        background: #4488eb;
      }
    }
  }
}
</style>
