<template>
  <div class="CommitteeLivingRoomDetails">
    <div class="CommitteeLivingRoomDetailsBox">
      <div class="CommitteeLivingRoomDetailsTitle">{{ info.title }}</div>
      <div class="CommitteeLivingRoomDetailsInfo">
        <img src="@/assets/img/headImg.png" alt="" class="info_head_img">
        <div class="info_item">
          <div class="info_item_user">
            <div v-if="info.userVo" class="info_item_user_name">{{ info.userVo?.userName }}</div>
            <div v-if="info.userVo" class="info_item_user_sectorType">{{ info.userVo?.sectorType?.name }}
            </div>
          </div>
          <div class="info_item_position">{{ info.userVo?.position }}</div>
        </div>
      </div>
      <div class="info_visiting_hours_tag">
        <span class="info_visiting_hours">会客时间：{{ info.scheduleDate }}</span>
        <span class="info_visiting_tag">{{ info.scheduleType }}</span>
      </div>
      <div class="info_working_personnel">工作人员：{{ info.staffName }}</div>
      <div class="info_content" v-html="info.content"></div>
    </div>
    <!-- 评论区 -->
    <div class="CommitteeLivingRoomDetailsComments">
      <div class="comments_title">
        <span class="comments_title_bar"></span>留言（{{ commentList.length }}）
      </div>
      <div class="comments_list">
        <template v-if="commentList && commentList.length > 0">
          <div class="comments_list_item" v-for="(item, index) in commentList" :key="index">
            <img src="@/assets/img/headImg.png" alt="" class="comments_item_head_img">
            <div style="width: 100%;">
              <div class="comments_item_top">
                <div class="comments_item_top_user">
                  <span class="comments_item_top_user_name">{{ item.senderUserName }}</span>
                  <div class="comments_item_top_user_tag" v-for="userRoles in item.userRoles" :key="userRoles">
                    {{ userRoles }}</div>
                </div>
                <span class="comments_item_top_status" v-if="item.checkStatus === 0">{{ item.checkStatus != '2' ? '审核中'
                  :
                  '未通过'
                  }}</span>
              </div>
              <div class="comments_item_title_content">
                <div class="comments_item_title">{{ item.title }}</div>
                <div class="comments_item_content">{{ item.content }}</div>
                <div class="comments_item_time">{{ formatDate(item.createDate, 'YYYY-MM-DD HH:mm') }}</div>
              </div>
              <div v-if="item.answers && item.answers.length" class="comments_reply_list">
                <div class="comments_reply_list_item" v-for="reply in item.answers" :key="reply.id">
                  <img src="@/assets/img/headImg.png" alt="" class="comments_reply_item_head_img">
                  <div>
                    <div class="comments_reply_item_user">{{ reply.answerUserName }} </div>
                    <div class="comments_reply_item_content">{{ reply.content }}</div>
                    <span class="comments_reply_item_time">{{ formatDate(reply.answerTime, 'YYYY-MM-DD HH:mm') }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="comments_list_item_more">暂无数据</div>
        </template>
      </div>
    </div>
    <div class="floating-message-btn" @click="goToMessage" v-if="info.status == '进行中'">留言</div>
  </div>
</template>
<script>
export default { name: 'CommitteeLivingRoomDetails' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { formatDate } from '@/assets/js/utils.js'
const route = useRoute()
const router = useRouter()
const title = ref('委员会客厅详情')
const info = ref({})
const commentList = ref([])
// const user = ref(JSON.parse(sessionStorage.getItem('user')) || '')
onMounted(() => {
  if (title.value) {
    document.title = title.value
  }
  getInfo()
})
// 获取详情
const getInfo = async () => {
  const { data } = await api.officeOnlineTopicInfo({ detailId: route.query.id })
  info.value = data
  getCommentList()
}
// 获取评论列表
const getCommentList = async () => {
  const { data } = await api.themeLetterWithAnswer({
    pageNo: 1,
    pageSize: 15,
    query: {
      businessCode: 'schedule_reception',
      businessId: route.query.id
    }
  })
  commentList.value = data
}
// 打开留言
const goToMessage = () => {
  router.push({ name: 'CommitteeLivingRoomMessage', query: { id: route.query.id, receiverId: route.query.receiverId } })
}
</script>
<style lang="scss">
.CommitteeLivingRoomDetails {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: #fff;

  .CommitteeLivingRoomDetailsBox {
    padding: 16px;

    .CommitteeLivingRoomDetailsTitle {
      font-size: 20px;
      font-weight: bold;
      margin-bottom: 10px;
    }

    .CommitteeLivingRoomDetailsInfo {
      padding: 10px 15px;
      border-radius: 4px;
      background: #f8f9fa;
      display: flex;
      align-items: center;

      .info_head_img {
        width: 46px;
        height: 46px;
        border-radius: 50%;
      }

      .info_item {
        margin-left: 10px;

        .info_item_user {
          display: flex;
          align-items: center;
          margin-bottom: 5px;

          .info_item_user_name {
            font-size: 16px;
            font-weight: bold;
            color: #333333;
            margin-right: 10px;
          }

          .info_item_user_sectorType {
            font-size: 14px;
            font-weight: 400;
            color: #999999;
          }
        }

        .info_item_position {
          font-size: 14px;
          font-weight: 400;
          color: #666666;
        }
      }
    }

    .info_visiting_hours_tag {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 12px;

      .info_visiting_hours {
        font-size: 14px;
        font-weight: 400;
        color: #333333;
      }

      .info_visiting_tag {
        background-color: #fdf7eb;
        font-weight: 400;
        color: #f6931c;
        padding: 3px 10px;
        font-size: 14px;
      }
    }

    .info_working_personnel {
      font-size: 14px;
      font-weight: 400;
      color: #333333;
      margin-top: 10px;
    }

    .info_content {
      margin-top: 10px;
      font-size: 17px;
      color: rgb(51, 51, 51);
      line-height: 28px;
    }
  }

  .CommitteeLivingRoomDetailsComments {
    border-top: 10px solid rgb(248, 248, 248);
    background: #fff;
    padding: 16px;

    .comments_title {
      display: flex;
      align-items: center;
      font-weight: bold;
      font-size: 16px;

      .comments_title_bar {
        display: inline-block;
        width: 4px;
        height: 18px;
        background: #2267d8;
        border-radius: 2px;
        margin-right: 8px;
      }
    }

    .comments_list {
      display: flex;
      flex-direction: column;
      padding-bottom: 30px;
      margin-bottom: 16px;

      .comments_list_item {
        display: flex;
        align-items: flex-start;
        width: auto;
        padding: 15px 0px;
        border-bottom: 1px solid rgb(238, 238, 238);

        .comments_item_head_img {
          width: 46px;
          height: 46px;
          border-radius: 50%;
          margin-right: 8px;
          margin-top: 2px;
        }

        .comments_item_top {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .comments_item_top_user {
            display: flex;
            align-items: center;

            .comments_item_top_user_name {
              font-weight: bold;
              color: #333333;
              margin-right: 10px;
              font-size: 16px;
            }

            .comments_item_top_user_tag {
              padding: 0 10px;
              background-color: #fef7ec;
              font-weight: 400;
              color: #f6931c;
            }
          }

          .comments_item_top_status {
            font-size: 14px;
            color: #999999;
          }
        }

        .comments_item_title_content {

          .comments_item_title {
            font-size: 16px;
            font-weight: bold;
            color: #333333;
            margin-top: 10px;
            margin-bottom: 10px;
          }

          .comments_item_content {
            font-size: 15px;
            font-weight: 400;
            color: #333333;
            line-height: 1.5;
            margin: 0px 0px 10px 0px;
          }

          .comments_item_time {
            font-weight: 400;
            color: #999999;
            font-size: 14px;
          }
        }

        .comments_reply_list {
          background: #f7f8fa;
          border-radius: 8px;
          margin: 10px 0 0 0;
          padding: 10px 15px;

          .comments_reply_list_item {
            font-size: 14px;
            color: #333;
            display: flex;
            align-items: flex-start;

            .comments_reply_item_head_img {
              width: 30px;
              height: 30px;
              border-radius: 50%;
              margin-right: 8px;
            }

            .comments_reply_item_user {
              font-weight: bold;
              color: #333333;
              font-size: 16px;
              margin-bottom: 8px;
            }

            .comments_reply_item_content {
              color: #333333;
              line-height: 1.8;
              font-size: 15px;
              margin-bottom: 5px;
            }

            .comments_reply_item_time {
              font-weight: 400;
              color: #999999;
              font-size: 14px;
            }
          }
        }
      }

      .comments_list_item_more {
        text-align: center;
        color: #bbb;
        font-size: 13px;
        margin-top: 8px;
      }
    }
  }

  .floating-message-btn {
    position: fixed;
    right: 20px;
    bottom: 25px;
    z-index: 100;
    background: #2267d8;
    color: #fff;
    font-size: 18px;
    border-radius: 25px;
    padding: 0 40px;
    height: 48px;
    line-height: 48px;
  }
}
</style>
