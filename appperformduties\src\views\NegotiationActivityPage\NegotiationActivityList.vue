<template>
  <div class="NegotiationActivityList">
    <van-sticky>
      <van-search v-model="keyword" shape="round" placeholder="请输入关键词" @search="onSearch"></van-search>
    </van-sticky>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list v-model:loading="loading" :finished="finished" :finished-text="dataList.length == 0 ? '' : '没有更多了'"
        offset="52" @load="onLoad">
        <div v-for="item in dataList" :key="item.id" class="list_box" @click="openDetails(item)">
          <div class="list_item">
            <div class="list_item_header" v-if="item.activityStatus">
              <div class="list_item_status">{{ item.activityStatus }}</div>
              <div style="flex: 1;"></div>
            </div>
            <div class="list_item_title">{{ item.title }}</div>
            <div class="list_item_shape">协商形式：{{ item.consultShape?.name }}</div>
            <div class="list_item_time">协商时间：{{ formatDate(item.pubTime, 'YYYY-MM-DD') }}</div>
          </div>
        </div>
        <van-empty v-if="dataList.length == 0 && !loading" description="暂无数据" />
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
export default { name: 'NegotiationActivityList' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import { formatDate } from '@/assets/js/utils.js'
import { useRouter, useRoute } from 'vue-router'
const route = useRoute()
const router = useRouter()

const title = ref('协商列表')
const keyword = ref('')//搜索关键词
const loading = ref(false)//加载中
const finished = ref(false)//是否加载完成
const refreshing = ref(false)//下拉刷新
const pageNo = ref(1)//当前页码
const pageSize = ref(15)//每页条数
const total = ref(0)//总条数
const dataList = ref([])
onMounted(() => {
  if (title.value) {
    document.title = title.value
  }
  setTimeout(() => {
    onRefresh()
  }, 100)
})
const onSearch = () => {
  onRefresh()
}
const onRefresh = () => {
  pageNo.value = 1
  dataList.value = []
  finished.value = false
  loading.value = true
  getList()
}
const onLoad = () => {
  if (dataList.value.length < total.value) {
    pageNo.value++
    getList()
  } else {
    finished.value = true
  }
}
// 获取置顶资讯
const getList = async () => {
  const { code, data, total: totals } = await api.consultActivityList({
    keyword: keyword.value,
    pageNo: pageNo.value,
    pageSize: pageSize.value,
    year: 2025,
    objectParam: { type: route.query.type }
  })
  if (code === 200) {
    if (route.query.type == 2) {
      dataList.value = []
      total.value = 0
      loading.value = false
      refreshing.value = false
      if (dataList.value.length >= total.value) {
        finished.value = true
      }
    } else {
      dataList.value = dataList.value.concat(data || [])
      total.value = totals
      loading.value = false
      refreshing.value = false
      if (dataList.value.length >= total.value) {
        finished.value = true
      }
    }
  } else {
    loading.value = false
    finished.value = true
    refreshing.value = false
  }
}
// 跳转资讯详情
const openDetails = (_item) => {
  router.push({ path: '/NegotiationActivityPage', query: { id: _item.id } })
}
</script>
<style lang="scss">
.NegotiationActivityList {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: #F4F5F9;

  .list_box {
    // padding: 10px 12px;
    background-color: #fff;
    border-top: 1px solid #F4F4F4;
    margin: 12px 16px;
    box-shadow: 0px 2 10px 0px rgba(24, 64, 118, 0.08);
    border-radius: 4px;

    .list_item {
      padding: 10px 12px;
      position: relative;

      .list_item_header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-bottom: 10px;

        .list_item_status {
          background: rgba(48, 136, 254, 0.08);
          color: #3088FE;
          border-radius: 4px;
          padding: 2px 8px;
          font-size: 14px;
          margin-right: 8px;
        }
      }

      .list_item_title {
        font-size: 16px;
        margin-top: 8px;
        font-weight: 600;
        color: #000;
      }

      .list_item_shape {
        font-size: 14px;
        color: #979DA3;
        margin-top: 10px;
      }

      .list_item_time {
        font-size: 14px;
        color: #979DA3;
        margin-top: 8px;
      }
    }
  }
}
</style>
