{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = \"function\" == typeof Symbol ? Symbol : {}, n = r.iterator || \"@@iterator\", o = r.toStringTag || \"@@toStringTag\"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, \"_invoke\", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError(\"Generator is already running\"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = \"next\"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i.return) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, \"constructor\", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", _regeneratorDefine2(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, \"Generator\"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, \"toString\", function () { return \"[object Generator]\"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }\nfunction _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, \"\", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { if (r) i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n;else { var o = function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); }; o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2); } }, _regeneratorDefine2(e, r, n, t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref, onMounted, onBeforeUnmount } from 'vue';\nimport { useRouter, useRoute } from 'vue-router';\nimport config from '@/config';\nimport { showToast } from 'vant';\nimport store from '@/store';\nimport { getAuthCode, removeTag } from '@/utils/utils';\nimport { isInThirdPartyApp, getDefaultUser, getEnvironmentType } from '@/utils/environment';\nvar __default__ = {\n  name: 'LayoutHome'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var router = useRouter();\n    var route = useRoute();\n    var carouselList = ref([]);\n    var loading = ref(false);\n    var carouselIndex = ref(0);\n    var carouselTimer = null;\n    // const touchStartX = ref(0)\n    // const touchEndX = ref(0)\n\n    var isPreheat = ref('');\n    var defaultActivityId = ref('');\n    var negotiationTopics = ref([]);\n    var negotiationTopicsLoading = ref(false);\n    var videoGraphicLiveLoading = ref(false);\n    var videoGraphicLive = ref([{\n      img: require('@/assets/img/bg_survey_list_zx.png'),\n      title: '市政协召开全市“深化六个改革”重点工作宣讲会',\n      id: 1\n    }, {\n      img: require('@/assets/img/icon_top_bg.png'),\n      title: '政协委员积极建言献策 助力城市发展',\n      id: 2\n    }, {\n      img: require('@/assets/img/icon_top_logo.png'),\n      title: '协商议政新模式 推动社会治理创新',\n      id: 3\n    }]);\n    var vgIndex = ref(0);\n    var vgTimer = null;\n    var vgTouchStartX = ref(0);\n    var vgTouchEndX = ref(0);\n    var negotiationTopicsCollectLoading = ref(false);\n    var negotiationtopicsCollectList = ref([]);\n    var culturalHistoryList = ref([]);\n    var loadingPage = ref(true);\n    onMounted(function () {\n      document.title = '西安政协';\n      // 获取i西安token\n      getIXiAnToken();\n    });\n    onBeforeUnmount(function () {\n      stopNewsCarousel();\n      stopVGCarousel();\n    });\n    // 获取i西安的token\n    var getIXiAnToken = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee() {\n        var inThirdPartyApp, envType, publicUser, defaultUser, res, _t;\n        return _regenerator().w(function (_context) {\n          while (1) switch (_context.n) {\n            case 0:\n              loadingPage.value = true;\n\n              // 检测环境，如果不在第三方app中，使用默认账号\n              inThirdPartyApp = isInThirdPartyApp();\n              envType = getEnvironmentType();\n              console.log(`环境检测结果: ${inThirdPartyApp ? '第三方app' : '普通浏览器'} (${envType})`);\n              if (inThirdPartyApp) {\n                _context.n = 4;\n                break;\n              }\n              console.log('当前不在第三方app环境中，使用默认账号登录');\n              publicUser = sessionStorage.getItem('public_user');\n              if (publicUser) {\n                _context.n = 1;\n                break;\n              }\n              defaultUser = getDefaultUser();\n              autoLogin(defaultUser);\n              _context.n = 3;\n              break;\n            case 1:\n              _context.n = 2;\n              return getTopCarousel();\n            case 2:\n              loadingPage.value = false;\n            case 3:\n              return _context.a(2);\n            case 4:\n              _context.p = 4;\n              _context.n = 5;\n              return api.ixaToekn({});\n            case 5:\n              res = _context.v;\n              if (res.data.data) {\n                getIXiAnCode(res.data.data.jsapiToken);\n              } else {\n                console.warn('JsapiToken获取失败，尝试使用默认账号');\n                loadingPage.value = false;\n                setTimeout(function () {\n                  showToast('JsapiToken获取失败，使用默认账号登录');\n                  var defaultUser = getDefaultUser();\n                  autoLogin(defaultUser);\n                }, 500);\n              }\n              _context.n = 7;\n              break;\n            case 6:\n              _context.p = 6;\n              _t = _context.v;\n              console.error('获取JsapiToken异常:', _t);\n              loadingPage.value = false;\n              setTimeout(function () {\n                showToast('网络异常，使用默认账号登录');\n                var defaultUser = getDefaultUser();\n                autoLogin(defaultUser);\n              }, 500);\n            case 7:\n              return _context.a(2);\n          }\n        }, _callee, null, [[4, 6]]);\n      }));\n      return function getIXiAnToken() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    // 获取i西安的code\n    var getIXiAnCode = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2(jsapiToken) {\n        var defaultUser, _defaultUser2;\n        return _regenerator().w(function (_context2) {\n          while (1) switch (_context2.n) {\n            case 0:\n              if (isInThirdPartyApp()) {\n                _context2.n = 1;\n                break;\n              }\n              console.log('getIXiAnCode: 当前不在第三方app环境中，使用默认账号');\n              defaultUser = getDefaultUser();\n              autoLogin(defaultUser);\n              return _context2.a(2);\n            case 1:\n              try {\n                getAuthCode({\n                  // \"appId\": \"9de99f6934b94cf38c75ce40c9ddf8a2\", //测试应用id\n                  \"appId\": \"b105ea5a2fca4b28afdd52558952552f\",\n                  //正式应用id\n                  \"forceScopes\": ['ixa_user_info'],\n                  //授权的能力标识数组，\n                  \"jsapiToken\": jsapiToken //应用授权的请求码\n                }, function (result) {\n                  if (result.code === 0) {\n                    console.log('✅ 第三方app授权成功');\n                    getIXiAnUser(result.data.authCode);\n                  } else {\n                    console.error('❌ 第三方app授权失败:', result.message);\n                    // 如果授权失败，降级到默认账号\n                    console.log('授权失败，降级使用默认账号登录');\n                    showToast('授权失败，使用默认账号登录');\n                    var _defaultUser = getDefaultUser();\n                    autoLogin(_defaultUser);\n                  }\n                });\n              } catch (error) {\n                console.error('getAuthCode调用异常:', error);\n                console.log('getAuthCode异常，降级使用默认账号登录');\n                showToast('授权异常，使用默认账号登录');\n                _defaultUser2 = getDefaultUser();\n                autoLogin(_defaultUser2);\n              }\n            case 2:\n              return _context2.a(2);\n          }\n        }, _callee2);\n      }));\n      return function getIXiAnCode(_x) {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    // 获取i西安的用户信息\n    var getIXiAnUser = /*#__PURE__*/function () {\n      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3(code) {\n        var res, ixaUser;\n        return _regenerator().w(function (_context3) {\n          while (1) switch (_context3.n) {\n            case 0:\n              _context3.n = 1;\n              return api.ixaUser({\n                authCode: code\n              });\n            case 1:\n              res = _context3.v;\n              console.log('用户信息-->' + JSON.stringify(res));\n              ixaUser = res.data.data.userInfo;\n              autoLogin(ixaUser);\n              localStorage.setItem('ixaUser', JSON.stringify(ixaUser));\n            case 2:\n              return _context3.a(2);\n          }\n        }, _callee3);\n      }));\n      return function getIXiAnUser(_x2) {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n    // 公众登录\n    var autoLogin = /*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee4(user) {\n        var _yield$api$login, data, _t2;\n        return _regenerator().w(function (_context4) {\n          while (1) switch (_context4.n) {\n            case 0:\n              if (user.userName) {\n                _context4.n = 1;\n                break;\n              }\n              loadingPage.value = false;\n              showToast('请您先进行实名认证！');\n              setTimeout(function () {\n                window.history.back();\n              }, 800);\n              return _context4.a(2);\n            case 1:\n              _context4.p = 1;\n              _context4.n = 2;\n              return api.login({\n                grant_type: 'anonymoussso',\n                userName: user.userName,\n                mobile: user.phone\n              }, {\n                headers: {\n                  authorization: 'basic enlzb2Z0Onp5c29mdCo2MDc5'\n                }\n              });\n            case 2:\n              _yield$api$login = _context4.v;\n              data = _yield$api$login.data;\n              sessionStorage.setItem('token', data.token);\n              sessionStorage.setItem('refresh_token', `bearer ${data.refreshToken.value}`);\n              sessionStorage.setItem('expires', data.expires_in);\n              sessionStorage.setItem('expiration', data.refreshToken.expiration);\n              sessionStorage.setItem('verify', data.passwordElementMatchReg ? 1 : 0);\n              loadingPage.value = false; // 登录成功后loading消失\n              _context4.n = 3;\n              return store.dispatch('loginUser');\n            case 3:\n              _context4.n = 4;\n              return getTopCarousel();\n            case 4:\n              _context4.n = 6;\n              break;\n            case 5:\n              _context4.p = 5;\n              _t2 = _context4.v;\n              loadingPage.value = false; // 登录失败也消失loading\n              showToast(_t2.message || '登录失败');\n            case 6:\n              return _context4.a(2);\n          }\n        }, _callee4, null, [[1, 5]]);\n      }));\n      return function autoLogin(_x3) {\n        return _ref5.apply(this, arguments);\n      };\n    }();\n    // 获取置顶资讯\n    var getTopCarousel = /*#__PURE__*/function () {\n      var _ref6 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee5() {\n        var params, _yield$api$newsConten, data;\n        return _regenerator().w(function (_context5) {\n          while (1) switch (_context5.n) {\n            case 0:\n              loading.value = true;\n              params = {\n                objectParam: {},\n                pageNo: 1,\n                pageSize: 5,\n                keyword: '',\n                query: {\n                  columnId: route.query.columnId || '1887325961586761729',\n                  moduleId: 1,\n                  passFlag: 1\n                },\n                wheres: [{\n                  columnId: 'zy_news_content_1_is_top',\n                  queryType: 'EQ',\n                  value: '1'\n                }],\n                tableId: 'zy_news_content_1'\n              };\n              _context5.n = 1;\n              return api.newsContentList(params);\n            case 1:\n              _yield$api$newsConten = _context5.v;\n              data = _yield$api$newsConten.data;\n              carouselList.value = data;\n              loading.value = false;\n              startNewsCarousel();\n              getConfig();\n            case 2:\n              return _context5.a(2);\n          }\n        }, _callee5);\n      }));\n      return function getTopCarousel() {\n        return _ref6.apply(this, arguments);\n      };\n    }();\n    // 获取配置\n    var getConfig = /*#__PURE__*/function () {\n      var _ref7 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee6() {\n        var _yield$api$globalRead, data;\n        return _regenerator().w(function (_context6) {\n          while (1) switch (_context6.n) {\n            case 0:\n              _context6.n = 1;\n              return api.globalReadOpenConfig({\n                codes: ['ixian_preheat']\n              });\n            case 1:\n              _yield$api$globalRead = _context6.v;\n              data = _yield$api$globalRead.data;\n              isPreheat.value = data.ixian_preheat;\n              getConsultActivityList();\n            case 2:\n              return _context6.a(2);\n          }\n        }, _callee6);\n      }));\n      return function getConfig() {\n        return _ref7.apply(this, arguments);\n      };\n    }();\n    // 获取协商活动\n    var getConsultActivityList = /*#__PURE__*/function () {\n      var _ref8 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee7(pageSize) {\n        var params, _yield$api$consultAct, data;\n        return _regenerator().w(function (_context7) {\n          while (1) switch (_context7.n) {\n            case 0:\n              if (!pageSize) {\n                negotiationTopicsLoading.value = true;\n              }\n              params = {\n                pageNo: 1,\n                pageSize: pageSize || 3,\n                year: 2025,\n                objectParam: {}\n              };\n              if (pageSize) {\n                params.objectParam.showPublic = '1';\n              } else {\n                params.objectParam.ifLatest = '1';\n              }\n              _context7.n = 1;\n              return api.consultActivityList(params);\n            case 1:\n              _yield$api$consultAct = _context7.v;\n              data = _yield$api$consultAct.data;\n              if (pageSize) {\n                if (data.length > 0) {\n                  defaultActivityId.value = data[0].id;\n                } else {\n                  showToast('暂无设置当前协商！');\n                }\n              } else {\n                negotiationTopics.value = data;\n                negotiationTopicsLoading.value = false;\n                getVideoGraphicLive();\n                getNegotiationTopicsCollect();\n              }\n            case 2:\n              return _context7.a(2);\n          }\n        }, _callee7);\n      }));\n      return function getConsultActivityList(_x4) {\n        return _ref8.apply(this, arguments);\n      };\n    }();\n    // 获取视频+图文直播\n    var getVideoGraphicLive = /*#__PURE__*/function () {\n      var _ref9 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee8() {\n        return _regenerator().w(function (_context8) {\n          while (1) switch (_context8.n) {\n            case 0:\n              videoGraphicLiveLoading.value = false;\n              startVGCarousel();\n            case 1:\n              return _context8.a(2);\n          }\n        }, _callee8);\n      }));\n      return function getVideoGraphicLive() {\n        return _ref9.apply(this, arguments);\n      };\n    }();\n    // 获取协商议题征集\n    var getNegotiationTopicsCollect = /*#__PURE__*/function () {\n      var _ref0 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee9() {\n        var _yield$api$opinioncol, code, data, now, oneDay, processedData;\n        return _regenerator().w(function (_context9) {\n          while (1) switch (_context9.n) {\n            case 0:\n              negotiationTopicsCollectLoading.value = true;\n              _context9.n = 1;\n              return api.opinioncollectList({\n                pageNo: 1,\n                pageSize: 1,\n                collectStatus: 2,\n                query: {\n                  businessCode: 'discussioncollect'\n                }\n              });\n            case 1:\n              _yield$api$opinioncol = _context9.v;\n              code = _yield$api$opinioncol.code;\n              data = _yield$api$opinioncol.data;\n              if (code === 200) {\n                now = new Date().getTime();\n                oneDay = 24 * 60 * 60 * 1000;\n                processedData = (data || []).map(function (item) {\n                  var start = new Date(item.startDate).getTime();\n                  var end = new Date(item.endDate).getTime();\n                  var statusText = '';\n                  var days = 0;\n                  if (now > end) {\n                    statusText = '征集已结束';\n                    days = 0;\n                  } else if (now > start) {\n                    statusText = '征集结束剩余：';\n                    days = Math.floor((end - now) / oneDay);\n                  } else {\n                    statusText = '征集开始还有：';\n                    days = Math.floor((start - now) / oneDay);\n                  }\n                  var canJoin = now > start && now < end && item.isJoinName === '未参与';\n                  return _objectSpread(_objectSpread({}, item), {}, {\n                    statusText,\n                    days,\n                    canJoin\n                  });\n                });\n                negotiationtopicsCollectList.value = negotiationtopicsCollectList.value.concat(processedData);\n                if (negotiationtopicsCollectList.value[0]) {\n                  negotiationtopicsCollectList.value[0].content = removeTag(negotiationtopicsCollectList.value[0].content);\n                }\n                negotiationTopicsCollectLoading.value = false;\n                getCulturalHistoricalCollection();\n              }\n            case 2:\n              return _context9.a(2);\n          }\n        }, _callee9);\n      }));\n      return function getNegotiationTopicsCollect() {\n        return _ref0.apply(this, arguments);\n      };\n    }();\n    // 通用轮播控制方法\n    var startCarousel = function startCarousel(timer, index, list, callback) {\n      if (timer) clearInterval(timer);\n      return setInterval(function () {\n        index.value = (index.value + 1) % list.value.length;\n      }, 30000);\n    };\n    var stopCarousel = function stopCarousel(timer) {\n      if (timer) {\n        clearInterval(timer);\n        return null;\n      }\n      return null;\n    };\n    var goToCarousel = function goToCarousel(idx, index, timer, list, callback) {\n      timer = stopCarousel(timer);\n      index.value = idx;\n      return startCarousel(timer, index, list, callback);\n    };\n    var prevCarousel = function prevCarousel(index, list) {\n      index.value = (index.value - 1 + list.value.length) % list.value.length;\n    };\n    var nextCarousel = function nextCarousel(index, list) {\n      index.value = (index.value + 1) % list.value.length;\n    };\n    // 通用触摸处理\n    var onTouchStart = function onTouchStart(e, touchStartX, touchEndX) {\n      touchStartX.value = e.touches[0].clientX;\n      touchEndX.value = e.touches[0].clientX;\n    };\n    var onTouchMove = function onTouchMove(e, touchEndX) {\n      touchEndX.value = e.touches[0].clientX;\n    };\n    var onTouchEnd = function onTouchEnd(e, touchStartX, touchEndX, stopFn, nextFn, prevFn, startFn) {\n      var deltaX = touchEndX.value - touchStartX.value;\n      if (Math.abs(deltaX) > 50) {\n        stopFn();\n        if (deltaX < 0) {\n          nextFn();\n        } else {\n          prevFn();\n        }\n        startFn();\n      }\n      touchStartX.value = 0;\n      touchEndX.value = 0;\n    };\n    // 资讯轮播图相关方法\n    var startNewsCarousel = function startNewsCarousel() {\n      carouselTimer = startCarousel(carouselTimer, carouselIndex, carouselList);\n    };\n    var stopNewsCarousel = function stopNewsCarousel() {\n      carouselTimer = stopCarousel(carouselTimer);\n    };\n    // 打开资讯详情\n    var onCarouselClick = function onCarouselClick(row) {\n      router.push({\n        path: '/NewDetails',\n        query: {\n          id: row.id\n        }\n      });\n    };\n    // 打开全部资讯\n    var openNewMore = function openNewMore() {\n      router.push({\n        path: '/NewList',\n        query: {\n          utype: '1'\n        }\n      });\n    };\n    // 跳转协商活动\n    var openActivety = /*#__PURE__*/function () {\n      var _ref1 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee0(_item) {\n        return _regenerator().w(function (_context0) {\n          while (1) switch (_context0.n) {\n            case 0:\n              if (!_item) {\n                _context0.n = 1;\n                break;\n              }\n              router.push({\n                path: '/NegotiationActivityPage',\n                query: {\n                  id: _item.id\n                }\n              });\n              _context0.n = 3;\n              break;\n            case 1:\n              _context0.n = 2;\n              return getConsultActivityList(1);\n            case 2:\n              if (defaultActivityId.value) {\n                router.push({\n                  path: '/NegotiationActivityPage',\n                  query: {\n                    id: defaultActivityId.value\n                  }\n                });\n              }\n            case 3:\n              return _context0.a(2);\n          }\n        }, _callee0);\n      }));\n      return function openActivety(_x5) {\n        return _ref1.apply(this, arguments);\n      };\n    }();\n    // 跳转网络议政\n    var openNetwork = function openNetwork() {\n      router.push({\n        path: '/NetworkDiscussList'\n      });\n    };\n    // 跳转问卷调查\n    var openSurvey = function openSurvey() {\n      router.push({\n        path: '/QuestionnaireList'\n      });\n    };\n    // 视频+图文直播轮播图相关方法\n    var startVGCarousel = function startVGCarousel() {\n      vgTimer = startCarousel(vgTimer, vgIndex, videoGraphicLive);\n    };\n    var stopVGCarousel = function stopVGCarousel() {\n      vgTimer = stopCarousel(vgTimer);\n    };\n    var goToVGCarousel = function goToVGCarousel(idx) {\n      vgTimer = goToCarousel(idx, vgIndex, vgTimer, videoGraphicLive);\n    };\n    var prevVGCarousel = function prevVGCarousel() {\n      prevCarousel(vgIndex, videoGraphicLive);\n    };\n    var nextVGCarousel = function nextVGCarousel() {\n      nextCarousel(vgIndex, videoGraphicLive);\n    };\n    var onVGTouchStart = function onVGTouchStart(e) {\n      onTouchStart(e, vgTouchStartX, vgTouchEndX);\n    };\n    var onVGTouchMove = function onVGTouchMove(e) {\n      onTouchMove(e, vgTouchEndX);\n    };\n    var onVGTouchEnd = function onVGTouchEnd(e) {\n      onTouchEnd(e, vgTouchStartX, vgTouchEndX, stopVGCarousel, nextVGCarousel, prevVGCarousel, startVGCarousel);\n    };\n    // 跳转视频+图文直播详情\n    var onVGCarouselClick = function onVGCarouselClick(_item) {\n      // 跳转或弹窗逻辑\n      // router.push({ path: '/VideoDetail', query: { id: _item.id } })\n    };\n    // 跳转委员会客厅\n    var openCommitteeLivingRoom = function openCommitteeLivingRoom() {\n      // router.push({ path: '/CommitteeLivingRoomList' })\n    };\n    // 跳转协商议题征集详情\n    var openCollectInfo = function openCollectInfo(_item) {\n      router.push({\n        path: '/NegotiationTopicsDetails',\n        query: {\n          id: _item.id\n        }\n      });\n    };\n    // 跳转政协概况\n    var openOverview = function openOverview() {\n      window.open('https://www.xa-cppcc.gov.cn/zxgk/zyzn/4.html', '_blank');\n    };\n    // 跳转文史资料\n    var openHistory = function openHistory() {\n      router.push({\n        path: '/CultureHistory'\n      });\n    };\n    // 获取文史资料征集\n    var getCulturalHistoricalCollection = /*#__PURE__*/function () {\n      var _ref10 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee1() {\n        var params, _yield$api$notificati, data, code, icons;\n        return _regenerator().w(function (_context1) {\n          while (1) switch (_context1.n) {\n            case 0:\n              params = {\n                pageNo: 1,\n                pageSize: 2,\n                query: {\n                  channelId: '1935275534502072322',\n                  isDraft: 0\n                },\n                tableId: 'id_message_notification',\n                wheres: []\n              };\n              _context1.n = 1;\n              return api.notificationList(params);\n            case 1:\n              _yield$api$notificati = _context1.v;\n              data = _yield$api$notificati.data;\n              code = _yield$api$notificati.code;\n              if (code == 200) {\n                icons = [require('@/assets/img/icon_culture_history01.png'), require('@/assets/img/icon_culture_history02.png')];\n                culturalHistoryList.value = (data || []).map(function (item, idx) {\n                  return _objectSpread(_objectSpread({}, item), {}, {\n                    icon: icons[idx]\n                  });\n                });\n              } else {\n                showToast('文史资料征集请求失败！');\n              }\n            case 2:\n              return _context1.a(2);\n          }\n        }, _callee1);\n      }));\n      return function getCulturalHistoricalCollection() {\n        return _ref10.apply(this, arguments);\n      };\n    }();\n    // 文史资料征集进详情\n    var goToCulturalDetail = function goToCulturalDetail(_item) {\n      router.push({\n        path: '/NoticeDetails',\n        query: {\n          id: _item.id\n        }\n      });\n    };\n    // 打开文史资料征集进全部\n    var openHistoricalCollecMore = function openHistoricalCollecMore() {\n      router.push({\n        path: '/NoticeList',\n        query: {}\n      });\n    };\n    // 提交文史资料\n    var openSubmitHistorical = function openSubmitHistorical() {\n      router.push({\n        path: '/CultureHistoryAdd'\n      });\n    };\n    var __returned__ = {\n      router,\n      route,\n      carouselList,\n      loading,\n      carouselIndex,\n      get carouselTimer() {\n        return carouselTimer;\n      },\n      set carouselTimer(v) {\n        carouselTimer = v;\n      },\n      isPreheat,\n      defaultActivityId,\n      negotiationTopics,\n      negotiationTopicsLoading,\n      videoGraphicLiveLoading,\n      videoGraphicLive,\n      vgIndex,\n      get vgTimer() {\n        return vgTimer;\n      },\n      set vgTimer(v) {\n        vgTimer = v;\n      },\n      vgTouchStartX,\n      vgTouchEndX,\n      negotiationTopicsCollectLoading,\n      negotiationtopicsCollectList,\n      culturalHistoryList,\n      loadingPage,\n      getIXiAnToken,\n      getIXiAnCode,\n      getIXiAnUser,\n      autoLogin,\n      getTopCarousel,\n      getConfig,\n      getConsultActivityList,\n      getVideoGraphicLive,\n      getNegotiationTopicsCollect,\n      startCarousel,\n      stopCarousel,\n      goToCarousel,\n      prevCarousel,\n      nextCarousel,\n      onTouchStart,\n      onTouchMove,\n      onTouchEnd,\n      startNewsCarousel,\n      stopNewsCarousel,\n      onCarouselClick,\n      openNewMore,\n      openActivety,\n      openNetwork,\n      openSurvey,\n      startVGCarousel,\n      stopVGCarousel,\n      goToVGCarousel,\n      prevVGCarousel,\n      nextVGCarousel,\n      onVGTouchStart,\n      onVGTouchMove,\n      onVGTouchEnd,\n      onVGCarouselClick,\n      openCommitteeLivingRoom,\n      openCollectInfo,\n      openOverview,\n      openHistory,\n      getCulturalHistoricalCollection,\n      goToCulturalDetail,\n      openHistoricalCollecMore,\n      openSubmitHistorical,\n      get api() {\n        return api;\n      },\n      ref,\n      onMounted,\n      onBeforeUnmount,\n      get useRouter() {\n        return useRouter;\n      },\n      get useRoute() {\n        return useRoute;\n      },\n      get config() {\n        return config;\n      },\n      get showToast() {\n        return showToast;\n      },\n      get store() {\n        return store;\n      },\n      get getAuthCode() {\n        return getAuthCode;\n      },\n      get removeTag() {\n        return removeTag;\n      },\n      get isInThirdPartyApp() {\n        return isInThirdPartyApp;\n      },\n      get getDefaultUser() {\n        return getDefaultUser;\n      },\n      get getEnvironmentType() {\n        return getEnvironmentType;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["e", "t", "r", "Symbol", "n", "iterator", "o", "toStringTag", "i", "c", "prototype", "Generator", "u", "Object", "create", "_regeneratorDefine2", "f", "p", "y", "G", "v", "a", "d", "bind", "length", "l", "TypeError", "call", "done", "value", "return", "GeneratorFunction", "GeneratorFunctionPrototype", "getPrototypeOf", "setPrototypeOf", "__proto__", "displayName", "_regenerator", "w", "m", "defineProperty", "_regeneratorDefine", "enumerable", "configurable", "writable", "_invoke", "asyncGeneratorStep", "Promise", "resolve", "then", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "ref", "onMounted", "onBeforeUnmount", "useRouter", "useRoute", "config", "showToast", "store", "getAuthCode", "removeTag", "isInThirdPartyApp", "getDefaultUser", "getEnvironmentType", "__default__", "name", "router", "route", "carouselList", "loading", "carouselIndex", "carouselTimer", "isPreheat", "defaultActivityId", "negotiationTopics", "negotiationTopicsLoading", "videoGraphicLiveLoading", "videoGraphicLive", "img", "require", "title", "id", "vgIndex", "vgTimer", "vgTouchStartX", "vgTouchEndX", "negotiationTopicsCollectLoading", "negotiationtopicsCollectList", "culturalHistoryList", "loadingPage", "document", "getIXiAnToken", "stopNewsCarousel", "stopVGCarousel", "_ref2", "_callee", "inThirdPartyApp", "envType", "publicUser", "defaultUser", "res", "_t", "_context", "console", "log", "sessionStorage", "getItem", "autoLogin", "getTopCarousel", "ixaToekn", "data", "getIXiAnCode", "jsapiToken", "warn", "setTimeout", "error", "_ref3", "_callee2", "_defaultUser2", "_context2", "result", "code", "getIXiAnUser", "authCode", "message", "_x", "_ref4", "_callee3", "ixaUser", "_context3", "JSON", "stringify", "userInfo", "localStorage", "setItem", "_x2", "_ref5", "_callee4", "user", "_yield$api$login", "_t2", "_context4", "userName", "window", "history", "back", "login", "grant_type", "mobile", "phone", "headers", "authorization", "token", "refreshToken", "expires_in", "expiration", "passwordElementMatchReg", "dispatch", "_x3", "_ref6", "_callee5", "params", "_yield$api$newsConten", "_context5", "objectParam", "pageNo", "pageSize", "keyword", "query", "columnId", "moduleId", "passFlag", "wheres", "queryType", "tableId", "newsContentList", "startNewsCarousel", "getConfig", "_ref7", "_callee6", "_yield$api$globalRead", "_context6", "globalReadOpenConfig", "codes", "ixian_preheat", "getConsultActivityList", "_ref8", "_callee7", "_yield$api$consultAct", "_context7", "year", "showPublic", "ifLatest", "consultActivityList", "getVideoGraphicLive", "getNegotiationTopicsCollect", "_x4", "_ref9", "_callee8", "_context8", "startVGCarousel", "_ref0", "_callee9", "_yield$api$opinioncol", "now", "oneDay", "processedData", "_context9", "opinioncollectList", "collectStatus", "businessCode", "Date", "getTime", "map", "item", "start", "startDate", "end", "endDate", "statusText", "days", "Math", "floor", "canJoin", "isJoinName", "_objectSpread", "concat", "content", "getCulturalHistoricalCollection", "startCarousel", "timer", "index", "list", "callback", "clearInterval", "setInterval", "stopCarousel", "goToCarousel", "idx", "prevCarousel", "nextCarousel", "onTouchStart", "touchStartX", "touchEndX", "touches", "clientX", "onTouchMove", "onTouchEnd", "stopFn", "nextFn", "prevFn", "startFn", "deltaX", "abs", "onCarouselClick", "row", "push", "path", "openNewMore", "utype", "openActivety", "_ref1", "_callee0", "_item", "_context0", "_x5", "openNetwork", "openSurvey", "goToVGCarousel", "prevVGCarousel", "nextVGCarousel", "onVGTouchStart", "onVGTouchMove", "onVGTouchEnd", "onVGCarouselClick", "openCommitteeLivingRoom", "openCollectInfo", "openOverview", "open", "openHistory", "_ref10", "_callee1", "_yield$api$notificati", "icons", "_context1", "channelId", "isDraft", "notificationList", "icon", "goToCulturalDetail", "openHistoricalCollecMore", "openSubmitHistorical"], "sources": ["D:/zy/xm/h5/i西安/appperformduties/src/views/LayoutHome/LayoutHome.vue"], "sourcesContent": ["<template>\r\n  <div class=\"LayoutHome\">\r\n    <!-- 全局loading遮罩 -->\r\n    <div v-if=\"loadingPage\"\r\n      class=\"global-loading-mask flex_box flex_flex_direction_column flex_align_center flex_justify_content\">\r\n      <div class=\"global-loading-spinner\"></div>\r\n      <div class=\"global-loading-text\">加载中...</div>\r\n    </div>\r\n    <!-- 顶部背景和logo -->\r\n    <div class=\"top_bg\">\r\n      <img src=\"@/assets/img/icon_top_bg.png\" alt=\"\" class=\"bg_img\">\r\n      <div class=\"logo\">\r\n        <img src=\"@/assets/img/icon_top_logo.png\" alt=\"\" style=\"width: 180px;height: 30px;\">\r\n      </div>\r\n      <div class=\"top_text\">\r\n        <img src=\"@/assets/img/icon_top_text.png\" alt=\"\" style=\"width: 277px;height: 55px;\">\r\n      </div>\r\n    </div>\r\n    <!-- 政协资讯 -->\r\n    <div class=\"new_box\">\r\n      <div class=\"header_box\">\r\n        <span class=\"header_title\">政协资讯</span>\r\n        <span class=\"header_all\" @click=\"openNewMore()\">全部 &gt;</span>\r\n      </div>\r\n      <template v-if=\"loading\">\r\n        <div class=\"loading\">\r\n          <span class=\"loading_spinner\"></span>\r\n        </div>\r\n      </template>\r\n      <template v-else-if=\"carouselList.length > 0\">\r\n        <div class=\"carousel\">\r\n          <van-swipe class=\"swiper-container\" :autoplay=\"3000\" indicator-color=\"#fff\" :show-indicators=\"true\"\r\n            :loop=\"true\">\r\n            <van-swipe-item v-for=\"(item, index) in carouselList\" :key=\"index\" class=\"swiper-item\"\r\n              @click=\"onCarouselClick(item)\">\r\n              <div class=\"swiper-image-container\">\r\n                <img :src=\"config.API_URL + '/image/' + item.infoPic\" :alt=\"item.title\" class=\"swiper-image\">\r\n                <div class=\"swiper-overlay\">\r\n                  <div class=\"swiper-title\">{{ item.infoTitle }}</div>\r\n                </div>\r\n                <div class=\"carousel-dots\">\r\n                  <span v-for=\"(item, idx) in carouselList\" :key=\"idx\" class=\"dot\"\r\n                    :class=\"{ active: idx === carouselIndex }\" @click=\"goToNewsCarousel(idx)\"></span>\r\n                </div>\r\n              </div>\r\n            </van-swipe-item>\r\n          </van-swipe>\r\n        </div>\r\n      </template>\r\n      <template v-else>\r\n        <div class=\"loading\">\r\n          <span>暂无数据</span>\r\n        </div>\r\n      </template>\r\n    </div>\r\n    <!-- 协商活动预热 -->\r\n    <div class=\"negotiation_activities_preheating\">\r\n      <!-- 协商活动预热头部 -->\r\n      <div class=\"header_box\">\r\n        <span class=\"header_title\">协商活动预热</span>\r\n      </div>\r\n      <!-- 协商活动预热图片 -->\r\n      <div class=\"negotiation_activity_preheat\" v-if=\"isPreheat == '1'\">\r\n        <img :src=\"config.API_URL + '/pageImg/open/ixian_preheat_bg'\" alt=\"\" class=\"negotiation_activity_preheat_img\"\r\n          @click=\"openActivety(null)\">\r\n      </div>\r\n      <!-- 协商议题区域 -->\r\n      <div class=\"negotiation_topics\">\r\n        <div class=\"negotiation_topics_title\">协商议题</div>\r\n        <template v-if=\"negotiationTopicsLoading\">\r\n          <div class=\"loading\">\r\n            <span class=\"loading_spinner\"></span>\r\n          </div>\r\n        </template>\r\n        <template v-else-if=\"negotiationTopics.length > 0\">\r\n          <div class=\"negotiation_topics_content\">\r\n            <div class=\"negotiation-topic-card main\" @click=\"openActivety(negotiationTopics[0])\">\r\n              <div class=\"topic-title\">{{ negotiationTopics[0].title }}</div>\r\n              <div class=\"topic-image\">\r\n                <img v-if=\"negotiationTopics[0].titlePic\"\r\n                  :src=\"config.API_URL + '/image/' + negotiationTopics[0].titlePic\" alt=\"icon\" />\r\n              </div>\r\n              <button class=\"topic-btn\">去看看</button>\r\n            </div>\r\n            <div class=\"negotiation-topic-card sub\" v-for=\"item in negotiationTopics.slice(1)\" :key=\"item.id\"\r\n              @click=\"openActivety(item)\">\r\n              <div class=\"topic-title\">{{ item.title }}</div>\r\n              <div class=\"sub-topic-btn\">\r\n                <button class=\"topic-btn\" style=\"margin:0;\">去看看</button>\r\n                <div class=\"topic-image small\" style=\"margin:0;\">\r\n                  <img v-if=\"item.titlePic\" :src=\"config.API_URL + '/image/' + item.titlePic\" alt=\"icon\" />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n        <template v-else>\r\n          <div class=\"loading\">\r\n            <span>暂无数据</span>\r\n          </div>\r\n        </template>\r\n        <div class=\"negotiation-submit-btn-wrapper\" @click=\"openActivety(null)\">\r\n          <button class=\"negotiation-submit-btn\">提交意见建议</button>\r\n        </div>\r\n      </div>\r\n      <!-- 网络议政与问卷调查卡片区域 -->\r\n      <div class=\"custom-action-cards\">\r\n        <div class=\"action-card network\" @click=\"openNetwork\">\r\n          <div class=\"action-card-content\">\r\n            <span class=\"action-title\">参加网络议政</span>\r\n            <img class=\"action-arrow\" src=\"@/assets/img/icon_arrow.png\" alt=\">\" />\r\n          </div>\r\n          <img class=\"action-icon\" src=\"@/assets/img/icon_network.png\" alt=\"网络议政\" />\r\n        </div>\r\n        <div class=\"action-card survey\" @click=\"openSurvey\">\r\n          <div class=\"action-card-content\">\r\n            <span class=\"action-title\">参加问卷调查</span>\r\n            <img class=\"action-arrow\" src=\"@/assets/img/icon_arrow.png\" alt=\">\" />\r\n          </div>\r\n          <img class=\"action-icon\" src=\"@/assets/img/icon_survey.png\" alt=\"问卷调查\" />\r\n        </div>\r\n      </div>\r\n      <!-- 视频+图文直播 -->\r\n      <div class=\"video_graphic_live\" v-if=\"false\">\r\n        <div class=\"video_graphic_live_title\">视频+图文直播</div>\r\n        <template v-if=\"videoGraphicLiveLoading\">\r\n          <div class=\"loading\">\r\n            <span class=\"loading_spinner\"></span>\r\n          </div>\r\n        </template>\r\n        <template v-else-if=\"videoGraphicLive.length > 0\">\r\n          <div class=\"video_graphic_live_content\">\r\n            <div class=\"vg-carousel-img-wrapper\" @touchstart=\"onVGTouchStart\" @touchmove=\"onVGTouchMove\"\r\n              @touchend=\"onVGTouchEnd\" @click=\"onVGCarouselClick(videoGraphicLive[vgIndex])\">\r\n              <img class=\"vg-carousel-img\" :src=\"videoGraphicLive[vgIndex].img\"\r\n                :alt=\"videoGraphicLive[vgIndex].title\" />\r\n              <div class=\"vg-carousel-bottom-bar\">\r\n                <div class=\"vg-carousel-title\">{{ videoGraphicLive[vgIndex].title }}</div>\r\n                <div class=\"vg-carousel-dots\">\r\n                  <span v-for=\"(item, idx) in videoGraphicLive\" :key=\"idx\" class=\"vg-dot\"\r\n                    :class=\"{ active: idx === vgIndex }\" @click.stop=\"goToVGCarousel(idx)\"></span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n        <template v-else>\r\n          <div class=\"loading\">\r\n            <span>暂无数据</span>\r\n          </div>\r\n        </template>\r\n      </div>\r\n    </div>\r\n    <!-- 委员会客厅 -->\r\n    <div class=\"committee_living_room\">\r\n      <img src=\"@/assets/img/icon_committee_living_room.png\" alt=\"\" class=\"committee_living_room_img\"\r\n        @click=\"openCommitteeLivingRoom()\">\r\n    </div>\r\n    <!-- 协商议题征集 -->\r\n    <div class=\"negotiation_topics_collect\"\r\n      v-if=\"negotiationtopicsCollectList && negotiationtopicsCollectList.length > 0\">\r\n      <div class=\"header_box\">\r\n        <span class=\"header_title\">协商议题征集</span>\r\n      </div>\r\n      <div class=\"negotiation_topics_collect_box\">\r\n        <template v-if=\"negotiationTopicsCollectLoading\">\r\n          <div class=\"loading\">\r\n            <span class=\"loading_spinner\"></span>\r\n          </div>\r\n        </template>\r\n        <template v-else-if=\"negotiationtopicsCollectList.length > 0\">\r\n          <div class=\"topics_collect_title\">{{ negotiationtopicsCollectList[0].title }}</div>\r\n          <div class=\"topics_collect_content\">\r\n            {{ negotiationtopicsCollectList[0].content }}\r\n          </div>\r\n          <div class=\"topics_collect_organize\">发布机构：{{ negotiationtopicsCollectList[0].publishOrganize }}</div>\r\n          <div class=\"topics_collect_flex\">\r\n            <div class=\"topics_collect_flex_text1\">\r\n              <span v-if=\"negotiationtopicsCollectList[0].statusText !== '征集已结束'\">\r\n                {{ negotiationtopicsCollectList[0].statusText }}\r\n                <span class=\"blue\">{{ negotiationtopicsCollectList[0].days }}天</span>\r\n              </span>\r\n              <span v-else>\r\n                {{ negotiationtopicsCollectList[0].statusText }}\r\n              </span>\r\n            </div>\r\n            <div class=\"topics_collect_flex_text2\">已有{{ negotiationtopicsCollectList[0].commentCount }}条建议</div>\r\n          </div>\r\n          <div class=\"negotiation-submit-btn-wrapper\" @click=\"openCollectInfo(negotiationtopicsCollectList[0])\">\r\n            <button class=\"negotiation-submit-btn\">立即参与</button>\r\n          </div>\r\n        </template>\r\n        <template v-else>\r\n          <div class=\"loading\">\r\n            <span>暂无数据</span>\r\n          </div>\r\n        </template>\r\n      </div>\r\n    </div>\r\n    <!-- 政协概况与文史资料卡片区域 -->\r\n    <div class=\"overview_history_card\">\r\n      <div class=\"action_card overview\" @click=\"openOverview\">\r\n        <div class=\"overview_history_card_content\">\r\n          <div class=\"action_title\">政协概况</div>\r\n          <div class=\"action_info\">查看详情</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"action_card history\" @click=\"openHistory\">\r\n        <div class=\"overview_history_card_content\">\r\n          <div class=\"action_title\">文史资料</div>\r\n          <div class=\"action_info\">查看详情</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 文史资料征集 -->\r\n    <div class=\"cultural_historical_collection\" v-if=\"culturalHistoryList && culturalHistoryList.length > 0\">\r\n      <div class=\"header_box\">\r\n        <span class=\"header_title\">文史资料征集</span>\r\n        <span class=\"header_all\" @click=\"openHistoricalCollecMore()\">全部 &gt;</span>\r\n      </div>\r\n      <div class=\"cultural_historical_collection_content\">\r\n        <div class=\"historical_collection_content_flex\">\r\n          <div class=\"cultural-history-card\" v-for=\"item in culturalHistoryList\" :key=\"item.id\"\r\n            @click=\"goToCulturalDetail(item)\">\r\n            <div class=\"card-title\">{{ item.theme }}</div>\r\n            <div class=\"card-btn-icon\">\r\n              <button class=\"card-btn\">去看看</button>\r\n              <img class=\"card-icon\" :src=\"item.icon\" alt=\"icon\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"historical_collection_wrapper\" @click=\"openSubmitHistorical()\">\r\n          <button class=\"historical_collection_wrapper_btn\">我要提交文史资料</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 文史馆 -->\r\n    <div class=\"archives_institute\">\r\n      <img src=\"@/assets/img/icon_committee_living_room.png\" alt=\"\" class=\"archives_institute_img\"\r\n        @click=\"openArchivesInstitute()\">\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'LayoutHome' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted, onBeforeUnmount } from 'vue'\r\nimport { useRouter, useRoute } from 'vue-router'\r\nimport config from '@/config'\r\nimport { showToast } from 'vant'\r\nimport store from '@/store'\r\nimport { getAuthCode, removeTag } from '@/utils/utils'\r\nimport { isInThirdPartyApp, getDefaultUser, getEnvironmentType } from '@/utils/environment'\r\nconst router = useRouter()\r\nconst route = useRoute()\r\n\r\nconst carouselList = ref([])\r\nconst loading = ref(false)\r\n\r\nconst carouselIndex = ref(0)\r\nlet carouselTimer = null\r\n// const touchStartX = ref(0)\r\n// const touchEndX = ref(0)\r\n\r\nconst isPreheat = ref('')\r\nconst defaultActivityId = ref('')\r\nconst negotiationTopics = ref([])\r\nconst negotiationTopicsLoading = ref(false)\r\n\r\nconst videoGraphicLiveLoading = ref(false)\r\nconst videoGraphicLive = ref([\r\n  {\r\n    img: require('@/assets/img/bg_survey_list_zx.png'),\r\n    title: '市政协召开全市“深化六个改革”重点工作宣讲会',\r\n    id: 1\r\n  },\r\n  {\r\n    img: require('@/assets/img/icon_top_bg.png'),\r\n    title: '政协委员积极建言献策 助力城市发展',\r\n    id: 2\r\n  },\r\n  {\r\n    img: require('@/assets/img/icon_top_logo.png'),\r\n    title: '协商议政新模式 推动社会治理创新',\r\n    id: 3\r\n  }\r\n])\r\nconst vgIndex = ref(0)\r\nlet vgTimer = null\r\nconst vgTouchStartX = ref(0)\r\nconst vgTouchEndX = ref(0)\r\n\r\nconst negotiationTopicsCollectLoading = ref(false)\r\nconst negotiationtopicsCollectList = ref([])\r\n\r\nconst culturalHistoryList = ref([])\r\nconst loadingPage = ref(true)\r\n\r\nonMounted(() => {\r\n  document.title = '西安政协'\r\n  // 获取i西安token\r\n  getIXiAnToken()\r\n})\r\nonBeforeUnmount(() => {\r\n  stopNewsCarousel()\r\n  stopVGCarousel()\r\n})\r\n// 获取i西安的token\r\nconst getIXiAnToken = async () => {\r\n  loadingPage.value = true\r\n\r\n  // 检测环境，如果不在第三方app中，使用默认账号\r\n  const inThirdPartyApp = isInThirdPartyApp()\r\n  const envType = getEnvironmentType()\r\n\r\n  console.log(`环境检测结果: ${inThirdPartyApp ? '第三方app' : '普通浏览器'} (${envType})`)\r\n\r\n  if (!inThirdPartyApp) {\r\n    console.log('当前不在第三方app环境中，使用默认账号登录')\r\n    const publicUser = sessionStorage.getItem('public_user')\r\n    if (!publicUser) {\r\n      const defaultUser = getDefaultUser()\r\n      autoLogin(defaultUser)\r\n    } else {\r\n      await getTopCarousel()\r\n      loadingPage.value = false\r\n    }\r\n    return\r\n  }\r\n\r\n  // 在第三方app环境中，继续原有逻辑\r\n  try {\r\n    const res = await api.ixaToekn({})\r\n    if (res.data.data) {\r\n      getIXiAnCode(res.data.data.jsapiToken)\r\n    } else {\r\n      console.warn('JsapiToken获取失败，尝试使用默认账号')\r\n      loadingPage.value = false\r\n      setTimeout(() => {\r\n        showToast('JsapiToken获取失败，使用默认账号登录')\r\n        const defaultUser = getDefaultUser()\r\n        autoLogin(defaultUser)\r\n      }, 500)\r\n    }\r\n  } catch (error) {\r\n    console.error('获取JsapiToken异常:', error)\r\n    loadingPage.value = false\r\n    setTimeout(() => {\r\n      showToast('网络异常，使用默认账号登录')\r\n      const defaultUser = getDefaultUser()\r\n      autoLogin(defaultUser)\r\n    }, 500)\r\n  }\r\n}\r\n// 获取i西安的code\r\nconst getIXiAnCode = async (jsapiToken) => {\r\n  // 再次检测环境，确保在第三方app中\r\n  if (!isInThirdPartyApp()) {\r\n    console.log('getIXiAnCode: 当前不在第三方app环境中，使用默认账号')\r\n    const defaultUser = getDefaultUser()\r\n    autoLogin(defaultUser)\r\n    return\r\n  }\r\n\r\n  try {\r\n    getAuthCode({\r\n      // \"appId\": \"9de99f6934b94cf38c75ce40c9ddf8a2\", //测试应用id\r\n      \"appId\": \"b105ea5a2fca4b28afdd52558952552f\", //正式应用id\r\n      \"forceScopes\": ['ixa_user_info'], //授权的能力标识数组，\r\n      \"jsapiToken\": jsapiToken //应用授权的请求码\r\n    }, function (result) {\r\n      if (result.code === 0) {\r\n        console.log('✅ 第三方app授权成功')\r\n        getIXiAnUser(result.data.authCode)\r\n      } else {\r\n        console.error('❌ 第三方app授权失败:', result.message)\r\n        // 如果授权失败，降级到默认账号\r\n        console.log('授权失败，降级使用默认账号登录')\r\n        showToast('授权失败，使用默认账号登录')\r\n        const defaultUser = getDefaultUser()\r\n        autoLogin(defaultUser)\r\n      }\r\n    })\r\n  } catch (error) {\r\n    console.error('getAuthCode调用异常:', error)\r\n    console.log('getAuthCode异常，降级使用默认账号登录')\r\n    showToast('授权异常，使用默认账号登录')\r\n    const defaultUser = getDefaultUser()\r\n    autoLogin(defaultUser)\r\n  }\r\n}\r\n// 获取i西安的用户信息\r\nconst getIXiAnUser = async (code) => {\r\n  const res = await api.ixaUser({ authCode: code })\r\n  console.log('用户信息-->' + JSON.stringify(res))\r\n  let ixaUser = res.data.data.userInfo\r\n  autoLogin(ixaUser)\r\n  localStorage.setItem('ixaUser', JSON.stringify(ixaUser))\r\n}\r\n// 公众登录\r\nconst autoLogin = async (user) => {\r\n  if (!user.userName) {\r\n    loadingPage.value = false\r\n    showToast('请您先进行实名认证！')\r\n    setTimeout(() => {\r\n      window.history.back()\r\n    }, 800)\r\n    return\r\n  }\r\n  try {\r\n    const { data } = await api.login({\r\n      grant_type: 'anonymoussso',\r\n      userName: user.userName,\r\n      mobile: user.phone\r\n    }, {\r\n      headers: {\r\n        authorization: 'basic enlzb2Z0Onp5c29mdCo2MDc5'\r\n      }\r\n    })\r\n    sessionStorage.setItem('token', data.token)\r\n    sessionStorage.setItem('refresh_token', `bearer ${data.refreshToken.value}`)\r\n    sessionStorage.setItem('expires', data.expires_in)\r\n    sessionStorage.setItem('expiration', data.refreshToken.expiration)\r\n    sessionStorage.setItem('verify', data.passwordElementMatchReg ? 1 : 0)\r\n    loadingPage.value = false // 登录成功后loading消失\r\n    await store.dispatch('loginUser')\r\n    // 获取置顶资讯\r\n    await getTopCarousel()\r\n  } catch (error) {\r\n    loadingPage.value = false // 登录失败也消失loading\r\n    showToast(error.message || '登录失败')\r\n  }\r\n}\r\n// 获取置顶资讯\r\nconst getTopCarousel = async () => {\r\n  loading.value = true\r\n  const params = {\r\n    objectParam: {},\r\n    pageNo: 1,\r\n    pageSize: 5,\r\n    keyword: '',\r\n    query: {\r\n      columnId: route.query.columnId || '1887325961586761729',\r\n      moduleId: 1,\r\n      passFlag: 1\r\n    },\r\n    wheres: [{ columnId: 'zy_news_content_1_is_top', queryType: 'EQ', value: '1' }],\r\n    tableId: 'zy_news_content_1'\r\n  }\r\n  const { data } = await api.newsContentList(params)\r\n  carouselList.value = data\r\n  loading.value = false\r\n  startNewsCarousel()\r\n  getConfig()\r\n}\r\n// 获取配置\r\nconst getConfig = async () => {\r\n  const { data } = await api.globalReadOpenConfig({ codes: ['ixian_preheat'] })\r\n  isPreheat.value = data.ixian_preheat\r\n  getConsultActivityList()\r\n}\r\n// 获取协商活动\r\nconst getConsultActivityList = async (pageSize) => {\r\n  if (!pageSize) {\r\n    negotiationTopicsLoading.value = true\r\n  }\r\n  const params = {\r\n    pageNo: 1,\r\n    pageSize: pageSize || 3,\r\n    year: 2025,\r\n    objectParam: {}\r\n  }\r\n  if (pageSize) {\r\n    params.objectParam.showPublic = '1'\r\n  } else {\r\n    params.objectParam.ifLatest = '1'\r\n  }\r\n  const { data } = await api.consultActivityList(params)\r\n  if (pageSize) {\r\n    if (data.length > 0) {\r\n      defaultActivityId.value = data[0].id\r\n    } else {\r\n      showToast('暂无设置当前协商！')\r\n    }\r\n  } else {\r\n    negotiationTopics.value = data\r\n    negotiationTopicsLoading.value = false\r\n    getVideoGraphicLive()\r\n    getNegotiationTopicsCollect()\r\n  }\r\n}\r\n// 获取视频+图文直播\r\nconst getVideoGraphicLive = async () => {\r\n  videoGraphicLiveLoading.value = false\r\n  startVGCarousel()\r\n}\r\n// 获取协商议题征集\r\nconst getNegotiationTopicsCollect = async () => {\r\n  negotiationTopicsCollectLoading.value = true\r\n  const { code, data } = await api.opinioncollectList({\r\n    pageNo: 1,\r\n    pageSize: 1,\r\n    collectStatus: 2,\r\n    query: { businessCode: 'discussioncollect' }\r\n  })\r\n  if (code === 200) {\r\n    const now = new Date().getTime()\r\n    const oneDay = 24 * 60 * 60 * 1000\r\n    const processedData = (data || []).map(item => {\r\n      const start = new Date(item.startDate).getTime()\r\n      const end = new Date(item.endDate).getTime()\r\n      let statusText = ''\r\n      let days = 0\r\n      if (now > end) {\r\n        statusText = '征集已结束'\r\n        days = 0\r\n      } else if (now > start) {\r\n        statusText = '征集结束剩余：'\r\n        days = Math.floor((end - now) / oneDay)\r\n      } else {\r\n        statusText = '征集开始还有：'\r\n        days = Math.floor((start - now) / oneDay)\r\n      }\r\n      const canJoin = now > start && now < end && item.isJoinName === '未参与'\r\n      return {\r\n        ...item,\r\n        statusText,\r\n        days,\r\n        canJoin\r\n      }\r\n    })\r\n    negotiationtopicsCollectList.value = negotiationtopicsCollectList.value.concat(processedData)\r\n    if (negotiationtopicsCollectList.value[0]) {\r\n      negotiationtopicsCollectList.value[0].content = removeTag(negotiationtopicsCollectList.value[0].content)\r\n    }\r\n    negotiationTopicsCollectLoading.value = false\r\n    getCulturalHistoricalCollection()\r\n  }\r\n}\r\n// 通用轮播控制方法\r\nconst startCarousel = (timer, index, list, callback) => {\r\n  if (timer) clearInterval(timer)\r\n  return setInterval(() => {\r\n    index.value = (index.value + 1) % list.value.length\r\n  }, 30000)\r\n}\r\nconst stopCarousel = (timer) => {\r\n  if (timer) {\r\n    clearInterval(timer)\r\n    return null\r\n  }\r\n  return null\r\n}\r\nconst goToCarousel = (idx, index, timer, list, callback) => {\r\n  timer = stopCarousel(timer)\r\n  index.value = idx\r\n  return startCarousel(timer, index, list, callback)\r\n}\r\nconst prevCarousel = (index, list) => {\r\n  index.value = (index.value - 1 + list.value.length) % list.value.length\r\n}\r\nconst nextCarousel = (index, list) => {\r\n  index.value = (index.value + 1) % list.value.length\r\n}\r\n// 通用触摸处理\r\nconst onTouchStart = (e, touchStartX, touchEndX) => {\r\n  touchStartX.value = e.touches[0].clientX\r\n  touchEndX.value = e.touches[0].clientX\r\n}\r\nconst onTouchMove = (e, touchEndX) => {\r\n  touchEndX.value = e.touches[0].clientX\r\n}\r\nconst onTouchEnd = (e, touchStartX, touchEndX, stopFn, nextFn, prevFn, startFn) => {\r\n  const deltaX = touchEndX.value - touchStartX.value\r\n  if (Math.abs(deltaX) > 50) {\r\n    stopFn()\r\n    if (deltaX < 0) {\r\n      nextFn()\r\n    } else {\r\n      prevFn()\r\n    }\r\n    startFn()\r\n  }\r\n  touchStartX.value = 0\r\n  touchEndX.value = 0\r\n}\r\n// 资讯轮播图相关方法\r\nconst startNewsCarousel = () => {\r\n  carouselTimer = startCarousel(carouselTimer, carouselIndex, carouselList)\r\n}\r\nconst stopNewsCarousel = () => {\r\n  carouselTimer = stopCarousel(carouselTimer)\r\n}\r\n// 打开资讯详情\r\nconst onCarouselClick = (row) => {\r\n  router.push({ path: '/NewDetails', query: { id: row.id } })\r\n}\r\n// 打开全部资讯\r\nconst openNewMore = () => {\r\n  router.push({ path: '/NewList', query: { utype: '1' } })\r\n}\r\n// 跳转协商活动\r\nconst openActivety = async (_item) => {\r\n  if (_item) {\r\n    router.push({ path: '/NegotiationActivityPage', query: { id: _item.id } })\r\n  } else {\r\n    await getConsultActivityList(1)\r\n    if (defaultActivityId.value) {\r\n      router.push({ path: '/NegotiationActivityPage', query: { id: defaultActivityId.value } })\r\n    }\r\n  }\r\n}\r\n// 跳转网络议政\r\nconst openNetwork = () => {\r\n  router.push({ path: '/NetworkDiscussList' })\r\n}\r\n// 跳转问卷调查\r\nconst openSurvey = () => {\r\n  router.push({ path: '/QuestionnaireList' })\r\n}\r\n// 视频+图文直播轮播图相关方法\r\nconst startVGCarousel = () => {\r\n  vgTimer = startCarousel(vgTimer, vgIndex, videoGraphicLive)\r\n}\r\nconst stopVGCarousel = () => {\r\n  vgTimer = stopCarousel(vgTimer)\r\n}\r\nconst goToVGCarousel = (idx) => {\r\n  vgTimer = goToCarousel(idx, vgIndex, vgTimer, videoGraphicLive)\r\n}\r\nconst prevVGCarousel = () => {\r\n  prevCarousel(vgIndex, videoGraphicLive)\r\n}\r\nconst nextVGCarousel = () => {\r\n  nextCarousel(vgIndex, videoGraphicLive)\r\n}\r\nconst onVGTouchStart = (e) => {\r\n  onTouchStart(e, vgTouchStartX, vgTouchEndX)\r\n}\r\nconst onVGTouchMove = (e) => {\r\n  onTouchMove(e, vgTouchEndX)\r\n}\r\nconst onVGTouchEnd = (e) => {\r\n  onTouchEnd(e, vgTouchStartX, vgTouchEndX, stopVGCarousel, nextVGCarousel, prevVGCarousel, startVGCarousel)\r\n}\r\n// 跳转视频+图文直播详情\r\nconst onVGCarouselClick = (_item) => {\r\n  // 跳转或弹窗逻辑\r\n  // router.push({ path: '/VideoDetail', query: { id: _item.id } })\r\n}\r\n// 跳转委员会客厅\r\nconst openCommitteeLivingRoom = () => {\r\n  // router.push({ path: '/CommitteeLivingRoomList' })\r\n}\r\n// 跳转协商议题征集详情\r\nconst openCollectInfo = (_item) => {\r\n  router.push({ path: '/NegotiationTopicsDetails', query: { id: _item.id } })\r\n}\r\n// 跳转政协概况\r\nconst openOverview = () => {\r\n  window.open('https://www.xa-cppcc.gov.cn/zxgk/zyzn/4.html', '_blank');\r\n}\r\n// 跳转文史资料\r\nconst openHistory = () => {\r\n  router.push({ path: '/CultureHistory' })\r\n}\r\n// 获取文史资料征集\r\nconst getCulturalHistoricalCollection = async () => {\r\n  const params = {\r\n    pageNo: 1,\r\n    pageSize: 2,\r\n    query: {\r\n      channelId: '1935275534502072322',\r\n      isDraft: 0\r\n    },\r\n    tableId: 'id_message_notification',\r\n    wheres: []\r\n  }\r\n  const { data, code } = await api.notificationList(params)\r\n  if (code == 200) {\r\n    const icons = [\r\n      require('@/assets/img/icon_culture_history01.png'),\r\n      require('@/assets/img/icon_culture_history02.png')\r\n    ]\r\n    culturalHistoryList.value = (data || []).map((item, idx) => ({\r\n      ...item,\r\n      icon: icons[idx]\r\n    }))\r\n  } else {\r\n    showToast('文史资料征集请求失败！')\r\n  }\r\n}\r\n// 文史资料征集进详情\r\nconst goToCulturalDetail = (_item) => {\r\n  router.push({ path: '/NoticeDetails', query: { id: _item.id } })\r\n}\r\n// 打开文史资料征集进全部\r\nconst openHistoricalCollecMore = () => {\r\n  router.push({ path: '/NoticeList', query: {} })\r\n}\r\n// 提交文史资料\r\nconst openSubmitHistorical = () => {\r\n  router.push({ path: '/CultureHistoryAdd' })\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.LayoutHome {\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow: auto;\r\n\r\n  // 顶部背景和logo\r\n  .top_bg {\r\n    position: relative;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 220px;\r\n    overflow: hidden;\r\n\r\n    .bg_img {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      width: 100%;\r\n      height: 100%;\r\n      object-fit: cover;\r\n      z-index: 1;\r\n    }\r\n\r\n    .logo {\r\n      position: relative;\r\n      z-index: 2;\r\n      align-self: flex-start;\r\n      margin: 32px 0 0 15px;\r\n    }\r\n\r\n    .top_text {\r\n      position: relative;\r\n      z-index: 2;\r\n      margin-top: auto;\r\n      margin-bottom: 43px;\r\n    }\r\n  }\r\n\r\n  // 政协资讯\r\n  .new_box {\r\n    margin-top: -32px;\r\n    background-image: url('@/assets/img/icon_new_bg.png');\r\n    background-size: 100% 100%;\r\n    height: 340px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    position: relative;\r\n    z-index: 3;\r\n\r\n    .carousel {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      margin: 15px;\r\n      height: 100%;\r\n\r\n      .swiper-container {\r\n        width: 100%;\r\n        height: 100%;\r\n\r\n        .swiper-item {\r\n          width: 100%;\r\n\r\n          .swiper-image-container {\r\n            position: relative;\r\n            width: 100%;\r\n            height: 100%;\r\n            box-sizing: border-box;\r\n\r\n            .swiper-image {\r\n              width: 100%;\r\n              height: 180px;\r\n              object-fit: cover;\r\n              border-radius: 5px;\r\n            }\r\n\r\n            .swiper-overlay {\r\n              padding: 5px;\r\n\r\n              .swiper-title {\r\n                color: #3A3A3A;\r\n                font-size: 14px;\r\n                margin: 0 0 8px 0;\r\n                display: -webkit-box;\r\n                -webkit-line-clamp: 2;\r\n                -webkit-box-orient: vertical;\r\n                overflow: hidden;\r\n                text-overflow: ellipsis;\r\n              }\r\n\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 协商活动预热\r\n  .negotiation_activities_preheating {\r\n    background-image: url('@/assets/img/icon_preheat_bg.png');\r\n    background-size: 100% 100%;\r\n    // height: 750px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    position: relative;\r\n\r\n    .negotiation_activity_preheat {\r\n      margin: 20px 26px 0;\r\n      flex-shrink: 0;\r\n\r\n      .negotiation_activity_preheat_img {\r\n        width: 100%;\r\n        height: auto;\r\n        display: block;\r\n      }\r\n    }\r\n\r\n    .negotiation_topics {\r\n      background: #EDF7FF;\r\n      border-radius: 8px;\r\n      box-shadow: 0px 0px 10px 0px rgba(107, 108, 109, 0.18);\r\n      padding: 14px 12px;\r\n      margin: 20px 26px 15px 26px;\r\n\r\n      .negotiation_topics_title {\r\n        font-size: 16px;\r\n        color: #3368C6;\r\n        margin-bottom: 12px;\r\n        letter-spacing: 1px;\r\n      }\r\n\r\n      .negotiation_topics_content {\r\n        display: grid;\r\n        grid-template-columns: 1.2fr 1fr;\r\n        grid-template-rows: 1fr 1fr;\r\n        gap: 10px;\r\n        margin-bottom: 15px;\r\n\r\n        .negotiation-topic-card {\r\n          background: #fff;\r\n          border-radius: 8px;\r\n          box-shadow: 0 2px 12px 0 rgba(51, 104, 198, 0.06);\r\n          padding: 10px;\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: flex-start;\r\n\r\n          .topic-title {\r\n            font-size: 12px;\r\n            color: #000;\r\n            line-height: 1.4;\r\n          }\r\n\r\n          .topic-image {\r\n            width: 74px;\r\n            height: 62px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            margin: auto;\r\n\r\n            img {\r\n              width: 100%;\r\n              height: 100%;\r\n              object-fit: contain;\r\n            }\r\n          }\r\n\r\n          .sub-topic-btn {\r\n            display: flex;\r\n            align-items: flex-end;\r\n            justify-content: space-between;\r\n            width: 100%;\r\n          }\r\n\r\n          .topic-btn {\r\n            margin: 0 auto;\r\n            background: #3368C6;\r\n            border-radius: 20px;\r\n            padding: 2px 10px;\r\n            font-size: 12px;\r\n            border: none;\r\n            color: #fff;\r\n          }\r\n        }\r\n\r\n        .main {\r\n          grid-row: 1 / span 2;\r\n          grid-column: 1 / 2;\r\n          min-width: 0;\r\n          width: 115px;\r\n          background: #E3EFFF;\r\n\r\n          .topic-title {\r\n            font-size: 12px;\r\n            color: #000;\r\n            line-height: 1.4;\r\n            display: -webkit-box;\r\n            -webkit-line-clamp: 4;\r\n            -webkit-box-orient: vertical;\r\n            overflow: hidden;\r\n          }\r\n\r\n          .topic-image {\r\n            width: 74px;\r\n            height: 62px;\r\n          }\r\n        }\r\n\r\n        .sub {\r\n          grid-column: 2 / 3;\r\n          width: 170px;\r\n          background: #FFFFFF;\r\n          border-radius: 8px;\r\n\r\n          .topic-title {\r\n            font-size: 12px;\r\n            color: #000;\r\n            line-height: 1.4;\r\n            display: -webkit-box;\r\n            -webkit-line-clamp: 2;\r\n            -webkit-box-orient: vertical;\r\n            overflow: hidden;\r\n          }\r\n\r\n          .topic-image {\r\n            width: 26px;\r\n            height: 33px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .negotiation-submit-btn-wrapper {\r\n        display: flex;\r\n        justify-content: center;\r\n        margin-bottom: 0;\r\n\r\n        .negotiation-submit-btn {\r\n          background: linear-gradient(270deg, #4E80D1 0%, #C2E6FF 100%);\r\n          color: #fff;\r\n          font-size: 15px;\r\n          border: none;\r\n          border-radius: 6px;\r\n          padding: 8px 0;\r\n          width: 100%;\r\n          max-width: 480px;\r\n          letter-spacing: 1px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .custom-action-cards {\r\n      display: flex;\r\n      flex-direction: row;\r\n      gap: 16px;\r\n      margin: 0 26px 15px 26px;\r\n\r\n      .action-card {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        background-image: url('@/assets/img/icon_participate_bg.png');\r\n        background-size: 100% 100%;\r\n        padding: 20px 18px 20px 12px;\r\n        position: relative;\r\n      }\r\n\r\n      .action-card-content {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 8px;\r\n        margin-right: 10px;\r\n      }\r\n\r\n      .action-title {\r\n        font-size: 14px;\r\n        color: #3368C6;\r\n        font-weight: 500;\r\n      }\r\n\r\n      .action-arrow {\r\n        width: 4px;\r\n        height: 5px;\r\n      }\r\n\r\n      .action-icon {\r\n        width: 17px;\r\n        height: 17px;\r\n      }\r\n    }\r\n\r\n    .video_graphic_live {\r\n      background: #EDF7FF;\r\n      border-radius: 8px;\r\n      box-shadow: 0px 0px 10px 0px rgba(107, 108, 109, 0.18);\r\n      padding: 10px 12px;\r\n      margin: 0px 26px 30px 26px;\r\n      height: 200px;\r\n\r\n      .video_graphic_live_title {\r\n        font-size: 16px;\r\n        color: #3368C6;\r\n        margin-bottom: 12px;\r\n        letter-spacing: 1px;\r\n      }\r\n\r\n      .video_graphic_live_content {\r\n        position: relative;\r\n        width: 100%;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        .vg-carousel-img-wrapper {\r\n          position: relative;\r\n          width: 100%;\r\n          height: 150px;\r\n          border-radius: 10px;\r\n          overflow: hidden;\r\n          box-shadow: 0 2px 12px 0 rgba(51, 104, 198, 0.08);\r\n          cursor: pointer;\r\n          transition: box-shadow 0.3s;\r\n          background: #fff;\r\n\r\n          .vg-carousel-img {\r\n            width: 100%;\r\n            height: 100%;\r\n            object-fit: cover;\r\n            display: block;\r\n          }\r\n\r\n          .vg-carousel-bottom-bar {\r\n            position: absolute;\r\n            left: 0;\r\n            bottom: 0;\r\n            width: 100%;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            background: rgba(0, 0, 0, 0.45);\r\n            padding: 0 16px 0 16px;\r\n            height: 38px;\r\n            border-bottom-left-radius: 10px;\r\n            border-bottom-right-radius: 10px;\r\n          }\r\n\r\n          .vg-carousel-title {\r\n            color: #fff;\r\n            font-size: 13px;\r\n            white-space: nowrap;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            max-width: 80%;\r\n          }\r\n\r\n          .vg-carousel-dots {\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 8px;\r\n            margin: 0 0 0 16px;\r\n\r\n            .vg-dot {\r\n              width: 4px;\r\n              height: 4px;\r\n              background: #C2E6FF;\r\n              border-radius: 2px;\r\n            }\r\n\r\n            .vg-dot.active {\r\n              width: 13px;\r\n              height: 3px;\r\n              background: #3368C6;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 委员会客厅\r\n  .committee_living_room {\r\n    margin: 4px 8px 8px;\r\n\r\n    .committee_living_room_img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n\r\n  // 协商议题征集\r\n  .negotiation_topics_collect {\r\n    background-image: url('@/assets/img/negotiation_topics_collect_bg.png');\r\n    background-size: 100% 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    position: relative;\r\n    min-height: 344px;\r\n\r\n    .negotiation_topics_collect_box {\r\n      background: #EDF7FF;\r\n      border-radius: 8px;\r\n      box-shadow: 0px 0px 10px 0px rgba(107, 108, 109, 0.18);\r\n      padding: 10px 12px;\r\n      margin: 26px 26px 30px 26px;\r\n      min-height: 270px;\r\n\r\n      .topics_collect_title {\r\n        font-size: 16px;\r\n        color: #3368C6;\r\n        text-align: center;\r\n        font-weight: bold;\r\n      }\r\n\r\n      .topics_collect_content {\r\n        font-size: 15px;\r\n        color: #333;\r\n        line-height: 24px;\r\n        margin-top: 20px;\r\n        display: -webkit-box;\r\n        -webkit-line-clamp: 8;\r\n        -webkit-box-orient: vertical;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n      }\r\n\r\n      .topics_collect_organize {\r\n        font-size: 13px;\r\n        color: #999999;\r\n        margin-top: 20px;\r\n      }\r\n\r\n      .topics_collect_flex {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        font-size: 13px;\r\n        margin-top: 10px;\r\n\r\n        .topics_collect_flex_text1 {\r\n          font-size: 13px;\r\n          color: #DA1718;\r\n        }\r\n\r\n        .topics_collect_flex_text2 {\r\n          font-size: 13px;\r\n          color: #333333;\r\n        }\r\n      }\r\n\r\n      .negotiation-submit-btn-wrapper {\r\n        display: flex;\r\n        justify-content: center;\r\n        margin-top: 16px;\r\n\r\n        .negotiation-submit-btn {\r\n          background: linear-gradient(270deg, #4E80D1 0%, #C2E6FF 100%);\r\n          color: #fff;\r\n          font-size: 15px;\r\n          border: none;\r\n          border-radius: 6px;\r\n          padding: 8px 0;\r\n          width: 100%;\r\n          max-width: 480px;\r\n          letter-spacing: 1px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 政协概况与文史资料卡片区域\r\n  .overview_history_card {\r\n    display: flex;\r\n    flex-direction: row;\r\n    gap: 16px;\r\n    margin: 10px 10px;\r\n\r\n    .action_card {\r\n      // display: flex;\r\n      // flex-direction: column;\r\n      // align-items: flex-start;\r\n      // justify-content: space-between;\r\n      padding: 20px 18px 20px 12px;\r\n      position: relative;\r\n      height: 94px;\r\n      width: 50%;\r\n\r\n      .overview_history_card_content {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n        gap: 8px;\r\n        margin-right: 10px;\r\n\r\n        .action_title {\r\n          font-size: 17px;\r\n          font-weight: bold;\r\n          color: #3368C6;\r\n        }\r\n\r\n        .action_info {\r\n          background: #3368C6;\r\n          border-radius: 20px;\r\n          font-size: 12px;\r\n          color: #FFFFFF;\r\n          line-height: 18px;\r\n          padding: 1px 8px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .overview {\r\n      background-image: url('@/assets/img/icon_overview_bg.png');\r\n      background-size: 100% 100%;\r\n    }\r\n\r\n    .history {\r\n      background-image: url('@/assets/img/icon_history_bg.png');\r\n      background-size: 100% 100%;\r\n    }\r\n  }\r\n\r\n  // 文史资料征集\r\n  .cultural_historical_collection {\r\n    background-image: url('@/assets/img/icon_cultural_historical_collection_bg.png');\r\n    background-size: 100% 100%;\r\n    height: 240px;\r\n    margin: 0 10px;\r\n\r\n    .cultural_historical_collection_content {\r\n\r\n      .historical_collection_content_flex {\r\n        display: grid;\r\n        grid-template-columns: repeat(2, 1fr);\r\n        gap: 15px;\r\n        padding: 20px 15px 5px 15px;\r\n\r\n        .cultural-history-card {\r\n          background: #EDF7FF;\r\n          border-radius: 12px;\r\n          box-shadow: 0 2px 8px rgba(51, 104, 198, 0.08);\r\n          padding: 10px 12px 6px 12px;\r\n          position: relative;\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: flex-start;\r\n\r\n          .card-title {\r\n            font-size: 14px;\r\n            color: #333;\r\n            font-weight: bold;\r\n            margin-bottom: 16px;\r\n            display: -webkit-box;\r\n            -webkit-line-clamp: 2;\r\n            -webkit-box-orient: vertical;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            // min-height: 40px;\r\n          }\r\n\r\n          .card-btn-icon {\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            width: 100%;\r\n\r\n            .card-btn {\r\n              background: #3368C6;\r\n              color: #fff;\r\n              border: none;\r\n              border-radius: 16px;\r\n              padding: 4px 10px;\r\n              font-size: 13px;\r\n              align-self: flex-start;\r\n            }\r\n\r\n            .card-icon {\r\n              width: 26px;\r\n              height: 26px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .historical_collection_wrapper {\r\n        display: flex;\r\n        justify-content: center;\r\n        margin: 15px 30px;\r\n\r\n        .historical_collection_wrapper_btn {\r\n          background: linear-gradient(270deg, #4E80D1 0%, #C2E6FF 100%);\r\n          color: #fff;\r\n          font-size: 15px;\r\n          border: none;\r\n          border-radius: 6px;\r\n          padding: 8px 0;\r\n          width: 100%;\r\n          max-width: 480px;\r\n          letter-spacing: 1px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 文史馆\r\n  .archives_institute {}\r\n\r\n  .archives_institute {\r\n    margin: 4px 8px 8px;\r\n\r\n    .archives_institute_img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n\r\n  .header_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 20px 25px 0;\r\n    z-index: 4;\r\n\r\n    .header_title {\r\n      font-size: 18px;\r\n      font-weight: bold;\r\n      color: #000;\r\n      letter-spacing: 1px;\r\n      display: inline-block;\r\n      position: relative;\r\n    }\r\n\r\n    .header_title::after {\r\n      content: '';\r\n      position: absolute;\r\n      left: 50%;\r\n      transform: translateX(-50%);\r\n      width: 20px;\r\n      height: 3px;\r\n      background: #3368C6;\r\n      border-radius: 2px;\r\n      margin-top: 4px;\r\n      margin-bottom: 2px;\r\n      bottom: -9px;\r\n    }\r\n\r\n    .header_all {\r\n      font-size: 14px;\r\n      color: #999999;\r\n    }\r\n  }\r\n\r\n  .loading {\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    font-size: 14px;\r\n    color: #8d8d8d;\r\n    padding: 42px 15px 20px;\r\n\r\n    .loading_spinner {\r\n      width: 40px;\r\n      height: 40px;\r\n      border: 4px solid #e0e0e0;\r\n      border-top: 4px solid #3368C6;\r\n      border-radius: 50%;\r\n      animation: spin 1s linear infinite;\r\n      display: inline-block;\r\n    }\r\n  }\r\n\r\n  @keyframes spin {\r\n    to {\r\n      transform: rotate(360deg);\r\n    }\r\n  }\r\n}\r\n\r\n// 全局loading遮罩样式\r\n.global-loading-mask {\r\n  position: fixed;\r\n  z-index: 9999;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(255, 255, 255, 0.85);\r\n}\r\n\r\n.global-loading-spinner {\r\n  width: 48px;\r\n  height: 48px;\r\n  border: 5px solid #e0e0e0;\r\n  border-top: 5px solid #3368C6;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.global-loading-text {\r\n  font-size: 18px;\r\n  color: #3368C6;\r\n}\r\n\r\n.van-swipe__indicators {\r\n  bottom: 10px !important;\r\n}\r\n\r\n.van-swipe__indicator {\r\n  width: 18px;\r\n  height: 6px;\r\n  border-radius: 3px;\r\n  background: #D8E0F3 !important;\r\n}\r\n\r\n.van-swipe__indicator--active {\r\n  background: #3368C6 !important;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;0BAwPA,uKAAAA,CAAA,EAAAC,CAAA,EAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,WAAA,8BAAAC,EAAAN,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAC,CAAA,GAAAL,CAAA,IAAAA,CAAA,CAAAM,SAAA,YAAAC,SAAA,GAAAP,CAAA,GAAAO,SAAA,EAAAC,CAAA,GAAAC,MAAA,CAAAC,MAAA,CAAAL,CAAA,CAAAC,SAAA,UAAAK,mBAAA,CAAAH,CAAA,uBAAAV,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAE,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAI,CAAA,MAAAC,CAAA,GAAAX,CAAA,QAAAY,CAAA,OAAAC,CAAA,KAAAF,CAAA,KAAAb,CAAA,KAAAgB,CAAA,EAAApB,CAAA,EAAAqB,CAAA,EAAAC,CAAA,EAAAN,CAAA,EAAAM,CAAA,CAAAC,IAAA,CAAAvB,CAAA,MAAAsB,CAAA,WAAAA,EAAArB,CAAA,EAAAC,CAAA,WAAAM,CAAA,GAAAP,CAAA,EAAAQ,CAAA,MAAAG,CAAA,GAAAZ,CAAA,EAAAmB,CAAA,CAAAf,CAAA,GAAAF,CAAA,EAAAmB,CAAA,gBAAAC,EAAApB,CAAA,EAAAE,CAAA,SAAAK,CAAA,GAAAP,CAAA,EAAAU,CAAA,GAAAR,CAAA,EAAAH,CAAA,OAAAiB,CAAA,IAAAF,CAAA,KAAAV,CAAA,IAAAL,CAAA,GAAAgB,CAAA,CAAAO,MAAA,EAAAvB,CAAA,UAAAK,CAAA,EAAAE,CAAA,GAAAS,CAAA,CAAAhB,CAAA,GAAAqB,CAAA,GAAAH,CAAA,CAAAF,CAAA,EAAAQ,CAAA,GAAAjB,CAAA,KAAAN,CAAA,QAAAI,CAAA,GAAAmB,CAAA,KAAArB,CAAA,MAAAQ,CAAA,GAAAJ,CAAA,EAAAC,CAAA,GAAAD,CAAA,YAAAC,CAAA,WAAAD,CAAA,MAAAA,CAAA,MAAAR,CAAA,IAAAQ,CAAA,OAAAc,CAAA,MAAAhB,CAAA,GAAAJ,CAAA,QAAAoB,CAAA,GAAAd,CAAA,QAAAC,CAAA,MAAAU,CAAA,CAAAC,CAAA,GAAAhB,CAAA,EAAAe,CAAA,CAAAf,CAAA,GAAAI,CAAA,OAAAc,CAAA,GAAAG,CAAA,KAAAnB,CAAA,GAAAJ,CAAA,QAAAM,CAAA,MAAAJ,CAAA,IAAAA,CAAA,GAAAqB,CAAA,MAAAjB,CAAA,MAAAN,CAAA,EAAAM,CAAA,MAAAJ,CAAA,EAAAe,CAAA,CAAAf,CAAA,GAAAqB,CAAA,EAAAhB,CAAA,cAAAH,CAAA,IAAAJ,CAAA,aAAAmB,CAAA,QAAAH,CAAA,OAAAd,CAAA,qBAAAE,CAAA,EAAAW,CAAA,EAAAQ,CAAA,QAAAT,CAAA,YAAAU,SAAA,uCAAAR,CAAA,UAAAD,CAAA,IAAAK,CAAA,CAAAL,CAAA,EAAAQ,CAAA,GAAAhB,CAAA,GAAAQ,CAAA,EAAAL,CAAA,GAAAa,CAAA,GAAAxB,CAAA,GAAAQ,CAAA,OAAAT,CAAA,GAAAY,CAAA,MAAAM,CAAA,KAAAV,CAAA,KAAAC,CAAA,GAAAA,CAAA,QAAAA,CAAA,SAAAU,CAAA,CAAAf,CAAA,QAAAkB,CAAA,CAAAb,CAAA,EAAAG,CAAA,KAAAO,CAAA,CAAAf,CAAA,GAAAQ,CAAA,GAAAO,CAAA,CAAAC,CAAA,GAAAR,CAAA,aAAAI,CAAA,MAAAR,CAAA,QAAAC,CAAA,KAAAH,CAAA,YAAAL,CAAA,GAAAO,CAAA,CAAAF,CAAA,WAAAL,CAAA,GAAAA,CAAA,CAAA0B,IAAA,CAAAnB,CAAA,EAAAI,CAAA,UAAAc,SAAA,2CAAAzB,CAAA,CAAA2B,IAAA,SAAA3B,CAAA,EAAAW,CAAA,GAAAX,CAAA,CAAA4B,KAAA,EAAApB,CAAA,SAAAA,CAAA,oBAAAA,CAAA,KAAAR,CAAA,GAAAO,CAAA,CAAAsB,MAAA,KAAA7B,CAAA,CAAA0B,IAAA,CAAAnB,CAAA,GAAAC,CAAA,SAAAG,CAAA,GAAAc,SAAA,uCAAApB,CAAA,gBAAAG,CAAA,OAAAD,CAAA,GAAAR,CAAA,cAAAC,CAAA,IAAAiB,CAAA,GAAAC,CAAA,CAAAf,CAAA,QAAAQ,CAAA,GAAAV,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,EAAAe,CAAA,OAAAE,CAAA,kBAAApB,CAAA,IAAAO,CAAA,GAAAR,CAAA,EAAAS,CAAA,MAAAG,CAAA,GAAAX,CAAA,cAAAe,CAAA,mBAAAa,KAAA,EAAA5B,CAAA,EAAA2B,IAAA,EAAAV,CAAA,SAAAhB,CAAA,EAAAI,CAAA,EAAAE,CAAA,QAAAI,CAAA,QAAAS,CAAA,gBAAAV,UAAA,cAAAoB,kBAAA,cAAAC,2BAAA,KAAA/B,CAAA,GAAAY,MAAA,CAAAoB,cAAA,MAAAxB,CAAA,MAAAL,CAAA,IAAAH,CAAA,CAAAA,CAAA,IAAAG,CAAA,SAAAW,mBAAA,CAAAd,CAAA,OAAAG,CAAA,iCAAAH,CAAA,GAAAW,CAAA,GAAAoB,0BAAA,CAAAtB,SAAA,GAAAC,SAAA,CAAAD,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAL,CAAA,YAAAO,EAAAhB,CAAA,WAAAa,MAAA,CAAAqB,cAAA,GAAArB,MAAA,CAAAqB,cAAA,CAAAlC,CAAA,EAAAgC,0BAAA,KAAAhC,CAAA,CAAAmC,SAAA,GAAAH,0BAAA,EAAAjB,mBAAA,CAAAf,CAAA,EAAAM,CAAA,yBAAAN,CAAA,CAAAU,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAF,CAAA,GAAAZ,CAAA,WAAA+B,iBAAA,CAAArB,SAAA,GAAAsB,0BAAA,EAAAjB,mBAAA,CAAAH,CAAA,iBAAAoB,0BAAA,GAAAjB,mBAAA,CAAAiB,0BAAA,iBAAAD,iBAAA,GAAAA,iBAAA,CAAAK,WAAA,wBAAArB,mBAAA,CAAAiB,0BAAA,EAAA1B,CAAA,wBAAAS,mBAAA,CAAAH,CAAA,GAAAG,mBAAA,CAAAH,CAAA,EAAAN,CAAA,gBAAAS,mBAAA,CAAAH,CAAA,EAAAR,CAAA,iCAAAW,mBAAA,CAAAH,CAAA,8DAAAyB,YAAA,YAAAA,aAAA,aAAAC,CAAA,EAAA9B,CAAA,EAAA+B,CAAA,EAAAvB,CAAA;AAAA,SAAAD,oBAAAf,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAO,CAAA,GAAAK,MAAA,CAAA2B,cAAA,QAAAhC,CAAA,uBAAAR,CAAA,IAAAQ,CAAA,QAAAO,mBAAA,YAAA0B,mBAAAzC,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAC,CAAA,EAAAM,CAAA,GAAAA,CAAA,CAAAR,CAAA,EAAAE,CAAA,IAAA2B,KAAA,EAAAzB,CAAA,EAAAsC,UAAA,GAAAzC,CAAA,EAAA0C,YAAA,GAAA1C,CAAA,EAAA2C,QAAA,GAAA3C,CAAA,MAAAD,CAAA,CAAAE,CAAA,IAAAE,CAAA,YAAAE,CAAA,YAAAA,EAAAJ,CAAA,EAAAE,CAAA,IAAAW,mBAAA,CAAAf,CAAA,EAAAE,CAAA,YAAAF,CAAA,gBAAA6C,OAAA,CAAA3C,CAAA,EAAAE,CAAA,EAAAJ,CAAA,UAAAM,CAAA,aAAAA,CAAA,cAAAA,CAAA,oBAAAS,mBAAA,CAAAf,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA;AAAA,SAAA6C,mBAAA1C,CAAA,EAAAH,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAI,CAAA,EAAAe,CAAA,EAAAZ,CAAA,cAAAD,CAAA,GAAAJ,CAAA,CAAAiB,CAAA,EAAAZ,CAAA,GAAAG,CAAA,GAAAJ,CAAA,CAAAqB,KAAA,WAAAzB,CAAA,gBAAAJ,CAAA,CAAAI,CAAA,KAAAI,CAAA,CAAAoB,IAAA,GAAA3B,CAAA,CAAAW,CAAA,IAAAmC,OAAA,CAAAC,OAAA,CAAApC,CAAA,EAAAqC,IAAA,CAAA/C,CAAA,EAAAI,CAAA;AAAA,SAAA4C,kBAAA9C,CAAA,6BAAAH,CAAA,SAAAD,CAAA,GAAAmD,SAAA,aAAAJ,OAAA,WAAA7C,CAAA,EAAAI,CAAA,QAAAe,CAAA,GAAAjB,CAAA,CAAAgD,KAAA,CAAAnD,CAAA,EAAAD,CAAA,YAAAqD,MAAAjD,CAAA,IAAA0C,kBAAA,CAAAzB,CAAA,EAAAnB,CAAA,EAAAI,CAAA,EAAA+C,KAAA,EAAAC,MAAA,UAAAlD,CAAA,cAAAkD,OAAAlD,CAAA,IAAA0C,kBAAA,CAAAzB,CAAA,EAAAnB,CAAA,EAAAI,CAAA,EAAA+C,KAAA,EAAAC,MAAA,WAAAlD,CAAA,KAAAiD,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,SAAS,EAAEC,eAAe,QAAQ,KAAK;AACrD,SAASC,SAAS,EAAEC,QAAQ,QAAQ,YAAY;AAChD,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,SAAS,QAAQ,MAAM;AAChC,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,WAAW,EAAEC,SAAS,QAAQ,eAAe;AACtD,SAASC,iBAAiB,EAAEC,cAAc,EAAEC,kBAAkB,QAAQ,qBAAqB;AAV3F,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAa,CAAC;;;;;IAWrC,IAAMC,MAAM,GAAGZ,SAAS,CAAC,CAAC;IAC1B,IAAMa,KAAK,GAAGZ,QAAQ,CAAC,CAAC;IAExB,IAAMa,YAAY,GAAGjB,GAAG,CAAC,EAAE,CAAC;IAC5B,IAAMkB,OAAO,GAAGlB,GAAG,CAAC,KAAK,CAAC;IAE1B,IAAMmB,aAAa,GAAGnB,GAAG,CAAC,CAAC,CAAC;IAC5B,IAAIoB,aAAa,GAAG,IAAI;IACxB;IACA;;IAEA,IAAMC,SAAS,GAAGrB,GAAG,CAAC,EAAE,CAAC;IACzB,IAAMsB,iBAAiB,GAAGtB,GAAG,CAAC,EAAE,CAAC;IACjC,IAAMuB,iBAAiB,GAAGvB,GAAG,CAAC,EAAE,CAAC;IACjC,IAAMwB,wBAAwB,GAAGxB,GAAG,CAAC,KAAK,CAAC;IAE3C,IAAMyB,uBAAuB,GAAGzB,GAAG,CAAC,KAAK,CAAC;IAC1C,IAAM0B,gBAAgB,GAAG1B,GAAG,CAAC,CAC3B;MACE2B,GAAG,EAAEC,OAAO,CAAC,oCAAoC,CAAC;MAClDC,KAAK,EAAE,wBAAwB;MAC/BC,EAAE,EAAE;IACN,CAAC,EACD;MACEH,GAAG,EAAEC,OAAO,CAAC,8BAA8B,CAAC;MAC5CC,KAAK,EAAE,mBAAmB;MAC1BC,EAAE,EAAE;IACN,CAAC,EACD;MACEH,GAAG,EAAEC,OAAO,CAAC,gCAAgC,CAAC;MAC9CC,KAAK,EAAE,kBAAkB;MACzBC,EAAE,EAAE;IACN,CAAC,CACF,CAAC;IACF,IAAMC,OAAO,GAAG/B,GAAG,CAAC,CAAC,CAAC;IACtB,IAAIgC,OAAO,GAAG,IAAI;IAClB,IAAMC,aAAa,GAAGjC,GAAG,CAAC,CAAC,CAAC;IAC5B,IAAMkC,WAAW,GAAGlC,GAAG,CAAC,CAAC,CAAC;IAE1B,IAAMmC,+BAA+B,GAAGnC,GAAG,CAAC,KAAK,CAAC;IAClD,IAAMoC,4BAA4B,GAAGpC,GAAG,CAAC,EAAE,CAAC;IAE5C,IAAMqC,mBAAmB,GAAGrC,GAAG,CAAC,EAAE,CAAC;IACnC,IAAMsC,WAAW,GAAGtC,GAAG,CAAC,IAAI,CAAC;IAE7BC,SAAS,CAAC,YAAM;MACdsC,QAAQ,CAACV,KAAK,GAAG,MAAM;MACvB;MACAW,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC;IACFtC,eAAe,CAAC,YAAM;MACpBuC,gBAAgB,CAAC,CAAC;MAClBC,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC;IACF;IACA,IAAMF,aAAa;MAAA,IAAAG,KAAA,GAAAjD,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAAG,SAAA6D,QAAA;QAAA,IAAAC,eAAA,EAAAC,OAAA,EAAAC,UAAA,EAAAC,WAAA,EAAAC,GAAA,EAAAC,EAAA;QAAA,OAAArE,YAAA,GAAAC,CAAA,WAAAqE,QAAA;UAAA,kBAAAA,QAAA,CAAAvG,CAAA;YAAA;cACpB0F,WAAW,CAACjE,KAAK,GAAG,IAAI;;cAExB;cACMwE,eAAe,GAAGnC,iBAAiB,CAAC,CAAC;cACrCoC,OAAO,GAAGlC,kBAAkB,CAAC,CAAC;cAEpCwC,OAAO,CAACC,GAAG,CAAC,WAAWR,eAAe,GAAG,QAAQ,GAAG,OAAO,KAAKC,OAAO,GAAG,CAAC;cAAA,IAEtED,eAAe;gBAAAM,QAAA,CAAAvG,CAAA;gBAAA;cAAA;cAClBwG,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;cAC/BN,UAAU,GAAGO,cAAc,CAACC,OAAO,CAAC,aAAa,CAAC;cAAA,IACnDR,UAAU;gBAAAI,QAAA,CAAAvG,CAAA;gBAAA;cAAA;cACPoG,WAAW,GAAGrC,cAAc,CAAC,CAAC;cACpC6C,SAAS,CAACR,WAAW,CAAC;cAAAG,QAAA,CAAAvG,CAAA;cAAA;YAAA;cAAAuG,QAAA,CAAAvG,CAAA;cAAA,OAEhB6G,cAAc,CAAC,CAAC;YAAA;cACtBnB,WAAW,CAACjE,KAAK,GAAG,KAAK;YAAA;cAAA,OAAA8E,QAAA,CAAAtF,CAAA;YAAA;cAAAsF,QAAA,CAAA1F,CAAA;cAAA0F,QAAA,CAAAvG,CAAA;cAAA,OAOTmD,GAAG,CAAC2D,QAAQ,CAAC,CAAC,CAAC,CAAC;YAAA;cAA5BT,GAAG,GAAAE,QAAA,CAAAvF,CAAA;cACT,IAAIqF,GAAG,CAACU,IAAI,CAACA,IAAI,EAAE;gBACjBC,YAAY,CAACX,GAAG,CAACU,IAAI,CAACA,IAAI,CAACE,UAAU,CAAC;cACxC,CAAC,MAAM;gBACLT,OAAO,CAACU,IAAI,CAAC,yBAAyB,CAAC;gBACvCxB,WAAW,CAACjE,KAAK,GAAG,KAAK;gBACzB0F,UAAU,CAAC,YAAM;kBACfzD,SAAS,CAAC,yBAAyB,CAAC;kBACpC,IAAM0C,WAAW,GAAGrC,cAAc,CAAC,CAAC;kBACpC6C,SAAS,CAACR,WAAW,CAAC;gBACxB,CAAC,EAAE,GAAG,CAAC;cACT;cAACG,QAAA,CAAAvG,CAAA;cAAA;YAAA;cAAAuG,QAAA,CAAA1F,CAAA;cAAAyF,EAAA,GAAAC,QAAA,CAAAvF,CAAA;cAEDwF,OAAO,CAACY,KAAK,CAAC,iBAAiB,EAAAd,EAAO,CAAC;cACvCZ,WAAW,CAACjE,KAAK,GAAG,KAAK;cACzB0F,UAAU,CAAC,YAAM;gBACfzD,SAAS,CAAC,eAAe,CAAC;gBAC1B,IAAM0C,WAAW,GAAGrC,cAAc,CAAC,CAAC;gBACpC6C,SAAS,CAACR,WAAW,CAAC;cACxB,CAAC,EAAE,GAAG,CAAC;YAAA;cAAA,OAAAG,QAAA,CAAAtF,CAAA;UAAA;QAAA,GAAA+E,OAAA;MAAA,CAEV;MAAA,gBA7CKJ,aAAaA,CAAA;QAAA,OAAAG,KAAA,CAAA/C,KAAA,OAAAD,SAAA;MAAA;IAAA,GA6ClB;IACD;IACA,IAAMiE,YAAY;MAAA,IAAAK,KAAA,GAAAvE,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAAG,SAAAmF,SAAOL,UAAU;QAAA,IAAAb,WAAA,EAAAmB,aAAA;QAAA,OAAAtF,YAAA,GAAAC,CAAA,WAAAsF,SAAA;UAAA,kBAAAA,SAAA,CAAAxH,CAAA;YAAA;cAAA,IAE/B8D,iBAAiB,CAAC,CAAC;gBAAA0D,SAAA,CAAAxH,CAAA;gBAAA;cAAA;cACtBwG,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;cAC3CL,WAAW,GAAGrC,cAAc,CAAC,CAAC;cACpC6C,SAAS,CAACR,WAAW,CAAC;cAAA,OAAAoB,SAAA,CAAAvG,CAAA;YAAA;cAIxB,IAAI;gBACF2C,WAAW,CAAC;kBACV;kBACA,OAAO,EAAE,kCAAkC;kBAAE;kBAC7C,aAAa,EAAE,CAAC,eAAe,CAAC;kBAAE;kBAClC,YAAY,EAAEqD,UAAU,CAAC;gBAC3B,CAAC,EAAE,UAAUQ,MAAM,EAAE;kBACnB,IAAIA,MAAM,CAACC,IAAI,KAAK,CAAC,EAAE;oBACrBlB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;oBAC3BkB,YAAY,CAACF,MAAM,CAACV,IAAI,CAACa,QAAQ,CAAC;kBACpC,CAAC,MAAM;oBACLpB,OAAO,CAACY,KAAK,CAAC,eAAe,EAAEK,MAAM,CAACI,OAAO,CAAC;oBAC9C;oBACArB,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;oBAC9B/C,SAAS,CAAC,eAAe,CAAC;oBAC1B,IAAM0C,YAAW,GAAGrC,cAAc,CAAC,CAAC;oBACpC6C,SAAS,CAACR,YAAW,CAAC;kBACxB;gBACF,CAAC,CAAC;cACJ,CAAC,CAAC,OAAOgB,KAAK,EAAE;gBACdZ,OAAO,CAACY,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;gBACxCZ,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;gBACvC/C,SAAS,CAAC,eAAe,CAAC;gBACpB0C,aAAW,GAAGrC,cAAc,CAAC,CAAC;gBACpC6C,SAAS,CAACR,aAAW,CAAC;cACxB;YAAC;cAAA,OAAAoB,SAAA,CAAAvG,CAAA;UAAA;QAAA,GAAAqG,QAAA;MAAA,CACF;MAAA,gBAnCKN,YAAYA,CAAAc,EAAA;QAAA,OAAAT,KAAA,CAAArE,KAAA,OAAAD,SAAA;MAAA;IAAA,GAmCjB;IACD;IACA,IAAM4E,YAAY;MAAA,IAAAI,KAAA,GAAAjF,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAAG,SAAA6F,SAAON,IAAI;QAAA,IAAArB,GAAA,EAAA4B,OAAA;QAAA,OAAAhG,YAAA,GAAAC,CAAA,WAAAgG,SAAA;UAAA,kBAAAA,SAAA,CAAAlI,CAAA;YAAA;cAAAkI,SAAA,CAAAlI,CAAA;cAAA,OACZmD,GAAG,CAAC8E,OAAO,CAAC;gBAAEL,QAAQ,EAAEF;cAAK,CAAC,CAAC;YAAA;cAA3CrB,GAAG,GAAA6B,SAAA,CAAAlH,CAAA;cACTwF,OAAO,CAACC,GAAG,CAAC,SAAS,GAAG0B,IAAI,CAACC,SAAS,CAAC/B,GAAG,CAAC,CAAC;cACxC4B,OAAO,GAAG5B,GAAG,CAACU,IAAI,CAACA,IAAI,CAACsB,QAAQ;cACpCzB,SAAS,CAACqB,OAAO,CAAC;cAClBK,YAAY,CAACC,OAAO,CAAC,SAAS,EAAEJ,IAAI,CAACC,SAAS,CAACH,OAAO,CAAC,CAAC;YAAA;cAAA,OAAAC,SAAA,CAAAjH,CAAA;UAAA;QAAA,GAAA+G,QAAA;MAAA,CACzD;MAAA,gBANKL,YAAYA,CAAAa,GAAA;QAAA,OAAAT,KAAA,CAAA/E,KAAA,OAAAD,SAAA;MAAA;IAAA,GAMjB;IACD;IACA,IAAM6D,SAAS;MAAA,IAAA6B,KAAA,GAAA3F,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAAG,SAAAuG,SAAOC,IAAI;QAAA,IAAAC,gBAAA,EAAA7B,IAAA,EAAA8B,GAAA;QAAA,OAAA5G,YAAA,GAAAC,CAAA,WAAA4G,SAAA;UAAA,kBAAAA,SAAA,CAAA9I,CAAA;YAAA;cAAA,IACtB2I,IAAI,CAACI,QAAQ;gBAAAD,SAAA,CAAA9I,CAAA;gBAAA;cAAA;cAChB0F,WAAW,CAACjE,KAAK,GAAG,KAAK;cACzBiC,SAAS,CAAC,YAAY,CAAC;cACvByD,UAAU,CAAC,YAAM;gBACf6B,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAC;cACvB,CAAC,EAAE,GAAG,CAAC;cAAA,OAAAJ,SAAA,CAAA7H,CAAA;YAAA;cAAA6H,SAAA,CAAAjI,CAAA;cAAAiI,SAAA,CAAA9I,CAAA;cAAA,OAIgBmD,GAAG,CAACgG,KAAK,CAAC;gBAC/BC,UAAU,EAAE,cAAc;gBAC1BL,QAAQ,EAAEJ,IAAI,CAACI,QAAQ;gBACvBM,MAAM,EAAEV,IAAI,CAACW;cACf,CAAC,EAAE;gBACDC,OAAO,EAAE;kBACPC,aAAa,EAAE;gBACjB;cACF,CAAC,CAAC;YAAA;cAAAZ,gBAAA,GAAAE,SAAA,CAAA9H,CAAA;cARM+F,IAAI,GAAA6B,gBAAA,CAAJ7B,IAAI;cASZL,cAAc,CAAC6B,OAAO,CAAC,OAAO,EAAExB,IAAI,CAAC0C,KAAK,CAAC;cAC3C/C,cAAc,CAAC6B,OAAO,CAAC,eAAe,EAAE,UAAUxB,IAAI,CAAC2C,YAAY,CAACjI,KAAK,EAAE,CAAC;cAC5EiF,cAAc,CAAC6B,OAAO,CAAC,SAAS,EAAExB,IAAI,CAAC4C,UAAU,CAAC;cAClDjD,cAAc,CAAC6B,OAAO,CAAC,YAAY,EAAExB,IAAI,CAAC2C,YAAY,CAACE,UAAU,CAAC;cAClElD,cAAc,CAAC6B,OAAO,CAAC,QAAQ,EAAExB,IAAI,CAAC8C,uBAAuB,GAAG,CAAC,GAAG,CAAC,CAAC;cACtEnE,WAAW,CAACjE,KAAK,GAAG,KAAK,EAAC;cAAAqH,SAAA,CAAA9I,CAAA;cAAA,OACpB2D,KAAK,CAACmG,QAAQ,CAAC,WAAW,CAAC;YAAA;cAAAhB,SAAA,CAAA9I,CAAA;cAAA,OAE3B6G,cAAc,CAAC,CAAC;YAAA;cAAAiC,SAAA,CAAA9I,CAAA;cAAA;YAAA;cAAA8I,SAAA,CAAAjI,CAAA;cAAAgI,GAAA,GAAAC,SAAA,CAAA9H,CAAA;cAEtB0E,WAAW,CAACjE,KAAK,GAAG,KAAK,EAAC;cAC1BiC,SAAS,CAACmF,GAAA,CAAMhB,OAAO,IAAI,MAAM,CAAC;YAAA;cAAA,OAAAiB,SAAA,CAAA7H,CAAA;UAAA;QAAA,GAAAyH,QAAA;MAAA,CAErC;MAAA,gBAhCK9B,SAASA,CAAAmD,GAAA;QAAA,OAAAtB,KAAA,CAAAzF,KAAA,OAAAD,SAAA;MAAA;IAAA,GAgCd;IACD;IACA,IAAM8D,cAAc;MAAA,IAAAmD,KAAA,GAAAlH,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAAG,SAAA8H,SAAA;QAAA,IAAAC,MAAA,EAAAC,qBAAA,EAAApD,IAAA;QAAA,OAAA9E,YAAA,GAAAC,CAAA,WAAAkI,SAAA;UAAA,kBAAAA,SAAA,CAAApK,CAAA;YAAA;cACrBsE,OAAO,CAAC7C,KAAK,GAAG,IAAI;cACdyI,MAAM,GAAG;gBACbG,WAAW,EAAE,CAAC,CAAC;gBACfC,MAAM,EAAE,CAAC;gBACTC,QAAQ,EAAE,CAAC;gBACXC,OAAO,EAAE,EAAE;gBACXC,KAAK,EAAE;kBACLC,QAAQ,EAAEtG,KAAK,CAACqG,KAAK,CAACC,QAAQ,IAAI,qBAAqB;kBACvDC,QAAQ,EAAE,CAAC;kBACXC,QAAQ,EAAE;gBACZ,CAAC;gBACDC,MAAM,EAAE,CAAC;kBAAEH,QAAQ,EAAE,0BAA0B;kBAAEI,SAAS,EAAE,IAAI;kBAAErJ,KAAK,EAAE;gBAAI,CAAC,CAAC;gBAC/EsJ,OAAO,EAAE;cACX,CAAC;cAAAX,SAAA,CAAApK,CAAA;cAAA,OACsBmD,GAAG,CAAC6H,eAAe,CAACd,MAAM,CAAC;YAAA;cAAAC,qBAAA,GAAAC,SAAA,CAAApJ,CAAA;cAA1C+F,IAAI,GAAAoD,qBAAA,CAAJpD,IAAI;cACZ1C,YAAY,CAAC5C,KAAK,GAAGsF,IAAI;cACzBzC,OAAO,CAAC7C,KAAK,GAAG,KAAK;cACrBwJ,iBAAiB,CAAC,CAAC;cACnBC,SAAS,CAAC,CAAC;YAAA;cAAA,OAAAd,SAAA,CAAAnJ,CAAA;UAAA;QAAA,GAAAgJ,QAAA;MAAA,CACZ;MAAA,gBApBKpD,cAAcA,CAAA;QAAA,OAAAmD,KAAA,CAAAhH,KAAA,OAAAD,SAAA;MAAA;IAAA,GAoBnB;IACD;IACA,IAAMmI,SAAS;MAAA,IAAAC,KAAA,GAAArI,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAAG,SAAAiJ,SAAA;QAAA,IAAAC,qBAAA,EAAAtE,IAAA;QAAA,OAAA9E,YAAA,GAAAC,CAAA,WAAAoJ,SAAA;UAAA,kBAAAA,SAAA,CAAAtL,CAAA;YAAA;cAAAsL,SAAA,CAAAtL,CAAA;cAAA,OACOmD,GAAG,CAACoI,oBAAoB,CAAC;gBAAEC,KAAK,EAAE,CAAC,eAAe;cAAE,CAAC,CAAC;YAAA;cAAAH,qBAAA,GAAAC,SAAA,CAAAtK,CAAA;cAArE+F,IAAI,GAAAsE,qBAAA,CAAJtE,IAAI;cACZtC,SAAS,CAAChD,KAAK,GAAGsF,IAAI,CAAC0E,aAAa;cACpCC,sBAAsB,CAAC,CAAC;YAAA;cAAA,OAAAJ,SAAA,CAAArK,CAAA;UAAA;QAAA,GAAAmK,QAAA;MAAA,CACzB;MAAA,gBAJKF,SAASA,CAAA;QAAA,OAAAC,KAAA,CAAAnI,KAAA,OAAAD,SAAA;MAAA;IAAA,GAId;IACD;IACA,IAAM2I,sBAAsB;MAAA,IAAAC,KAAA,GAAA7I,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAAG,SAAAyJ,SAAOrB,QAAQ;QAAA,IAAAL,MAAA,EAAA2B,qBAAA,EAAA9E,IAAA;QAAA,OAAA9E,YAAA,GAAAC,CAAA,WAAA4J,SAAA;UAAA,kBAAAA,SAAA,CAAA9L,CAAA;YAAA;cAC5C,IAAI,CAACuK,QAAQ,EAAE;gBACb3F,wBAAwB,CAACnD,KAAK,GAAG,IAAI;cACvC;cACMyI,MAAM,GAAG;gBACbI,MAAM,EAAE,CAAC;gBACTC,QAAQ,EAAEA,QAAQ,IAAI,CAAC;gBACvBwB,IAAI,EAAE,IAAI;gBACV1B,WAAW,EAAE,CAAC;cAChB,CAAC;cACD,IAAIE,QAAQ,EAAE;gBACZL,MAAM,CAACG,WAAW,CAAC2B,UAAU,GAAG,GAAG;cACrC,CAAC,MAAM;gBACL9B,MAAM,CAACG,WAAW,CAAC4B,QAAQ,GAAG,GAAG;cACnC;cAACH,SAAA,CAAA9L,CAAA;cAAA,OACsBmD,GAAG,CAAC+I,mBAAmB,CAAChC,MAAM,CAAC;YAAA;cAAA2B,qBAAA,GAAAC,SAAA,CAAA9K,CAAA;cAA9C+F,IAAI,GAAA8E,qBAAA,CAAJ9E,IAAI;cACZ,IAAIwD,QAAQ,EAAE;gBACZ,IAAIxD,IAAI,CAAC3F,MAAM,GAAG,CAAC,EAAE;kBACnBsD,iBAAiB,CAACjD,KAAK,GAAGsF,IAAI,CAAC,CAAC,CAAC,CAAC7B,EAAE;gBACtC,CAAC,MAAM;kBACLxB,SAAS,CAAC,WAAW,CAAC;gBACxB;cACF,CAAC,MAAM;gBACLiB,iBAAiB,CAAClD,KAAK,GAAGsF,IAAI;gBAC9BnC,wBAAwB,CAACnD,KAAK,GAAG,KAAK;gBACtC0K,mBAAmB,CAAC,CAAC;gBACrBC,2BAA2B,CAAC,CAAC;cAC/B;YAAC;cAAA,OAAAN,SAAA,CAAA7K,CAAA;UAAA;QAAA,GAAA2K,QAAA;MAAA,CACF;MAAA,gBA5BKF,sBAAsBA,CAAAW,GAAA;QAAA,OAAAV,KAAA,CAAA3I,KAAA,OAAAD,SAAA;MAAA;IAAA,GA4B3B;IACD;IACA,IAAMoJ,mBAAmB;MAAA,IAAAG,KAAA,GAAAxJ,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAAG,SAAAoK,SAAA;QAAA,OAAAtK,YAAA,GAAAC,CAAA,WAAAsK,SAAA;UAAA,kBAAAA,SAAA,CAAAxM,CAAA;YAAA;cAC1B6E,uBAAuB,CAACpD,KAAK,GAAG,KAAK;cACrCgL,eAAe,CAAC,CAAC;YAAA;cAAA,OAAAD,SAAA,CAAAvL,CAAA;UAAA;QAAA,GAAAsL,QAAA;MAAA,CAClB;MAAA,gBAHKJ,mBAAmBA,CAAA;QAAA,OAAAG,KAAA,CAAAtJ,KAAA,OAAAD,SAAA;MAAA;IAAA,GAGxB;IACD;IACA,IAAMqJ,2BAA2B;MAAA,IAAAM,KAAA,GAAA5J,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAAG,SAAAwK,SAAA;QAAA,IAAAC,qBAAA,EAAAlF,IAAA,EAAAX,IAAA,EAAA8F,GAAA,EAAAC,MAAA,EAAAC,aAAA;QAAA,OAAA9K,YAAA,GAAAC,CAAA,WAAA8K,SAAA;UAAA,kBAAAA,SAAA,CAAAhN,CAAA;YAAA;cAClCuF,+BAA+B,CAAC9D,KAAK,GAAG,IAAI;cAAAuL,SAAA,CAAAhN,CAAA;cAAA,OACfmD,GAAG,CAAC8J,kBAAkB,CAAC;gBAClD3C,MAAM,EAAE,CAAC;gBACTC,QAAQ,EAAE,CAAC;gBACX2C,aAAa,EAAE,CAAC;gBAChBzC,KAAK,EAAE;kBAAE0C,YAAY,EAAE;gBAAoB;cAC7C,CAAC,CAAC;YAAA;cAAAP,qBAAA,GAAAI,SAAA,CAAAhM,CAAA;cALM0G,IAAI,GAAAkF,qBAAA,CAAJlF,IAAI;cAAEX,IAAI,GAAA6F,qBAAA,CAAJ7F,IAAI;cAMlB,IAAIW,IAAI,KAAK,GAAG,EAAE;gBACVmF,GAAG,GAAG,IAAIO,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;gBAC1BP,MAAM,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;gBAC5BC,aAAa,GAAG,CAAChG,IAAI,IAAI,EAAE,EAAEuG,GAAG,CAAC,UAAAC,IAAI,EAAI;kBAC7C,IAAMC,KAAK,GAAG,IAAIJ,IAAI,CAACG,IAAI,CAACE,SAAS,CAAC,CAACJ,OAAO,CAAC,CAAC;kBAChD,IAAMK,GAAG,GAAG,IAAIN,IAAI,CAACG,IAAI,CAACI,OAAO,CAAC,CAACN,OAAO,CAAC,CAAC;kBAC5C,IAAIO,UAAU,GAAG,EAAE;kBACnB,IAAIC,IAAI,GAAG,CAAC;kBACZ,IAAIhB,GAAG,GAAGa,GAAG,EAAE;oBACbE,UAAU,GAAG,OAAO;oBACpBC,IAAI,GAAG,CAAC;kBACV,CAAC,MAAM,IAAIhB,GAAG,GAAGW,KAAK,EAAE;oBACtBI,UAAU,GAAG,SAAS;oBACtBC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,GAAG,GAAGb,GAAG,IAAIC,MAAM,CAAC;kBACzC,CAAC,MAAM;oBACLc,UAAU,GAAG,SAAS;oBACtBC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACP,KAAK,GAAGX,GAAG,IAAIC,MAAM,CAAC;kBAC3C;kBACA,IAAMkB,OAAO,GAAGnB,GAAG,GAAGW,KAAK,IAAIX,GAAG,GAAGa,GAAG,IAAIH,IAAI,CAACU,UAAU,KAAK,KAAK;kBACrE,OAAAC,aAAA,CAAAA,aAAA,KACKX,IAAI;oBACPK,UAAU;oBACVC,IAAI;oBACJG;kBAAO;gBAEX,CAAC,CAAC;gBACFxI,4BAA4B,CAAC/D,KAAK,GAAG+D,4BAA4B,CAAC/D,KAAK,CAAC0M,MAAM,CAACpB,aAAa,CAAC;gBAC7F,IAAIvH,4BAA4B,CAAC/D,KAAK,CAAC,CAAC,CAAC,EAAE;kBACzC+D,4BAA4B,CAAC/D,KAAK,CAAC,CAAC,CAAC,CAAC2M,OAAO,GAAGvK,SAAS,CAAC2B,4BAA4B,CAAC/D,KAAK,CAAC,CAAC,CAAC,CAAC2M,OAAO,CAAC;gBAC1G;gBACA7I,+BAA+B,CAAC9D,KAAK,GAAG,KAAK;gBAC7C4M,+BAA+B,CAAC,CAAC;cACnC;YAAC;cAAA,OAAArB,SAAA,CAAA/L,CAAA;UAAA;QAAA,GAAA0L,QAAA;MAAA,CACF;MAAA,gBAzCKP,2BAA2BA,CAAA;QAAA,OAAAM,KAAA,CAAA1J,KAAA,OAAAD,SAAA;MAAA;IAAA,GAyChC;IACD;IACA,IAAMuL,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,QAAQ,EAAK;MACtD,IAAIH,KAAK,EAAEI,aAAa,CAACJ,KAAK,CAAC;MAC/B,OAAOK,WAAW,CAAC,YAAM;QACvBJ,KAAK,CAAC/M,KAAK,GAAG,CAAC+M,KAAK,CAAC/M,KAAK,GAAG,CAAC,IAAIgN,IAAI,CAAChN,KAAK,CAACL,MAAM;MACrD,CAAC,EAAE,KAAK,CAAC;IACX,CAAC;IACD,IAAMyN,YAAY,GAAG,SAAfA,YAAYA,CAAIN,KAAK,EAAK;MAC9B,IAAIA,KAAK,EAAE;QACTI,aAAa,CAACJ,KAAK,CAAC;QACpB,OAAO,IAAI;MACb;MACA,OAAO,IAAI;IACb,CAAC;IACD,IAAMO,YAAY,GAAG,SAAfA,YAAYA,CAAIC,GAAG,EAAEP,KAAK,EAAED,KAAK,EAAEE,IAAI,EAAEC,QAAQ,EAAK;MAC1DH,KAAK,GAAGM,YAAY,CAACN,KAAK,CAAC;MAC3BC,KAAK,CAAC/M,KAAK,GAAGsN,GAAG;MACjB,OAAOT,aAAa,CAACC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,QAAQ,CAAC;IACpD,CAAC;IACD,IAAMM,YAAY,GAAG,SAAfA,YAAYA,CAAIR,KAAK,EAAEC,IAAI,EAAK;MACpCD,KAAK,CAAC/M,KAAK,GAAG,CAAC+M,KAAK,CAAC/M,KAAK,GAAG,CAAC,GAAGgN,IAAI,CAAChN,KAAK,CAACL,MAAM,IAAIqN,IAAI,CAAChN,KAAK,CAACL,MAAM;IACzE,CAAC;IACD,IAAM6N,YAAY,GAAG,SAAfA,YAAYA,CAAIT,KAAK,EAAEC,IAAI,EAAK;MACpCD,KAAK,CAAC/M,KAAK,GAAG,CAAC+M,KAAK,CAAC/M,KAAK,GAAG,CAAC,IAAIgN,IAAI,CAAChN,KAAK,CAACL,MAAM;IACrD,CAAC;IACD;IACA,IAAM8N,YAAY,GAAG,SAAfA,YAAYA,CAAItP,CAAC,EAAEuP,WAAW,EAAEC,SAAS,EAAK;MAClDD,WAAW,CAAC1N,KAAK,GAAG7B,CAAC,CAACyP,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;MACxCF,SAAS,CAAC3N,KAAK,GAAG7B,CAAC,CAACyP,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;IACxC,CAAC;IACD,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAI3P,CAAC,EAAEwP,SAAS,EAAK;MACpCA,SAAS,CAAC3N,KAAK,GAAG7B,CAAC,CAACyP,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;IACxC,CAAC;IACD,IAAME,UAAU,GAAG,SAAbA,UAAUA,CAAI5P,CAAC,EAAEuP,WAAW,EAAEC,SAAS,EAAEK,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAK;MACjF,IAAMC,MAAM,GAAGT,SAAS,CAAC3N,KAAK,GAAG0N,WAAW,CAAC1N,KAAK;MAClD,IAAIqM,IAAI,CAACgC,GAAG,CAACD,MAAM,CAAC,GAAG,EAAE,EAAE;QACzBJ,MAAM,CAAC,CAAC;QACR,IAAII,MAAM,GAAG,CAAC,EAAE;UACdH,MAAM,CAAC,CAAC;QACV,CAAC,MAAM;UACLC,MAAM,CAAC,CAAC;QACV;QACAC,OAAO,CAAC,CAAC;MACX;MACAT,WAAW,CAAC1N,KAAK,GAAG,CAAC;MACrB2N,SAAS,CAAC3N,KAAK,GAAG,CAAC;IACrB,CAAC;IACD;IACA,IAAMwJ,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;MAC9BzG,aAAa,GAAG8J,aAAa,CAAC9J,aAAa,EAAED,aAAa,EAAEF,YAAY,CAAC;IAC3E,CAAC;IACD,IAAMwB,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;MAC7BrB,aAAa,GAAGqK,YAAY,CAACrK,aAAa,CAAC;IAC7C,CAAC;IACD;IACA,IAAMuL,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,GAAG,EAAK;MAC/B7L,MAAM,CAAC8L,IAAI,CAAC;QAAEC,IAAI,EAAE,aAAa;QAAEzF,KAAK,EAAE;UAAEvF,EAAE,EAAE8K,GAAG,CAAC9K;QAAG;MAAE,CAAC,CAAC;IAC7D,CAAC;IACD;IACA,IAAMiL,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxBhM,MAAM,CAAC8L,IAAI,CAAC;QAAEC,IAAI,EAAE,UAAU;QAAEzF,KAAK,EAAE;UAAE2F,KAAK,EAAE;QAAI;MAAE,CAAC,CAAC;IAC1D,CAAC;IACD;IACA,IAAMC,YAAY;MAAA,IAAAC,KAAA,GAAAxN,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAAG,SAAAoO,SAAOC,KAAK;QAAA,OAAAvO,YAAA,GAAAC,CAAA,WAAAuO,SAAA;UAAA,kBAAAA,SAAA,CAAAzQ,CAAA;YAAA;cAAA,KAC3BwQ,KAAK;gBAAAC,SAAA,CAAAzQ,CAAA;gBAAA;cAAA;cACPmE,MAAM,CAAC8L,IAAI,CAAC;gBAAEC,IAAI,EAAE,0BAA0B;gBAAEzF,KAAK,EAAE;kBAAEvF,EAAE,EAAEsL,KAAK,CAACtL;gBAAG;cAAE,CAAC,CAAC;cAAAuL,SAAA,CAAAzQ,CAAA;cAAA;YAAA;cAAAyQ,SAAA,CAAAzQ,CAAA;cAAA,OAEpE0L,sBAAsB,CAAC,CAAC,CAAC;YAAA;cAC/B,IAAIhH,iBAAiB,CAACjD,KAAK,EAAE;gBAC3B0C,MAAM,CAAC8L,IAAI,CAAC;kBAAEC,IAAI,EAAE,0BAA0B;kBAAEzF,KAAK,EAAE;oBAAEvF,EAAE,EAAER,iBAAiB,CAACjD;kBAAM;gBAAE,CAAC,CAAC;cAC3F;YAAC;cAAA,OAAAgP,SAAA,CAAAxP,CAAA;UAAA;QAAA,GAAAsP,QAAA;MAAA,CAEJ;MAAA,gBATKF,YAAYA,CAAAK,GAAA;QAAA,OAAAJ,KAAA,CAAAtN,KAAA,OAAAD,SAAA;MAAA;IAAA,GASjB;IACD;IACA,IAAM4N,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxBxM,MAAM,CAAC8L,IAAI,CAAC;QAAEC,IAAI,EAAE;MAAsB,CAAC,CAAC;IAC9C,CAAC;IACD;IACA,IAAMU,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvBzM,MAAM,CAAC8L,IAAI,CAAC;QAAEC,IAAI,EAAE;MAAqB,CAAC,CAAC;IAC7C,CAAC;IACD;IACA,IAAMzD,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;MAC5BrH,OAAO,GAAGkJ,aAAa,CAAClJ,OAAO,EAAED,OAAO,EAAEL,gBAAgB,CAAC;IAC7D,CAAC;IACD,IAAMgB,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;MAC3BV,OAAO,GAAGyJ,YAAY,CAACzJ,OAAO,CAAC;IACjC,CAAC;IACD,IAAMyL,cAAc,GAAG,SAAjBA,cAAcA,CAAI9B,GAAG,EAAK;MAC9B3J,OAAO,GAAG0J,YAAY,CAACC,GAAG,EAAE5J,OAAO,EAAEC,OAAO,EAAEN,gBAAgB,CAAC;IACjE,CAAC;IACD,IAAMgM,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;MAC3B9B,YAAY,CAAC7J,OAAO,EAAEL,gBAAgB,CAAC;IACzC,CAAC;IACD,IAAMiM,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;MAC3B9B,YAAY,CAAC9J,OAAO,EAAEL,gBAAgB,CAAC;IACzC,CAAC;IACD,IAAMkM,cAAc,GAAG,SAAjBA,cAAcA,CAAIpR,CAAC,EAAK;MAC5BsP,YAAY,CAACtP,CAAC,EAAEyF,aAAa,EAAEC,WAAW,CAAC;IAC7C,CAAC;IACD,IAAM2L,aAAa,GAAG,SAAhBA,aAAaA,CAAIrR,CAAC,EAAK;MAC3B2P,WAAW,CAAC3P,CAAC,EAAE0F,WAAW,CAAC;IAC7B,CAAC;IACD,IAAM4L,YAAY,GAAG,SAAfA,YAAYA,CAAItR,CAAC,EAAK;MAC1B4P,UAAU,CAAC5P,CAAC,EAAEyF,aAAa,EAAEC,WAAW,EAAEQ,cAAc,EAAEiL,cAAc,EAAED,cAAc,EAAErE,eAAe,CAAC;IAC5G,CAAC;IACD;IACA,IAAM0E,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIX,KAAK,EAAK;MACnC;MACA;IAAA,CACD;IACD;IACA,IAAMY,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAA,EAAS;MACpC;IAAA,CACD;IACD;IACA,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAIb,KAAK,EAAK;MACjCrM,MAAM,CAAC8L,IAAI,CAAC;QAAEC,IAAI,EAAE,2BAA2B;QAAEzF,KAAK,EAAE;UAAEvF,EAAE,EAAEsL,KAAK,CAACtL;QAAG;MAAE,CAAC,CAAC;IAC7E,CAAC;IACD;IACA,IAAMoM,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MACzBtI,MAAM,CAACuI,IAAI,CAAC,8CAA8C,EAAE,QAAQ,CAAC;IACvE,CAAC;IACD;IACA,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxBrN,MAAM,CAAC8L,IAAI,CAAC;QAAEC,IAAI,EAAE;MAAkB,CAAC,CAAC;IAC1C,CAAC;IACD;IACA,IAAM7B,+BAA+B;MAAA,IAAAoD,MAAA,GAAA3O,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAAG,SAAAuP,SAAA;QAAA,IAAAxH,MAAA,EAAAyH,qBAAA,EAAA5K,IAAA,EAAAW,IAAA,EAAAkK,KAAA;QAAA,OAAA3P,YAAA,GAAAC,CAAA,WAAA2P,SAAA;UAAA,kBAAAA,SAAA,CAAA7R,CAAA;YAAA;cAChCkK,MAAM,GAAG;gBACbI,MAAM,EAAE,CAAC;gBACTC,QAAQ,EAAE,CAAC;gBACXE,KAAK,EAAE;kBACLqH,SAAS,EAAE,qBAAqB;kBAChCC,OAAO,EAAE;gBACX,CAAC;gBACDhH,OAAO,EAAE,yBAAyB;gBAClCF,MAAM,EAAE;cACV,CAAC;cAAAgH,SAAA,CAAA7R,CAAA;cAAA,OAC4BmD,GAAG,CAAC6O,gBAAgB,CAAC9H,MAAM,CAAC;YAAA;cAAAyH,qBAAA,GAAAE,SAAA,CAAA7Q,CAAA;cAAjD+F,IAAI,GAAA4K,qBAAA,CAAJ5K,IAAI;cAAEW,IAAI,GAAAiK,qBAAA,CAAJjK,IAAI;cAClB,IAAIA,IAAI,IAAI,GAAG,EAAE;gBACTkK,KAAK,GAAG,CACZ5M,OAAO,CAAC,yCAAyC,CAAC,EAClDA,OAAO,CAAC,yCAAyC,CAAC,CACnD;gBACDS,mBAAmB,CAAChE,KAAK,GAAG,CAACsF,IAAI,IAAI,EAAE,EAAEuG,GAAG,CAAC,UAACC,IAAI,EAAEwB,GAAG;kBAAA,OAAAb,aAAA,CAAAA,aAAA,KAClDX,IAAI;oBACP0E,IAAI,EAAEL,KAAK,CAAC7C,GAAG;kBAAC;gBAAA,CAChB,CAAC;cACL,CAAC,MAAM;gBACLrL,SAAS,CAAC,aAAa,CAAC;cAC1B;YAAC;cAAA,OAAAmO,SAAA,CAAA5Q,CAAA;UAAA;QAAA,GAAAyQ,QAAA;MAAA,CACF;MAAA,gBAxBKrD,+BAA+BA,CAAA;QAAA,OAAAoD,MAAA,CAAAzO,KAAA,OAAAD,SAAA;MAAA;IAAA,GAwBpC;IACD;IACA,IAAMmP,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAI1B,KAAK,EAAK;MACpCrM,MAAM,CAAC8L,IAAI,CAAC;QAAEC,IAAI,EAAE,gBAAgB;QAAEzF,KAAK,EAAE;UAAEvF,EAAE,EAAEsL,KAAK,CAACtL;QAAG;MAAE,CAAC,CAAC;IAClE,CAAC;IACD;IACA,IAAMiN,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAA,EAAS;MACrChO,MAAM,CAAC8L,IAAI,CAAC;QAAEC,IAAI,EAAE,aAAa;QAAEzF,KAAK,EAAE,CAAC;MAAE,CAAC,CAAC;IACjD,CAAC;IACD;IACA,IAAM2H,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA,EAAS;MACjCjO,MAAM,CAAC8L,IAAI,CAAC;QAAEC,IAAI,EAAE;MAAqB,CAAC,CAAC;IAC7C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}