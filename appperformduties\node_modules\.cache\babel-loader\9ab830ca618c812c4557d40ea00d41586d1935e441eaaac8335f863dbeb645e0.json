{"ast": null, "code": "import { ref, onMounted } from 'vue';\nimport { showToast } from 'vant';\n\n// 响应式数据\n\nvar __default__ = {\n  name: 'ArchiveVisitBooking'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var contactName = ref('');\n    var contactPhone = ref('');\n    var groupName = ref('');\n    var groupType = ref('');\n    var groupTypeText = ref('');\n    var showGroupTypePicker = ref(false);\n\n    // 文件上传相关\n    var attachmentFiles = ref([]);\n\n    // 团体性质选项\n    var groupTypeOptions = [{\n      text: '机关单位',\n      value: '机关单位'\n    }, {\n      text: '企事业单位',\n      value: '企事业单位'\n    }, {\n      text: '学校',\n      value: '学校'\n    }, {\n      text: '社会团体',\n      value: '社会团体'\n    }, {\n      text: '其他',\n      value: '其他'\n    }];\n\n    // 日期数据\n    var dateList = ref([{\n      day: '二',\n      date: '9-2',\n      status: '可预约',\n      active: false,\n      disabled: false\n    }, {\n      day: '三',\n      date: '9-3',\n      status: '可预约',\n      active: false,\n      disabled: false\n    }, {\n      day: '四',\n      date: '9-4',\n      status: '可预约',\n      active: false,\n      disabled: false\n    }, {\n      day: '五',\n      date: '9-5',\n      status: '可预约',\n      active: true,\n      disabled: false\n    }, {\n      day: '六',\n      date: '9-6',\n      status: '可预约',\n      active: false,\n      disabled: false\n    }, {\n      day: '日',\n      date: '9-7',\n      status: '可预约',\n      active: false,\n      disabled: false\n    }, {\n      day: '一',\n      date: '9-8',\n      status: '可预约',\n      active: false,\n      disabled: true\n    }]);\n\n    // 时段数据\n    var timeSlots = ref([{\n      id: 1,\n      time: '09:00-11:00',\n      status: '可预约',\n      count: 49,\n      active: false\n    }, {\n      id: 2,\n      time: '13:30-16:30',\n      status: '可预约',\n      count: 160,\n      active: true\n    }, {\n      id: 3,\n      time: '13:30-16:30',\n      status: '可预约',\n      count: 160,\n      active: true\n    }, {\n      id: 4,\n      time: '13:30-16:30',\n      status: '可预约',\n      count: 160,\n      active: true\n    }]);\n\n    // 方法\n    var selectDate = function selectDate(date) {\n      if (date.disabled) return;\n      dateList.value.forEach(function (item) {\n        return item.active = false;\n      });\n      date.active = true;\n    };\n    var selectTimeSlot = function selectTimeSlot(slot) {\n      timeSlots.value.forEach(function (item) {\n        return item.active = false;\n      });\n      slot.active = true;\n    };\n\n    // 团体性质选择确认\n    var onGroupTypeConfirm = function onGroupTypeConfirm(_ref2) {\n      var selectedOptions = _ref2.selectedOptions;\n      groupType.value = selectedOptions[0].value;\n      groupTypeText.value = selectedOptions[0].text;\n      showGroupTypePicker.value = false;\n    };\n\n    // 附件上传处理\n    var onAttachmentRead = function onAttachmentRead(file) {\n      console.log('附件上传:', file);\n\n      // 验证文件类型\n      if (!file.file.type.startsWith('image/')) {\n        showToast('请选择图片文件');\n        return;\n      }\n\n      // 验证文件大小 (限制为5MB)\n      var maxSize = 5 * 1024 * 1024;\n      if (file.file.size > maxSize) {\n        showToast('图片大小不能超过5MB');\n        return;\n      }\n\n      // 这里可以添加文件上传到服务器的逻辑\n      // 例如：uploadFile(file.file)\n      showToast('图片上传成功');\n    };\n\n    // 附件删除处理\n    var onAttachmentDelete = function onAttachmentDelete(file, detail) {\n      console.log('删除附件:', file, detail);\n      showToast('图片已删除');\n    };\n    onMounted(function () {\n      // 初始化逻辑\n    });\n    var __returned__ = {\n      contactName,\n      contactPhone,\n      groupName,\n      groupType,\n      groupTypeText,\n      showGroupTypePicker,\n      attachmentFiles,\n      groupTypeOptions,\n      dateList,\n      timeSlots,\n      selectDate,\n      selectTimeSlot,\n      onGroupTypeConfirm,\n      onAttachmentRead,\n      onAttachmentDelete,\n      ref,\n      onMounted,\n      get showToast() {\n        return showToast;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "onMounted", "showToast", "__default__", "name", "contactName", "contactPhone", "groupName", "groupType", "groupTypeText", "showGroupTypePicker", "attachmentFiles", "groupTypeOptions", "text", "value", "dateList", "day", "date", "status", "active", "disabled", "timeSlots", "id", "time", "count", "selectDate", "for<PERSON>ach", "item", "selectTimeSlot", "slot", "onGroupTypeConfirm", "_ref2", "selectedOptions", "onAttachmentRead", "file", "console", "log", "type", "startsWith", "maxSize", "size", "onAttachmentDelete", "detail"], "sources": ["D:/zy/xm/h5/i西安/appperformduties/src/views/ArchiveVisitBooking/ArchiveVisitBooking.vue"], "sourcesContent": ["<template>\r\n  <div class=\"archive-visit-booking\">\r\n    <!-- 选择入馆日期 -->\r\n    <div class=\"section\">\r\n      <div class=\"section-header flex_box flex_justify_between\">\r\n        <div class=\"flex_box flex_align_center\">\r\n          <img src=\"../../assets/img/icon_museum.png \" alt=\"\" class=\"icon_museum\">\r\n          <div class=\"section-title\">选择入馆日期</div>\r\n        </div>\r\n        <div class=\"year\">2025年</div>\r\n      </div>\r\n      <div class=\"date-grid flex_box\">\r\n        <div class=\"date-item\" v-for=\"date in dateList\" :key=\"date.day\"\r\n          :class=\"{ active: date.active, disabled: date.disabled }\" @click=\"selectDate(date)\">\r\n          <div class=\"day\">{{ date.day }}</div>\r\n          <div class=\"date\">{{ date.date }}</div>\r\n          <div class=\"status\">{{ date.status }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 选择入馆时段 -->\r\n    <div class=\"section\">\r\n      <div class=\"section-header flex_box flex_justify_between\">\r\n        <div class=\"flex_box flex_align_center\">\r\n          <img src=\"../../assets/img/icon_museum.png \" alt=\"\" class=\"icon_museum\">\r\n          <div class=\"section-title\">选择入馆时段</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"time-slots\">\r\n        <div class=\"time-slot\" v-for=\"slot in timeSlots\" :key=\"slot.id\" :class=\"{ active: slot.active }\"\r\n          @click=\"selectTimeSlot(slot)\">\r\n          <div class=\"time\">{{ slot.time }}</div>\r\n          <div class=\"capacity\">{{ slot.status }} ({{ slot.count }}人)</div>\r\n          <div v-if=\"slot.active\" class=\"active-indicator\"></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 预约人员信息 -->\r\n    <div class=\"section\">\r\n      <div class=\"section-header flex_box flex_justify_between\">\r\n        <div class=\"flex_box flex_align_center\">\r\n          <span class=\"section-line\"></span>\r\n          <div class=\"section-title\">预约人员信息</div>\r\n        </div>\r\n      </div>\r\n      <van-cell-group inset class=\"form-group\">\r\n        <!-- 联系人 -->\r\n        <van-field v-model=\"contactName\" label=\"联系人\" placeholder=\"请输入姓名\" required label-width=\"80px\"\r\n          class=\"form-field\" />\r\n\r\n        <!-- 联系方式 -->\r\n        <van-field v-model=\"contactPhone\" label=\"联系方式\" placeholder=\"请输入手机号\" required label-width=\"80px\"\r\n          class=\"form-field\" />\r\n\r\n        <!-- 团体名称 -->\r\n        <van-field v-model=\"groupName\" label=\"团体名称\" placeholder=\"请输入单位名称\" required label-width=\"80px\"\r\n          class=\"form-field\" />\r\n\r\n        <!-- 团体性质 -->\r\n        <van-field v-model=\"groupTypeText\" label=\"团体性质\" placeholder=\"请选择团体性质\" readonly clickable required\r\n          label-width=\"80px\" class=\"form-field\" @click=\"showGroupTypePicker = true\" />\r\n\r\n        <!-- 附件 -->\r\n        <van-field label=\"附件\" required label-width=\"80px\" class=\"form-field upload-field\">\r\n          <template #input>\r\n            <div class=\"upload-container\">\r\n              <van-uploader v-model=\"attachmentFiles\" :max-count=\"3\" :after-read=\"onAttachmentRead\"\r\n                :after-delete=\"onAttachmentDelete\" accept=\"image/*\" class=\"image-uploader\" multiple :preview-size=\"80\"\r\n                :upload-icon=\"'plus'\">\r\n                <template #default>\r\n                  <div class=\"upload-placeholder\">\r\n                    <van-icon name=\"plus\" size=\"20\" color=\"#ff6b35\" />\r\n                    <div class=\"upload-text\">上传图片</div>\r\n                  </div>\r\n                </template>\r\n              </van-uploader>\r\n            </div>\r\n          </template>\r\n        </van-field>\r\n      </van-cell-group>\r\n    </div>\r\n\r\n    <!-- 底部按钮 -->\r\n    <div class=\"submit-buttons\">\r\n      <div class=\"btn\">提交预约</div>\r\n      <div class=\"btn\">我的预约</div>\r\n    </div>\r\n\r\n    <!-- 团体性质选择弹窗 -->\r\n    <van-popup v-model:show=\"showGroupTypePicker\" position=\"bottom\">\r\n      <van-picker :columns=\"groupTypeOptions\" @confirm=\"onGroupTypeConfirm\" @cancel=\"showGroupTypePicker = false\" />\r\n    </van-popup>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default { name: 'ArchiveVisitBooking' }\r\n</script>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue'\r\nimport { showToast } from 'vant'\r\n\r\n// 响应式数据\r\nconst contactName = ref('')\r\nconst contactPhone = ref('')\r\nconst groupName = ref('')\r\nconst groupType = ref('')\r\nconst groupTypeText = ref('')\r\nconst showGroupTypePicker = ref(false)\r\n\r\n// 文件上传相关\r\nconst attachmentFiles = ref([])\r\n\r\n// 团体性质选项\r\nconst groupTypeOptions = [\r\n  { text: '机关单位', value: '机关单位' },\r\n  { text: '企事业单位', value: '企事业单位' },\r\n  { text: '学校', value: '学校' },\r\n  { text: '社会团体', value: '社会团体' },\r\n  { text: '其他', value: '其他' }\r\n]\r\n\r\n// 日期数据\r\nconst dateList = ref([\r\n  { day: '二', date: '9-2', status: '可预约', active: false, disabled: false },\r\n  { day: '三', date: '9-3', status: '可预约', active: false, disabled: false },\r\n  { day: '四', date: '9-4', status: '可预约', active: false, disabled: false },\r\n  { day: '五', date: '9-5', status: '可预约', active: true, disabled: false },\r\n  { day: '六', date: '9-6', status: '可预约', active: false, disabled: false },\r\n  { day: '日', date: '9-7', status: '可预约', active: false, disabled: false },\r\n  { day: '一', date: '9-8', status: '可预约', active: false, disabled: true }\r\n])\r\n\r\n// 时段数据\r\nconst timeSlots = ref([\r\n  { id: 1, time: '09:00-11:00', status: '可预约', count: 49, active: false },\r\n  { id: 2, time: '13:30-16:30', status: '可预约', count: 160, active: true },\r\n  { id: 3, time: '13:30-16:30', status: '可预约', count: 160, active: true },\r\n  { id: 4, time: '13:30-16:30', status: '可预约', count: 160, active: true }\r\n])\r\n\r\n// 方法\r\nconst selectDate = (date) => {\r\n  if (date.disabled) return\r\n  dateList.value.forEach(item => item.active = false)\r\n  date.active = true\r\n}\r\n\r\nconst selectTimeSlot = (slot) => {\r\n  timeSlots.value.forEach(item => item.active = false)\r\n  slot.active = true\r\n}\r\n\r\n// 团体性质选择确认\r\nconst onGroupTypeConfirm = ({ selectedOptions }) => {\r\n  groupType.value = selectedOptions[0].value\r\n  groupTypeText.value = selectedOptions[0].text\r\n  showGroupTypePicker.value = false\r\n}\r\n\r\n// 附件上传处理\r\nconst onAttachmentRead = (file) => {\r\n  console.log('附件上传:', file)\r\n\r\n  // 验证文件类型\r\n  if (!file.file.type.startsWith('image/')) {\r\n    showToast('请选择图片文件')\r\n    return\r\n  }\r\n\r\n  // 验证文件大小 (限制为5MB)\r\n  const maxSize = 5 * 1024 * 1024\r\n  if (file.file.size > maxSize) {\r\n    showToast('图片大小不能超过5MB')\r\n    return\r\n  }\r\n\r\n  // 这里可以添加文件上传到服务器的逻辑\r\n  // 例如：uploadFile(file.file)\r\n  showToast('图片上传成功')\r\n}\r\n\r\n// 附件删除处理\r\nconst onAttachmentDelete = (file, detail) => {\r\n  console.log('删除附件:', file, detail)\r\n  showToast('图片已删除')\r\n}\r\n\r\nonMounted(() => {\r\n  // 初始化逻辑\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.archive-visit-booking {\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow: auto;\r\n  background: #f9f9f9;\r\n  padding: 10px 10px;\r\n\r\n  .section {\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    margin-bottom: 12px;\r\n    padding: 10px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n    .section-header {\r\n      margin-bottom: 15px;\r\n\r\n      .icon_museum {\r\n        width: 16px;\r\n        height: 15px;\r\n        margin-right: 8px;\r\n      }\r\n\r\n      .section-line {\r\n        width: 4px;\r\n        height: 16px;\r\n        background: #A54E3B;\r\n        margin-right: 8px;\r\n      }\r\n\r\n      .section-title {\r\n        font-size: 15px;\r\n        font-weight: bold;\r\n        color: #333;\r\n      }\r\n\r\n      .year {\r\n        font-size: 14px;\r\n        color: #666;\r\n        font-weight: normal;\r\n      }\r\n    }\r\n\r\n    // 日期选择样式\r\n    .date-grid {\r\n      gap: 6px;\r\n      overflow-x: auto;\r\n\r\n      .date-item {\r\n        flex: 1;\r\n        max-width: 70px;\r\n        height: 70px;\r\n        background: #f8f8f8;\r\n        border-radius: 4px;\r\n        position: relative;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: space-evenly;\r\n\r\n        .day {\r\n          font-size: 12px;\r\n          color: #666;\r\n          margin-bottom: 3px;\r\n          line-height: 1;\r\n          font-weight: normal;\r\n        }\r\n\r\n        .date {\r\n          font-size: 13px;\r\n          font-weight: 500;\r\n          color: #333;\r\n          margin-bottom: 3px;\r\n          line-height: 1;\r\n        }\r\n\r\n        .status {\r\n          font-size: 10px;\r\n          color: #999;\r\n          line-height: 1;\r\n          font-weight: normal;\r\n        }\r\n\r\n        &.active {\r\n          background: #fff3e0;\r\n          border-color: #ff6b35;\r\n\r\n          .day,\r\n          .date {\r\n            color: #ff6b35;\r\n          }\r\n\r\n          .status {\r\n            color: #ff6b35;\r\n          }\r\n        }\r\n\r\n        &.disabled {\r\n          opacity: 0.4;\r\n          background: #f5f5f5;\r\n\r\n          .day,\r\n          .date,\r\n          .status {\r\n            color: #ccc;\r\n          }\r\n        }\r\n\r\n        &:hover:not(.disabled):not(.active) {\r\n          background: #f0f0f0;\r\n          border-color: #e0e0e0;\r\n        }\r\n      }\r\n    }\r\n\r\n    // 时段选择样式\r\n    .time-slots {\r\n      display: flex;\r\n      gap: 8px;\r\n\r\n      .time-slot {\r\n        flex: 1;\r\n        background: #f5f5f5;\r\n        border-radius: 6px;\r\n        padding: 12px 8px;\r\n        transition: all 0.3s ease;\r\n        border: 1px solid transparent;\r\n        position: relative;\r\n        text-align: center;\r\n\r\n        .time {\r\n          font-size: 14px;\r\n          font-weight: 500;\r\n          color: #333;\r\n          margin-bottom: 2px;\r\n          line-height: 1.2;\r\n        }\r\n\r\n        .capacity {\r\n          font-size: 12px;\r\n          color: #666;\r\n          line-height: 1.2;\r\n        }\r\n\r\n        .active-indicator {\r\n          position: absolute;\r\n          top: 4px;\r\n          right: 4px;\r\n          width: 6px;\r\n          height: 6px;\r\n          background: #ff4444;\r\n          border-radius: 50%;\r\n        }\r\n\r\n        &.active {\r\n          background: #fff3e0;\r\n          border-color: #ff9800;\r\n\r\n          .time {\r\n            color: #ff6b35;\r\n          }\r\n\r\n          .capacity {\r\n            color: #ff6b35;\r\n          }\r\n        }\r\n\r\n        &:hover:not(.active) {\r\n          background: #eeeeee;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Vant表单组件样式\r\n    .form-group {\r\n      margin: 12px 0;\r\n      border-radius: 8px;\r\n      overflow: hidden;\r\n\r\n      .van-cell {\r\n        padding: 12px 0;\r\n        background: white;\r\n\r\n        &::after {\r\n          border-bottom: 1px solid #f0f0f0;\r\n        }\r\n\r\n        &:last-child::after {\r\n          border-bottom: none;\r\n        }\r\n      }\r\n\r\n      :deep(.van-field__label) {\r\n        color: #333;\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n        width: 80px;\r\n        flex-shrink: 0;\r\n\r\n        &::before {\r\n          content: '*';\r\n          color: #ff4444;\r\n          margin-right: 4px;\r\n        }\r\n      }\r\n\r\n      :deep(.van-field__control) {\r\n        font-size: 14px;\r\n        color: #333;\r\n\r\n        &::placeholder {\r\n          color: #999;\r\n        }\r\n      }\r\n\r\n      // 上传字段样式\r\n      .upload-field {\r\n        :deep(.van-field__control) {\r\n          padding: 0;\r\n        }\r\n\r\n        .upload-container {\r\n          width: 100%;\r\n\r\n          .image-uploader {\r\n            :deep(.van-uploader__wrapper) {\r\n              display: flex;\r\n              flex-wrap: wrap;\r\n              gap: 16px;\r\n              align-items: flex-start;\r\n              padding: 8px 0;\r\n            }\r\n\r\n            :deep(.van-uploader__preview) {\r\n              position: relative;\r\n              border-radius: 12px;\r\n              overflow: hidden;\r\n              box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08);\r\n              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n\r\n              &:hover {\r\n                transform: translateY(-2px) scale(1.02);\r\n                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);\r\n              }\r\n\r\n              .van-uploader__preview-image {\r\n                border-radius: 12px;\r\n                object-fit: cover;\r\n                transition: transform 0.3s ease;\r\n\r\n                &:hover {\r\n                  transform: scale(1.05);\r\n                }\r\n              }\r\n\r\n              .van-uploader__preview-delete {\r\n                position: absolute;\r\n                top: 6px;\r\n                right: 6px;\r\n                width: 24px;\r\n                height: 24px;\r\n                background: linear-gradient(135deg, rgba(255, 59, 48, 0.9) 0%, rgba(255, 69, 58, 0.9) 100%);\r\n                border-radius: 50%;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                backdrop-filter: blur(10px);\r\n                border: 2px solid rgba(255, 255, 255, 0.3);\r\n                transition: all 0.3s ease;\r\n\r\n                &:hover {\r\n                  background: linear-gradient(135deg, rgba(255, 59, 48, 1) 0%, rgba(255, 69, 58, 1) 100%);\r\n                  transform: scale(1.1);\r\n                  box-shadow: 0 4px 12px rgba(255, 59, 48, 0.4);\r\n                }\r\n\r\n                .van-icon {\r\n                  color: white;\r\n                  font-size: 14px;\r\n                  font-weight: bold;\r\n                  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));\r\n                }\r\n              }\r\n            }\r\n\r\n            :deep(.van-uploader__upload) {\r\n              margin: 0;\r\n\r\n              .upload-placeholder {\r\n                width: 100px;\r\n                height: 100px;\r\n                border: 2px dashed #ff6b35;\r\n                border-radius: 12px;\r\n                background: linear-gradient(135deg, #fff9f5 0%, #fff3e0 100%);\r\n                display: flex;\r\n                flex-direction: column;\r\n                align-items: center;\r\n                justify-content: center;\r\n                cursor: pointer;\r\n                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n                position: relative;\r\n                overflow: hidden;\r\n\r\n                &::before {\r\n                  content: '';\r\n                  position: absolute;\r\n                  top: 0;\r\n                  left: 0;\r\n                  right: 0;\r\n                  bottom: 0;\r\n                  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);\r\n                  transform: translateX(-100%);\r\n                  transition: transform 0.6s ease;\r\n                }\r\n\r\n                .van-icon {\r\n                  margin-bottom: 6px;\r\n                  filter: drop-shadow(0 2px 4px rgba(255, 107, 53, 0.2));\r\n                }\r\n\r\n                .upload-text {\r\n                  font-size: 13px;\r\n                  color: #ff6b35;\r\n                  font-weight: 600;\r\n                  letter-spacing: 0.5px;\r\n                  text-shadow: 0 1px 2px rgba(255, 107, 53, 0.1);\r\n                }\r\n\r\n                &:hover {\r\n                  border-color: #ff4500;\r\n                  background: linear-gradient(135deg, #fff5f0 0%, #ffe8d6 100%);\r\n                  transform: translateY(-3px) scale(1.02);\r\n                  box-shadow: 0 8px 25px rgba(255, 107, 53, 0.25), 0 3px 10px rgba(0, 0, 0, 0.1);\r\n\r\n                  &::before {\r\n                    transform: translateX(100%);\r\n                  }\r\n\r\n                  .upload-text {\r\n                    color: #ff4500;\r\n                  }\r\n                }\r\n\r\n                &:active {\r\n                  transform: translateY(-1px) scale(1.01);\r\n                  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.2);\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .counter {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 16px;\r\n\r\n      .counter-btn {\r\n        width: 32px;\r\n        height: 32px;\r\n        border: 1px solid #ddd;\r\n        border-radius: 6px;\r\n        background: white;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        cursor: pointer;\r\n        font-size: 18px;\r\n        color: #666;\r\n        transition: all 0.3s ease;\r\n\r\n        &:hover {\r\n          border-color: #ff6b35;\r\n          color: #ff6b35;\r\n        }\r\n\r\n        &:active {\r\n          background: #f0f0f0;\r\n        }\r\n      }\r\n\r\n      .count {\r\n        font-size: 18px;\r\n        font-weight: 600;\r\n        color: #333;\r\n        min-width: 24px;\r\n        text-align: center;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 底部操作区域\r\n.submit-buttons {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  align-items: center;\r\n\r\n  .btn {\r\n    color: #ffffff;\r\n    background-color: #e1b97b;\r\n    font-weight: normal;\r\n    border-radius: 30px;\r\n    padding: 10px 0;\r\n    width: 46%;\r\n    text-align: center;\r\n    font-size: 14px;\r\n  }\r\n}\r\n</style>"], "mappings": "AAsGA,SAASA,GAAG,EAAEC,SAAS,QAAQ,KAAK;AACpC,SAASC,SAAS,QAAQ,MAAM;;AAEhC;;AAPA,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAsB,CAAC;;;;;IAQ9C,IAAMC,WAAW,GAAGL,GAAG,CAAC,EAAE,CAAC;IAC3B,IAAMM,YAAY,GAAGN,GAAG,CAAC,EAAE,CAAC;IAC5B,IAAMO,SAAS,GAAGP,GAAG,CAAC,EAAE,CAAC;IACzB,IAAMQ,SAAS,GAAGR,GAAG,CAAC,EAAE,CAAC;IACzB,IAAMS,aAAa,GAAGT,GAAG,CAAC,EAAE,CAAC;IAC7B,IAAMU,mBAAmB,GAAGV,GAAG,CAAC,KAAK,CAAC;;IAEtC;IACA,IAAMW,eAAe,GAAGX,GAAG,CAAC,EAAE,CAAC;;IAE/B;IACA,IAAMY,gBAAgB,GAAG,CACvB;MAAEC,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAO,CAAC,EAC/B;MAAED,IAAI,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAQ,CAAC,EACjC;MAAED,IAAI,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,EAC3B;MAAED,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAO,CAAC,EAC/B;MAAED,IAAI,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAC5B;;IAED;IACA,IAAMC,QAAQ,GAAGf,GAAG,CAAC,CACnB;MAAEgB,GAAG,EAAE,GAAG;MAAEC,IAAI,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,QAAQ,EAAE;IAAM,CAAC,EACxE;MAAEJ,GAAG,EAAE,GAAG;MAAEC,IAAI,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,QAAQ,EAAE;IAAM,CAAC,EACxE;MAAEJ,GAAG,EAAE,GAAG;MAAEC,IAAI,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,QAAQ,EAAE;IAAM,CAAC,EACxE;MAAEJ,GAAG,EAAE,GAAG;MAAEC,IAAI,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,MAAM,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAM,CAAC,EACvE;MAAEJ,GAAG,EAAE,GAAG;MAAEC,IAAI,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,QAAQ,EAAE;IAAM,CAAC,EACxE;MAAEJ,GAAG,EAAE,GAAG;MAAEC,IAAI,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,QAAQ,EAAE;IAAM,CAAC,EACxE;MAAEJ,GAAG,EAAE,GAAG;MAAEC,IAAI,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,QAAQ,EAAE;IAAK,CAAC,CACxE,CAAC;;IAEF;IACA,IAAMC,SAAS,GAAGrB,GAAG,CAAC,CACpB;MAAEsB,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,aAAa;MAAEL,MAAM,EAAE,KAAK;MAAEM,KAAK,EAAE,EAAE;MAAEL,MAAM,EAAE;IAAM,CAAC,EACvE;MAAEG,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,aAAa;MAAEL,MAAM,EAAE,KAAK;MAAEM,KAAK,EAAE,GAAG;MAAEL,MAAM,EAAE;IAAK,CAAC,EACvE;MAAEG,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,aAAa;MAAEL,MAAM,EAAE,KAAK;MAAEM,KAAK,EAAE,GAAG;MAAEL,MAAM,EAAE;IAAK,CAAC,EACvE;MAAEG,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,aAAa;MAAEL,MAAM,EAAE,KAAK;MAAEM,KAAK,EAAE,GAAG;MAAEL,MAAM,EAAE;IAAK,CAAC,CACxE,CAAC;;IAEF;IACA,IAAMM,UAAU,GAAG,SAAbA,UAAUA,CAAIR,IAAI,EAAK;MAC3B,IAAIA,IAAI,CAACG,QAAQ,EAAE;MACnBL,QAAQ,CAACD,KAAK,CAACY,OAAO,CAAC,UAAAC,IAAI;QAAA,OAAIA,IAAI,CAACR,MAAM,GAAG,KAAK;MAAA,EAAC;MACnDF,IAAI,CAACE,MAAM,GAAG,IAAI;IACpB,CAAC;IAED,IAAMS,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,IAAI,EAAK;MAC/BR,SAAS,CAACP,KAAK,CAACY,OAAO,CAAC,UAAAC,IAAI;QAAA,OAAIA,IAAI,CAACR,MAAM,GAAG,KAAK;MAAA,EAAC;MACpDU,IAAI,CAACV,MAAM,GAAG,IAAI;IACpB,CAAC;;IAED;IACA,IAAMW,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAAC,KAAA,EAA4B;MAAA,IAAtBC,eAAe,GAAAD,KAAA,CAAfC,eAAe;MAC3CxB,SAAS,CAACM,KAAK,GAAGkB,eAAe,CAAC,CAAC,CAAC,CAAClB,KAAK;MAC1CL,aAAa,CAACK,KAAK,GAAGkB,eAAe,CAAC,CAAC,CAAC,CAACnB,IAAI;MAC7CH,mBAAmB,CAACI,KAAK,GAAG,KAAK;IACnC,CAAC;;IAED;IACA,IAAMmB,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,IAAI,EAAK;MACjCC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEF,IAAI,CAAC;;MAE1B;MACA,IAAI,CAACA,IAAI,CAACA,IAAI,CAACG,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACxCpC,SAAS,CAAC,SAAS,CAAC;QACpB;MACF;;MAEA;MACA,IAAMqC,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI;MAC/B,IAAIL,IAAI,CAACA,IAAI,CAACM,IAAI,GAAGD,OAAO,EAAE;QAC5BrC,SAAS,CAAC,aAAa,CAAC;QACxB;MACF;;MAEA;MACA;MACAA,SAAS,CAAC,QAAQ,CAAC;IACrB,CAAC;;IAED;IACA,IAAMuC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIP,IAAI,EAAEQ,MAAM,EAAK;MAC3CP,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEF,IAAI,EAAEQ,MAAM,CAAC;MAClCxC,SAAS,CAAC,OAAO,CAAC;IACpB,CAAC;IAEDD,SAAS,CAAC,YAAM;MACd;IAAA,CACD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}