import { createApp } from 'vue'
import App from './App.vue'
import router, { history } from './router'
import store from './store'
import 'animate.css'
import 'amfe-flexible'
import Vant from 'vant'
import 'vant/lib/index.css'
import components from './components'
import './assets/scss/index.scss'
// import api from './api'
// import utils from './assets/js/utils'
import '@/public-path'



// const login = async () => {
//   //管理员 dc_admin
//   //   驻站代表：18528020132 张小小
//   // 梅溪湖基站工作人员：17872353958 王威
//   // 区县总站：岳麓管理员 - 15808026257
//   // 市级：长沙管理员 - 18800000000
//   // 省级：湖南省管理员 - 18822222222
//   const { data } = await api.login({
//     grant_type: 'password',
//     username: '18822222222',
//     password: utils.encrypt('pwd2222', new Date().getTime(), '1')
//   })
//   sessionStorage.setItem('token', data.token)
//   sessionStorage.setItem('refresh_token', `bearer ${data.refreshToken.value}`)
//   sessionStorage.setItem('expires', data.expires_in)
//   sessionStorage.setItem('expiration', data.refreshToken.expiration)
//   sessionStorage.setItem('verify', data.passwordElementMatchReg ? 1 : 0)
//   store.dispatch('loginUser')
// }
// if (!sessionStorage.getItem('token')) login()
// if (sessionStorage.getItem('token')) store.dispatch('loginUser')



// JSON名带http_的获取缓存
// localStorage.getItem(window.location.origin + name)
// JSON名不带http_的 
// localStorage.getItem(name)

// localStorage.getItem('loginToken') // 获取登录token
// localStorage.getItem('loginInfo') // 获取登录用户信息
// localStorage.getItem('web-token') // 获取web token
// localStorage.getItem('shuCangMenu') // 获取模块信息
// localStorage.getItem(window.location.origin + 'USER_INFO') // 获取用户信息
// localStorage.getItem(window.location.origin + 'LAST_VIEWS_PATH') // 获取首页路径
// localStorage.getItem(window.location.origin + 'SYS_ElSE_CONFIG') // 获取
// localStorage.getItem(window.location.origin + 'MENU') // 获取菜单缓存
// localStorage.getItem(window.location.origin + 'MENU_MODULE_ID') // 获取当前菜单缓存ID
// 独立运行
if (!window.__POWERED_BY_QIANKUN__) {
  const app = createApp(App)
  app.use(router).use(store).use(Vant).use(components).mount('#app')
}

let instance = null
let microHistory = null
let microRouter = null
// 生命周期 - 挂载前,在这里由主应用传过来的参数
export async function bootstrap () { }
// 生命周期 - 挂载后
export async function mount (props) {
  microHistory = history
  microRouter = router
  instance = createApp(App)
  instance.use(microRouter).use(store).use(Vant).use(components).mount(props.container ? props.container.querySelector('#app') : '#app')
}
// 生命周期 - 解除挂载
export async function unmount () {
  instance.unmount()
  instance._container.innerHTML = ''
  instance = null
  microRouter = null
  microHistory.destroy()
}
