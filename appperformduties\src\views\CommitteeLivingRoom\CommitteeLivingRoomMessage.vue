<template>
  <div class="CommitteeLivingRoomMessage">
    <van-form @submit="onSubmit">
      <van-cell-group inset>
        <van-field v-model="form.title" placeholder="请输入标题" :rules="[{ required: true, message: '请输入标题' }]">
          <template #label>
            <span class="required">*</span>标题
          </template>
        </van-field>
        <van-field v-model="form.name" label="姓名" readonly />
        <van-field v-model="form.phone" label="电话" readonly />
        <van-field v-model="form.email" label="邮箱" readonly />
        <van-field v-model="form.content" type="textarea" rows="5" show-word-limit maxlength="500" placeholder="请输入内容"
          :rules="[{ required: true, message: '请输入内容' }]">
          <template #label>
            <span class="required">*</span>写信内容
          </template>
          <template #button>
            <span class="word-count">已有字数{{ form.content.length }}</span>
          </template>
        </van-field>
      </van-cell-group>
      <div style="margin: 24px 16px 0 16px;">
        <van-button round block type="primary" native-type="submit">提交</van-button>
      </div>
    </van-form>
  </div>
</template>

<script setup>
import api from '@/api'
import { ref, onMounted, reactive } from 'vue'
import { showToast } from 'vant'
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()
const user = ref(JSON.parse(sessionStorage.getItem('user')) || {})
const form = reactive({
  title: '',
  name: user.value.userName || '',
  phone: user.value.mobile || '',
  email: user.value.email || '',
  content: ''
})
onMounted(() => {
  document.title = '留言'
})
const onSubmit = async () => {
  if (!form.title) {
    showToast('请输入标题')
    return
  }
  if (!form.content) {
    showToast('请输入内容')
    return
  }
  const res = await api.themeLetterAdd({
    form: {
      businessCode: 'schedule_reception',
      businessId: route.query.id,
      checkStatus: 0,
      content: form.content,
      emailShow: '',
      mobileShow: form.phone,
      receiverId: route.query.receiverId,
      senderId: user.value.id,
      title: form.title,
      userId: user.value.id,
      userName: user.value.userName,
      userNameShow: user.value.userName,
    }
  })
  if (res.code == 200) {
    showToast('提交成功')
    router.back()
  }
}
</script>

<style lang="scss">
.CommitteeLivingRoomMessage {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: #F4F5F9;

  .required {
    color: #f56c6c;
    margin-right: 2px;
  }

  .word-count {
    color: #999;
    font-size: 12px;
    float: right;
    margin-top: 4px;
  }
}
</style>
