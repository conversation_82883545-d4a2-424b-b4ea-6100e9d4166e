<template>
  <div class="NoticeDetails">
    <div class="info_title">{{ info.theme }}</div>
    <div class="flex_box flex_align_center" style="margin-top: 10px;">
      <div v-if="info.publishTime" class="info_time">{{ formatDate(info.publishTime) }}</div>
      <div class="flex_placeholder"></div>
      <div v-if="info.channelName" class="info_source">{{ info.channelName }}</div>
    </div>
    <div class="info_content" v-html="info.content"></div>
  </div>
</template>
<script>
export default { name: 'NoticeDetails' }
</script>
<script setup>
import api from '@/api'
import { ref, onMounted } from 'vue'
import { formatDate } from '@/assets/js/utils.js'
import { useRoute } from 'vue-router'
const route = useRoute()
const title = ref('')
const info = ref({})
onMounted(() => {
  if (title.value) {
    document.title = title.value
  }
  getInfo()
})

const getInfo = async () => {
  const res = await api.notificationInfo({ detailId: route.query.id })
  info.value = res.data
}
</script>
<style lang="scss">
.NoticeDetails {
  width: 100%;
  height: 100%;
  overflow: auto;
  padding: 12px 18px;

  .info_title {
    font-size: 16px;
    margin: 5px 0px;
    font-weight: 600;
    color: #000;
  }

  .info_time {
    color: #999;
    font-size: 13px;
    margin-right: 10px;
  }

  .info_source {
    color: #999;
    font-size: 13px;
    margin-right: 10px;
  }

  .info_content {
    line-height: 1.8;
    margin-top: 15px;

    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
