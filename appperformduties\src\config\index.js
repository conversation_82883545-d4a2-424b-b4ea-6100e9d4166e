const systemUrl = (url) => {
  if (!url) return ''
  const Expression = /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/ // eslint-disable-line
  const pathExp = new RegExp(Expression)
  return pathExp.test(url) ? url : `${window.location.protocol}//${window.location.hostname}:${url}`
}
const config = {
  // 后台接口配置
  API_URL: systemUrl(window?.globalConfig?.API_URL || process.env.VUE_APP_URL) || 'https://xaszzx.xa-cppcc.gov.cn:8131/lzt',
  // API_URL: systemUrl(window?.globalConfig?.API_URL || process.env.VUE_APP_URL) || 'https://xazx.cszysoft.com:8131/lzt',
  // API_URL: systemUrl(window?.globalConfig?.API_URL || process.env.VUE_APP_URL) || 'http://*************:84/service-ssp',
  // API_URL: '/api',
}
console.log(systemUrl(window?.globalConfig?.API_URL || process.env.VUE_APP_URL))
console.log(config)
export default config
